{"ast": null, "code": "/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeFloor = Math.floor,\n  nativeRandom = Math.random;\n\n/**\n * The base implementation of `_.random` without support for returning\n * floating-point numbers.\n *\n * @private\n * @param {number} lower The lower bound.\n * @param {number} upper The upper bound.\n * @returns {number} Returns the random number.\n */\nfunction baseRandom(lower, upper) {\n  return lower + nativeFloor(nativeRandom() * (upper - lower + 1));\n}\nexport default baseRandom;", "map": {"version": 3, "names": ["nativeFloor", "Math", "floor", "nativeRandom", "random", "baseRandom", "lower", "upper"], "sources": ["D:/WorkSpace/facai7/facai7_admin/node_modules/lodash-es/_baseRandom.js"], "sourcesContent": ["/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeFloor = Math.floor,\n    nativeRandom = Math.random;\n\n/**\n * The base implementation of `_.random` without support for returning\n * floating-point numbers.\n *\n * @private\n * @param {number} lower The lower bound.\n * @param {number} upper The upper bound.\n * @returns {number} Returns the random number.\n */\nfunction baseRandom(lower, upper) {\n  return lower + nativeFloor(nativeRandom() * (upper - lower + 1));\n}\n\nexport default baseRandom;\n"], "mappings": "AAAA;AACA,IAAIA,WAAW,GAAGC,IAAI,CAACC,KAAK;EACxBC,YAAY,GAAGF,IAAI,CAACG,MAAM;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,KAAK,EAAEC,KAAK,EAAE;EAChC,OAAOD,KAAK,GAAGN,WAAW,CAACG,YAAY,CAAC,CAAC,IAAII,KAAK,GAAGD,KAAK,GAAG,CAAC,CAAC,CAAC;AAClE;AAEA,eAAeD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}