{"ast": null, "code": "import baseKeys from './_baseKeys.js';\nimport getTag from './_getTag.js';\nimport isArrayLike from './isArrayLike.js';\nimport isString from './isString.js';\nimport stringSize from './_stringSize.js';\n\n/** `Object#toString` result references. */\nvar mapTag = '[object Map]',\n  setTag = '[object Set]';\n\n/**\n * Gets the size of `collection` by returning its length for array-like\n * values or the number of own enumerable string keyed properties for objects.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object|string} collection The collection to inspect.\n * @returns {number} Returns the collection size.\n * @example\n *\n * _.size([1, 2, 3]);\n * // => 3\n *\n * _.size({ 'a': 1, 'b': 2 });\n * // => 2\n *\n * _.size('pebbles');\n * // => 7\n */\nfunction size(collection) {\n  if (collection == null) {\n    return 0;\n  }\n  if (isArrayLike(collection)) {\n    return isString(collection) ? stringSize(collection) : collection.length;\n  }\n  var tag = getTag(collection);\n  if (tag == mapTag || tag == setTag) {\n    return collection.size;\n  }\n  return baseKeys(collection).length;\n}\nexport default size;", "map": {"version": 3, "names": ["baseKeys", "getTag", "isArrayLike", "isString", "stringSize", "mapTag", "setTag", "size", "collection", "length", "tag"], "sources": ["D:/WorkSpace/facai7/facai7_admin/node_modules/lodash-es/size.js"], "sourcesContent": ["import baseKeys from './_baseKeys.js';\nimport getTag from './_getTag.js';\nimport isArrayLike from './isArrayLike.js';\nimport isString from './isString.js';\nimport stringSize from './_stringSize.js';\n\n/** `Object#toString` result references. */\nvar mapTag = '[object Map]',\n    setTag = '[object Set]';\n\n/**\n * Gets the size of `collection` by returning its length for array-like\n * values or the number of own enumerable string keyed properties for objects.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object|string} collection The collection to inspect.\n * @returns {number} Returns the collection size.\n * @example\n *\n * _.size([1, 2, 3]);\n * // => 3\n *\n * _.size({ 'a': 1, 'b': 2 });\n * // => 2\n *\n * _.size('pebbles');\n * // => 7\n */\nfunction size(collection) {\n  if (collection == null) {\n    return 0;\n  }\n  if (isArrayLike(collection)) {\n    return isString(collection) ? stringSize(collection) : collection.length;\n  }\n  var tag = getTag(collection);\n  if (tag == mapTag || tag == setTag) {\n    return collection.size;\n  }\n  return baseKeys(collection).length;\n}\n\nexport default size;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,UAAU,MAAM,kBAAkB;;AAEzC;AACA,IAAIC,MAAM,GAAG,cAAc;EACvBC,MAAM,GAAG,cAAc;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,IAAIA,CAACC,UAAU,EAAE;EACxB,IAAIA,UAAU,IAAI,IAAI,EAAE;IACtB,OAAO,CAAC;EACV;EACA,IAAIN,WAAW,CAACM,UAAU,CAAC,EAAE;IAC3B,OAAOL,QAAQ,CAACK,UAAU,CAAC,GAAGJ,UAAU,CAACI,UAAU,CAAC,GAAGA,UAAU,CAACC,MAAM;EAC1E;EACA,IAAIC,GAAG,GAAGT,MAAM,CAACO,UAAU,CAAC;EAC5B,IAAIE,GAAG,IAAIL,MAAM,IAAIK,GAAG,IAAIJ,MAAM,EAAE;IAClC,OAAOE,UAAU,CAACD,IAAI;EACxB;EACA,OAAOP,QAAQ,CAACQ,UAAU,CAAC,CAACC,MAAM;AACpC;AAEA,eAAeF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}