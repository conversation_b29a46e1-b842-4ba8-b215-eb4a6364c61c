{"ast": null, "code": "import { getCurrentInstance, nextTick, onMounted, ref } from 'vue';\nimport { limitEnums, accoutActiveEnums, accountTypeEnums, userlistSfzStatusEnums } from '@/config/enums';\nexport default {\n  __name: 'userStateForm',\n  props: ['item'],\n  setup(__props, {\n    expose: __expose\n  }) {\n    const form = ref({\n      uid: '',\n      ban_buy: '',\n      ban_sigin: '',\n      ban_raffle: '',\n      ban_login: '',\n      ban_invite: '',\n      ban_recharge: '',\n      ban_withdraw: '',\n      ban_exchange: '',\n      sfz_status: '',\n      kick_out: '',\n      is_test: '',\n      is_valid_user: ''\n    });\n    const props = __props;\n    onMounted(() => {\n      nextTick(() => {\n        form.value = Object.assign(form.value, props.item);\n        getUserState();\n      });\n    });\n    const {\n      proxy\n    } = getCurrentInstance();\n    const getUserState = async () => {\n      const res = await proxy.$http({\n        method: 'get',\n        url: 'user/getUserState?id=' + form.value.id\n      });\n      if (res.code == 0) {\n        form.value = Object.assign(form.value, res.data);\n      }\n    };\n    __expose({\n      form\n    });\n    const __returned__ = {\n      form,\n      props,\n      proxy,\n      getUserState,\n      getCurrentInstance,\n      nextTick,\n      onMounted,\n      ref,\n      get limitEnums() {\n        return limitEnums;\n      },\n      get accoutActiveEnums() {\n        return accoutActiveEnums;\n      },\n      get accountTypeEnums() {\n        return accountTypeEnums;\n      },\n      get userlistSfzStatusEnums() {\n        return userlistSfzStatusEnums;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["getCurrentInstance", "nextTick", "onMounted", "ref", "limitEnums", "accoutActiveEnums", "accountTypeEnums", "userlistSfzStatusEnums", "form", "uid", "ban_buy", "ban_sigin", "ban_raffle", "ban_login", "ban_invite", "ban_recharge", "ban_withdraw", "ban_exchange", "sfz_status", "kick_out", "is_test", "is_valid_user", "props", "__props", "value", "Object", "assign", "item", "getUserState", "proxy", "res", "$http", "method", "url", "id", "code", "data", "__expose"], "sources": ["D:/WorkSpace/facai7/facai7_admin/src/views/userManage/components/userList/userStateForm.vue"], "sourcesContent": ["<template>\r\n    <el-form label-width=\"120px\" :inline=\"true\" :model=\"form\" class=\"demo-form-inline\">\r\n        <el-form-item label=\"用户名ID\" required >\r\n            <el-input v-model=\"form.uid\" disabled clearable  />\r\n        </el-form-item>\r\n        <el-form-item label=\"用户名\" required >\r\n            <el-input v-model=\"form.username\" disabled clearable  />\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"禁止购买\" required>\r\n            <el-select v-model=\"form.ban_buy\" placeholder=\"\" clearable>\r\n                <el-option v-for=\"item in limitEnums\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"禁止签到\" required>\r\n            <el-select v-model=\"form.ban_sigin\" placeholder=\"\" clearable>\r\n                <el-option v-for=\"item in limitEnums\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"禁止抽奖\" required>\r\n            <el-select v-model=\"form.ban_raffle\" placeholder=\"\" clearable>\r\n                <el-option v-for=\"item in limitEnums\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"禁止登入\" required>\r\n            <el-select v-model=\"form.ban_login\" placeholder=\"\" clearable>\r\n                <el-option v-for=\"item in limitEnums\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"禁止邀请\" required>\r\n            <el-select v-model=\"form.ban_invite\" placeholder=\"\" clearable>\r\n                <el-option v-for=\"item in limitEnums\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"禁止充值\" required>\r\n            <el-select v-model=\"form.ban_recharge\" placeholder=\"\" clearable>\r\n                <el-option v-for=\"item in limitEnums\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"禁止提款\" required>\r\n            <el-select v-model=\"form.ban_withdraw\" placeholder=\"\" clearable>\r\n                <el-option v-for=\"item in limitEnums\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"禁止商品兑换\" required>\r\n            <el-select v-model=\"form.ban_exchange\" placeholder=\"\" clearable>\r\n                <el-option v-for=\"item in limitEnums\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"身份证认证\" required>\r\n            <el-select v-model=\"form.sfz_status\" placeholder=\"\" clearable>\r\n                <el-option v-for=\"item in userlistSfzStatusEnums\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"踢用户下线\" required>\r\n            <el-select v-model=\"form.kick_out\" placeholder=\"\" clearable>\r\n                <el-option v-for=\"item in limitEnums\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"是否测试账号\" required>\r\n            <el-select v-model=\"form.is_test\" placeholder=\"\" clearable>\r\n                <el-option v-for=\"item in accountTypeEnums\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"是否有效账号\" required>\r\n            <el-select v-model=\"form.is_valid_user\" placeholder=\"\" clearable>\r\n                <el-option v-for=\"item in accoutActiveEnums\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n        </el-form-item>\r\n       \r\n    </el-form>\r\n</template>\r\n\r\n<script setup>\r\nimport { getCurrentInstance, nextTick, onMounted, ref } from 'vue'\r\nimport {limitEnums, accoutActiveEnums, accountTypeEnums, userlistSfzStatusEnums } from '@/config/enums'\r\n\r\nconst form = ref({\r\n    uid: '',\r\n    ban_buy: '',\r\n    ban_sigin: '',\r\n    ban_raffle: '',\r\n    ban_login: '',\r\n    ban_invite: '',\r\n    ban_recharge: '',\r\n    ban_withdraw: '',\r\n    ban_exchange: '',\r\n    sfz_status: '',\r\n    kick_out: '',\r\n    is_test: '',\r\n    is_valid_user: '',\r\n})\r\nconst props = defineProps(['item'])\r\n\r\nonMounted(() => {\r\n    nextTick(()=> {\r\n        form.value = Object.assign(form.value, props.item)\r\n        getUserState()\r\n    })\r\n})\r\n\r\nconst { proxy } = getCurrentInstance()\r\nconst getUserState = async () => {\r\n    const res = await proxy.$http({\r\n        method: 'get',\r\n        url: 'user/getUserState?id=' + form.value.id\r\n    })\r\n\r\n    if (res.code == 0) {\r\n        form.value = Object.assign(form.value, res.data)\r\n    }\r\n}\r\n\r\ndefineExpose({form})\r\n\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.demo-form-inline {\r\n    justify-content: flex-start;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n}\r\n.demo-form-inline .el-input {\r\n    --el-input-width: 260px;\r\n}\r\n\r\n.demo-form-inline .el-select {\r\n    --el-select-width: 260px;\r\n}\r\n/deep/ .el-radio-group {\r\n    width: 220px;\r\n}\r\n.form-title {\r\n text-align: left;\r\n padding-left: 30px;\r\n margin: 20px auto 10px;\r\n height: 44px;\r\n background-color: #f2f2f2;\r\n border-radius: 5px;\r\n line-height: 44px;\r\n}\r\n/deep/  .el-form-item {\r\n    align-items: flex-start;\r\n}\r\n</style>"], "mappings": "AA0EA,SAASA,kBAAkB,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,GAAG,QAAQ,KAAK;AAClE,SAAQC,UAAU,EAAEC,iBAAiB,EAAEC,gBAAgB,EAAEC,sBAAsB,QAAQ,gBAAgB;;;;;;;IAEvG,MAAMC,IAAI,GAAGL,GAAG,CAAC;MACbM,GAAG,EAAE,EAAE;MACPC,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE,EAAE;MACbC,UAAU,EAAE,EAAE;MACdC,SAAS,EAAE,EAAE;MACbC,UAAU,EAAE,EAAE;MACdC,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE,EAAE;MAChBC,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE,EAAE;MACXC,aAAa,EAAE;IACnB,CAAC,CAAC;IACF,MAAMC,KAAK,GAAGC,OAAqB;IAEnCrB,SAAS,CAAC,MAAM;MACZD,QAAQ,CAAC,MAAK;QACVO,IAAI,CAACgB,KAAK,GAAGC,MAAM,CAACC,MAAM,CAAClB,IAAI,CAACgB,KAAK,EAAEF,KAAK,CAACK,IAAI,CAAC;QAClDC,YAAY,CAAC,CAAC;MAClB,CAAC,CAAC;IACN,CAAC,CAAC;IAEF,MAAM;MAAEC;IAAM,CAAC,GAAG7B,kBAAkB,CAAC,CAAC;IACtC,MAAM4B,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC7B,MAAME,GAAG,GAAG,MAAMD,KAAK,CAACE,KAAK,CAAC;QAC1BC,MAAM,EAAE,KAAK;QACbC,GAAG,EAAE,uBAAuB,GAAGzB,IAAI,CAACgB,KAAK,CAACU;MAC9C,CAAC,CAAC;MAEF,IAAIJ,GAAG,CAACK,IAAI,IAAI,CAAC,EAAE;QACf3B,IAAI,CAACgB,KAAK,GAAGC,MAAM,CAACC,MAAM,CAAClB,IAAI,CAACgB,KAAK,EAAEM,GAAG,CAACM,IAAI,CAAC;MACpD;IACJ,CAAC;IAEDC,QAAY,CAAC;MAAC7B;IAAI,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}