{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Welsh [cy]\n//! author : <PERSON> : https://github.com/robgallen\n//! author : https://github.com/ryangreaves\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var cy = moment.defineLocale('cy', {\n    months: 'Ionawr_Chwefror_Mawrth_Ebrill_Mai_Mehefin_Gorffennaf_Awst_Medi_Hydref_Tachwedd_Rhagfyr'.split('_'),\n    monthsShort: 'Ion_Chwe_Maw_Ebr_Mai_Meh_Gor_Aws_Med_Hyd_Tach_Rhag'.split('_'),\n    weekdays: 'Dydd Sul_Dydd Llun_Dydd Mawrth_Dydd Mercher_Dydd Iau_Dydd Gwener_Dydd Sadwrn'.split('_'),\n    weekdaysShort: 'Sul_Llun_Maw_Mer_Iau_Gwe_Sad'.split('_'),\n    weekdaysMin: 'Su_Ll_Ma_Me_Ia_Gw_Sa'.split('_'),\n    weekdaysParseExact: true,\n    // time formats are the same as en-gb\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Heddiw am] LT',\n      nextDay: '[Yfory am] LT',\n      nextWeek: 'dddd [am] LT',\n      lastDay: '[Ddoe am] LT',\n      lastWeek: 'dddd [diwethaf am] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'mewn %s',\n      past: '%s yn ôl',\n      s: 'ychydig eiliadau',\n      ss: '%d eiliad',\n      m: 'munud',\n      mm: '%d munud',\n      h: 'awr',\n      hh: '%d awr',\n      d: 'diwrnod',\n      dd: '%d diwrnod',\n      M: 'mis',\n      MM: '%d mis',\n      y: 'blwyddyn',\n      yy: '%d flynedd'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(fed|ain|af|il|ydd|ed|eg)/,\n    // traditional ordinal numbers above 31 are not commonly used in colloquial Welsh\n    ordinal: function (number) {\n      var b = number,\n        output = '',\n        lookup = ['', 'af', 'il', 'ydd', 'ydd', 'ed', 'ed', 'ed', 'fed', 'fed', 'fed',\n        // 1af to 10fed\n        'eg', 'fed', 'eg', 'eg', 'fed', 'eg', 'eg', 'fed', 'eg', 'fed' // 11eg to 20fed\n        ];\n      if (b > 20) {\n        if (b === 40 || b === 50 || b === 60 || b === 80 || b === 100) {\n          output = 'fed'; // not 30ain, 70ain or 90ain\n        } else {\n          output = 'ain';\n        }\n      } else if (b > 0) {\n        output = lookup[b];\n      }\n      return number + output;\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return cy;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "cy", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "number", "b", "output", "lookup", "week", "dow", "doy"], "sources": ["D:/WorkSpace/facai7/facai7_admin/node_modules/moment/locale/cy.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Welsh [cy]\n//! author : <PERSON> : https://github.com/robgallen\n//! author : https://github.com/ryangreaves\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var cy = moment.defineLocale('cy', {\n        months: 'Ionawr_Chwefror_Mawrth_Ebrill_Mai_Mehefin_Gorffennaf_Awst_Medi_Hydref_Tachwedd_Rhagfyr'.split(\n            '_'\n        ),\n        monthsShort: 'Ion_Chwe_Maw_Ebr_Mai_Meh_Gor_Aws_Med_Hyd_Tach_Rhag'.split(\n            '_'\n        ),\n        weekdays:\n            'Dydd Sul_Dydd Llun_Dydd Mawrth_Dydd Mercher_Dydd Iau_Dydd Gwener_Dydd Sadwrn'.split(\n                '_'\n            ),\n        weekdaysShort: 'Sul_Llun_Maw_Mer_Iau_Gwe_Sad'.split('_'),\n        weekdaysMin: 'Su_Ll_Ma_Me_Ia_Gw_Sa'.split('_'),\n        weekdaysParseExact: true,\n        // time formats are the same as en-gb\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd, D MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[Heddiw am] LT',\n            nextDay: '[Yfory am] LT',\n            nextWeek: 'dddd [am] LT',\n            lastDay: '[Ddoe am] LT',\n            lastWeek: 'dddd [diwethaf am] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'mewn %s',\n            past: '%s yn ôl',\n            s: 'ychydig eiliadau',\n            ss: '%d eiliad',\n            m: 'munud',\n            mm: '%d munud',\n            h: 'awr',\n            hh: '%d awr',\n            d: 'diwrnod',\n            dd: '%d diwrnod',\n            M: 'mis',\n            MM: '%d mis',\n            y: 'blwyddyn',\n            yy: '%d flynedd',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}(fed|ain|af|il|ydd|ed|eg)/,\n        // traditional ordinal numbers above 31 are not commonly used in colloquial Welsh\n        ordinal: function (number) {\n            var b = number,\n                output = '',\n                lookup = [\n                    '',\n                    'af',\n                    'il',\n                    'ydd',\n                    'ydd',\n                    'ed',\n                    'ed',\n                    'ed',\n                    'fed',\n                    'fed',\n                    'fed', // 1af to 10fed\n                    'eg',\n                    'fed',\n                    'eg',\n                    'eg',\n                    'fed',\n                    'eg',\n                    'eg',\n                    'fed',\n                    'eg',\n                    'fed', // 11eg to 20fed\n                ];\n            if (b > 20) {\n                if (b === 40 || b === 50 || b === 60 || b === 80 || b === 100) {\n                    output = 'fed'; // not 30ain, 70ain or 90ain\n                } else {\n                    output = 'ain';\n                }\n            } else if (b > 0) {\n                output = lookup[b];\n            }\n            return number + output;\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return cy;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,wFAAwF,CAACC,KAAK,CAClG,GACJ,CAAC;IACDC,WAAW,EAAE,oDAAoD,CAACD,KAAK,CACnE,GACJ,CAAC;IACDE,QAAQ,EACJ,8EAA8E,CAACF,KAAK,CAChF,GACJ,CAAC;IACLG,aAAa,EAAE,8BAA8B,CAACH,KAAK,CAAC,GAAG,CAAC;IACxDI,WAAW,EAAE,sBAAsB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC9CK,kBAAkB,EAAE,IAAI;IACxB;IACAC,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,gBAAgB;MACzBC,OAAO,EAAE,eAAe;MACxBC,QAAQ,EAAE,cAAc;MACxBC,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE,uBAAuB;MACjCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,SAAS;MACjBC,IAAI,EAAE,UAAU;MAChBC,CAAC,EAAE,kBAAkB;MACrBC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,OAAO;MACVC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,KAAK;MACRC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,KAAK;MACRC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,kCAAkC;IAC1D;IACAC,OAAO,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACvB,IAAIC,CAAC,GAAGD,MAAM;QACVE,MAAM,GAAG,EAAE;QACXC,MAAM,GAAG,CACL,EAAE,EACF,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK;QAAE;QACP,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,KAAK,CAAE;QAAA,CACV;MACL,IAAIF,CAAC,GAAG,EAAE,EAAE;QACR,IAAIA,CAAC,KAAK,EAAE,IAAIA,CAAC,KAAK,EAAE,IAAIA,CAAC,KAAK,EAAE,IAAIA,CAAC,KAAK,EAAE,IAAIA,CAAC,KAAK,GAAG,EAAE;UAC3DC,MAAM,GAAG,KAAK,CAAC,CAAC;QACpB,CAAC,MAAM;UACHA,MAAM,GAAG,KAAK;QAClB;MACJ,CAAC,MAAM,IAAID,CAAC,GAAG,CAAC,EAAE;QACdC,MAAM,GAAGC,MAAM,CAACF,CAAC,CAAC;MACtB;MACA,OAAOD,MAAM,GAAGE,MAAM;IAC1B,CAAC;IACDE,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAO9C,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}