{"ast": null, "code": "import { nextTick, onMounted, ref } from 'vue';\nimport { withdrawTypeEnums, getLabelByVal } from '@/config/enums';\nexport default {\n  __name: 'editPop',\n  props: ['item'],\n  setup(__props, {\n    expose: __expose\n  }) {\n    const form = ref({\n      phone: \"\",\n      zhi: \"\",\n      qi: \"\",\n      wei: \"\",\n      lai: \"\",\n      chuang: \"\",\n      ling: \"\",\n      wu: \"\",\n      xian: \"\"\n    });\n    const props = __props;\n    onMounted(() => {\n      nextTick(() => {\n        form.value = Object.assign(form, props.item);\n      });\n    });\n    __expose({\n      form\n    });\n    const __returned__ = {\n      form,\n      props,\n      nextTick,\n      onMounted,\n      ref,\n      get withdrawTypeEnums() {\n        return withdrawTypeEnums;\n      },\n      get getLabelByVal() {\n        return getLabelByVal;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["nextTick", "onMounted", "ref", "withdrawTypeEnums", "getLabelByVal", "form", "phone", "zhi", "qi", "wei", "lai", "<PERSON>uang", "ling", "wu", "xian", "props", "__props", "value", "Object", "assign", "item", "__expose"], "sources": ["D:/WorkSpace/facai7/facai7_admin/src/views/projectManage/components/collectWords/editPop.vue"], "sourcesContent": ["<template>\r\n    <el-form label-width=\"180px\" :inline=\"true\" :model=\"form\" class=\"demo-form-inline\">\r\n        <el-form-item label=\"手机号\" required>\r\n            <el-input v-model=\"form.phone\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"智\" required>\r\n            <el-input v-model=\"form.zhi\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"启\" required>\r\n            <el-input v-model=\"form.qi\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"未\" required>\r\n            <el-input v-model=\"form.wei\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"来\" required>\r\n            <el-input v-model=\"form.lai\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"创\" required>\r\n            <el-input v-model=\"form.chuang\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"领\" required>\r\n            <el-input v-model=\"form.ling\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"无\" required>\r\n            <el-input v-model=\"form.wu\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"限\" required>\r\n            <el-input v-model=\"form.xian\" />\r\n        </el-form-item>\r\n        \r\n    </el-form>\r\n</template>\r\n\r\n<script setup>\r\nimport { nextTick, onMounted, ref } from 'vue'\r\nimport { withdrawTypeEnums, getLabelByVal } from '@/config/enums'\r\n\r\nconst form = ref({\r\n    phone: \"\",\r\n    zhi: \"\",\r\n    qi: \"\",\r\n    wei: \"\",\r\n    lai: \"\",\r\n    chuang: \"\",\r\n    ling: \"\",\r\n    wu: \"\",\r\n    xian: \"\",\r\n})\r\nconst props = defineProps(['item'])\r\n\r\nonMounted(() => {\r\n    nextTick(() => {\r\n        form.value = Object.assign(form, props.item)\r\n    })\r\n})\r\n\r\ndefineExpose({ form })\r\n\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.demo-form-inline {\r\n    justify-content: flex-start;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n}\r\n\r\n.demo-form-inline .el-input {\r\n    --el-input-width: 220px;\r\n}\r\n\r\n.demo-form-inline .el-select {\r\n    --el-select-width: 220px;\r\n}\r\n\r\n/deep/ .el-radio-group {\r\n    width: 220px;\r\n}\r\n\r\n.form-title {\r\n    text-align: left;\r\n    padding-left: 30px;\r\n    margin: 20px auto 10px;\r\n    height: 44px;\r\n    background-color: #f2f2f2;\r\n    border-radius: 5px;\r\n    line-height: 44px;\r\n    width: 100%;\r\n}\r\n\r\n/deep/ .el-form-item {\r\n    align-items: flex-start;\r\n}\r\n</style>"], "mappings": "AAkCA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,GAAG,QAAQ,KAAK;AAC9C,SAASC,iBAAiB,EAAEC,aAAa,QAAQ,gBAAgB;;;;;;;IAEjE,MAAMC,IAAI,GAAGH,GAAG,CAAC;MACbI,KAAK,EAAE,EAAE;MACTC,GAAG,EAAE,EAAE;MACPC,EAAE,EAAE,EAAE;MACNC,GAAG,EAAE,EAAE;MACPC,GAAG,EAAE,EAAE;MACPC,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE,EAAE;MACRC,EAAE,EAAE,EAAE;MACNC,IAAI,EAAE;IACV,CAAC,CAAC;IACF,MAAMC,KAAK,GAAGC,OAAqB;IAEnCf,SAAS,CAAC,MAAM;MACZD,QAAQ,CAAC,MAAM;QACXK,IAAI,CAACY,KAAK,GAAGC,MAAM,CAACC,MAAM,CAACd,IAAI,EAAEU,KAAK,CAACK,IAAI,CAAC;MAChD,CAAC,CAAC;IACN,CAAC,CAAC;IAEFC,QAAY,CAAC;MAAEhB;IAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}