{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Central Atlas Tamazight Latin [tzm-latn]\n//! author : <PERSON><PERSON> : https://github.com/abdelsaid\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var tzmLatn = moment.defineLocale('tzm-latn', {\n    months: 'innayr_brˤayrˤ_marˤsˤ_ibrir_mayyw_ywnyw_ywlywz_ɣwšt_šwtanbir_ktˤwbrˤ_nwwanbir_dwjnbir'.split('_'),\n    monthsShort: 'innayr_brˤayrˤ_marˤsˤ_ibrir_mayyw_ywnyw_ywlywz_ɣwšt_šwtanbir_ktˤwbrˤ_nwwanbir_dwjnbir'.split('_'),\n    weekdays: 'asamas_aynas_asinas_akras_akwas_asimwas_asiḍyas'.split('_'),\n    weekdaysShort: 'asamas_aynas_asinas_akras_akwas_asimwas_asiḍyas'.split('_'),\n    weekdaysMin: 'asamas_aynas_asinas_akras_akwas_asimwas_asiḍyas'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[asdkh g] LT',\n      nextDay: '[aska g] LT',\n      nextWeek: 'dddd [g] LT',\n      lastDay: '[assant g] LT',\n      lastWeek: 'dddd [g] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'dadkh s yan %s',\n      past: 'yan %s',\n      s: 'imik',\n      ss: '%d imik',\n      m: 'minuḍ',\n      mm: '%d minuḍ',\n      h: 'saɛa',\n      hh: '%d tassaɛin',\n      d: 'ass',\n      dd: '%d ossan',\n      M: 'ayowr',\n      MM: '%d iyyirn',\n      y: 'asgas',\n      yy: '%d isgasn'\n    },\n    week: {\n      dow: 6,\n      // Saturday is the first day of the week.\n      doy: 12 // The week that contains Jan 12th is the first week of the year.\n    }\n  });\n  return tzmLatn;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "tzmLatn", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "week", "dow", "doy"], "sources": ["D:/WorkSpace/facai7/facai7_admin/node_modules/moment/locale/tzm-latn.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Central Atlas Tamazight Latin [tzm-latn]\n//! author : <PERSON><PERSON> : https://github.com/abdelsaid\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var tzmLatn = moment.defineLocale('tzm-latn', {\n        months: 'innayr_brˤayrˤ_marˤsˤ_ibrir_mayyw_ywnyw_ywlywz_ɣwšt_šwtanbir_ktˤwbrˤ_nwwanbir_dwjnbir'.split(\n            '_'\n        ),\n        monthsShort:\n            'innayr_brˤayrˤ_marˤsˤ_ibrir_mayyw_ywnyw_ywlywz_ɣwšt_šwtanbir_ktˤwbrˤ_nwwanbir_dwjnbir'.split(\n                '_'\n            ),\n        weekdays: 'asamas_aynas_asinas_akras_akwas_asimwas_asiḍyas'.split('_'),\n        weekdaysShort: 'asamas_aynas_asinas_akras_akwas_asimwas_asiḍyas'.split('_'),\n        weekdaysMin: 'asamas_aynas_asinas_akras_akwas_asimwas_asiḍyas'.split('_'),\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd D MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[asdkh g] LT',\n            nextDay: '[aska g] LT',\n            nextWeek: 'dddd [g] LT',\n            lastDay: '[assant g] LT',\n            lastWeek: 'dddd [g] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'dadkh s yan %s',\n            past: 'yan %s',\n            s: 'imik',\n            ss: '%d imik',\n            m: 'minuḍ',\n            mm: '%d minuḍ',\n            h: 'saɛa',\n            hh: '%d tassaɛin',\n            d: 'ass',\n            dd: '%d ossan',\n            M: 'ayowr',\n            MM: '%d iyyirn',\n            y: 'asgas',\n            yy: '%d isgasn',\n        },\n        week: {\n            dow: 6, // Saturday is the first day of the week.\n            doy: 12, // The week that contains Jan 12th is the first week of the year.\n        },\n    });\n\n    return tzmLatn;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,OAAO,GAAGD,MAAM,CAACE,YAAY,CAAC,UAAU,EAAE;IAC1CC,MAAM,EAAE,uFAAuF,CAACC,KAAK,CACjG,GACJ,CAAC;IACDC,WAAW,EACP,uFAAuF,CAACD,KAAK,CACzF,GACJ,CAAC;IACLE,QAAQ,EAAE,iDAAiD,CAACF,KAAK,CAAC,GAAG,CAAC;IACtEG,aAAa,EAAE,iDAAiD,CAACH,KAAK,CAAC,GAAG,CAAC;IAC3EI,WAAW,EAAE,iDAAiD,CAACJ,KAAK,CAAC,GAAG,CAAC;IACzEK,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,cAAc;MACvBC,OAAO,EAAE,aAAa;MACtBC,QAAQ,EAAE,aAAa;MACvBC,OAAO,EAAE,eAAe;MACxBC,QAAQ,EAAE,aAAa;MACvBC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,gBAAgB;MACxBC,IAAI,EAAE,QAAQ;MACdC,CAAC,EAAE,MAAM;MACTC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,OAAO;MACVC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,MAAM;MACTC,EAAE,EAAE,aAAa;MACjBC,CAAC,EAAE,KAAK;MACRC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,OAAO;MACVC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,OAAO;MACVC,EAAE,EAAE;IACR,CAAC;IACDC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,EAAE,CAAE;IACb;EACJ,CAAC,CAAC;EAEF,OAAOvC,OAAO;AAElB,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}