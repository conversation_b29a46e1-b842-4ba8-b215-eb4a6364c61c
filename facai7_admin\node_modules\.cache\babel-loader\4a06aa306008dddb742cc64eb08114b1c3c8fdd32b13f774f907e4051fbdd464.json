{"ast": null, "code": "import baseInvoke from './_baseInvoke.js';\nimport baseRest from './_baseRest.js';\n\n/**\n * The opposite of `_.method`; this method creates a function that invokes\n * the method at a given path of `object`. Any additional arguments are\n * provided to the invoked method.\n *\n * @static\n * @memberOf _\n * @since 3.7.0\n * @category Util\n * @param {Object} object The object to query.\n * @param {...*} [args] The arguments to invoke the method with.\n * @returns {Function} Returns the new invoker function.\n * @example\n *\n * var array = _.times(3, _.constant),\n *     object = { 'a': array, 'b': array, 'c': array };\n *\n * _.map(['a[2]', 'c[0]'], _.methodOf(object));\n * // => [2, 0]\n *\n * _.map([['a', '2'], ['c', '0']], _.methodOf(object));\n * // => [2, 0]\n */\nvar methodOf = baseRest(function (object, args) {\n  return function (path) {\n    return baseInvoke(object, path, args);\n  };\n});\nexport default methodOf;", "map": {"version": 3, "names": ["baseInvoke", "baseRest", "methodOf", "object", "args", "path"], "sources": ["D:/WorkSpace/facai7/facai7_admin/node_modules/lodash-es/methodOf.js"], "sourcesContent": ["import baseInvoke from './_baseInvoke.js';\nimport baseRest from './_baseRest.js';\n\n/**\n * The opposite of `_.method`; this method creates a function that invokes\n * the method at a given path of `object`. Any additional arguments are\n * provided to the invoked method.\n *\n * @static\n * @memberOf _\n * @since 3.7.0\n * @category Util\n * @param {Object} object The object to query.\n * @param {...*} [args] The arguments to invoke the method with.\n * @returns {Function} Returns the new invoker function.\n * @example\n *\n * var array = _.times(3, _.constant),\n *     object = { 'a': array, 'b': array, 'c': array };\n *\n * _.map(['a[2]', 'c[0]'], _.methodOf(object));\n * // => [2, 0]\n *\n * _.map([['a', '2'], ['c', '0']], _.methodOf(object));\n * // => [2, 0]\n */\nvar methodOf = baseRest(function(object, args) {\n  return function(path) {\n    return baseInvoke(object, path, args);\n  };\n});\n\nexport default methodOf;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;AACzC,OAAOC,QAAQ,MAAM,gBAAgB;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,QAAQ,GAAGD,QAAQ,CAAC,UAASE,MAAM,EAAEC,IAAI,EAAE;EAC7C,OAAO,UAASC,IAAI,EAAE;IACpB,OAAOL,UAAU,CAACG,MAAM,EAAEE,IAAI,EAAED,IAAI,CAAC;EACvC,CAAC;AACH,CAAC,CAAC;AAEF,eAAeF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}