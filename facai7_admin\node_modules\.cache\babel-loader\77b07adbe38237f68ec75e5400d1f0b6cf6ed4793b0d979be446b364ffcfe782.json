{"ast": null, "code": "import { rolesEnums } from \"@/config/enums\";\nimport { onBeforeUnmount, nextTick, ref, shallowRef, onMounted, getCurrentInstance } from \"vue\";\nimport { getTokenAUTH } from \"@/utils/auth\";\nexport default {\n  __name: 'editPop',\n  props: [\"item\"],\n  setup(__props, {\n    expose: __expose\n  }) {\n    const form = ref({\n      title: \"\",\n      img: \"\",\n      url: \"\"\n    });\n    const props = __props;\n    const {\n      proxy\n    } = getCurrentInstance();\n    const headers = ref({});\n    onMounted(() => {\n      headers.value['Accept-Token'] = getTokenAUTH();\n      nextTick(() => {\n        form.value = Object.assign(form.value, props.item);\n      });\n    });\n    const successUpload = res => {\n      form.value.img = res.data.url;\n    };\n    const handleErr = err => {\n      if (err.status == 320) {\n        form.value.img = JSON.parse(err.message).data.url;\n      }\n    };\n\n    // 编辑器实例，必须用 shallowRef\n    const editorRef = shallowRef();\n\n    // 内容 HTML\n    const valueHtml = ref(\"<p>hello</p>\");\n    const mode = ref(\"default\");\n    const toolbarConfig = {};\n    const editorConfig = {\n      placeholder: \"请输入内容...\",\n      MENU_CONF: {\n        uploadImage: {\n          fieldName: \"file\",\n          maxFileSize: 10 * 1024 * 1024,\n          // 10M\n          server: proxy.BASE_API_URL + \"index/uploadX\",\n          headers: {\n            \"Accept-Token\": getTokenAUTH()\n          },\n          customInsert(res, insertFn) {\n            const url = proxy.IMG_BASE_URL + res.data.url;\n            const alt = res.data.alt;\n            const href = res.data.href;\n            insertFn(url, alt, href);\n          }\n        }\n      }\n    };\n\n    // 组件销毁时，也及时销毁编辑器\n    onBeforeUnmount(() => {\n      const editor = editorRef.value;\n      if (editor == null) return;\n      editor.destroy();\n    });\n    const handleCreated = editor => {\n      editorRef.value = editor; // 记录 editor 实例，重要！\n    };\n    __expose({\n      form\n    });\n    const __returned__ = {\n      form,\n      props,\n      proxy,\n      headers,\n      successUpload,\n      handleErr,\n      editorRef,\n      valueHtml,\n      mode,\n      toolbarConfig,\n      editorConfig,\n      handleCreated,\n      get rolesEnums() {\n        return rolesEnums;\n      },\n      onBeforeUnmount,\n      nextTick,\n      ref,\n      shallowRef,\n      onMounted,\n      getCurrentInstance,\n      get getTokenAUTH() {\n        return getTokenAUTH;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["rolesEnums", "onBeforeUnmount", "nextTick", "ref", "shallowRef", "onMounted", "getCurrentInstance", "getTokenAUTH", "form", "title", "img", "url", "props", "__props", "proxy", "headers", "value", "Object", "assign", "item", "successUpload", "res", "data", "handleErr", "err", "status", "JSON", "parse", "message", "editor<PERSON><PERSON>", "valueHtml", "mode", "toolbarConfig", "editorConfig", "placeholder", "MENU_CONF", "uploadImage", "fieldName", "maxFileSize", "server", "BASE_API_URL", "customInsert", "insertFn", "IMG_BASE_URL", "alt", "href", "editor", "destroy", "handleCreated", "__expose"], "sources": ["D:/WorkSpace/facai7/facai7_admin/src/views/operationManage/components/bannerList/editPop.vue"], "sourcesContent": ["<template>\r\n  <el-form\r\n    label-width=\"100px\"\r\n    :inline=\"true\"\r\n    :model=\"form\"\r\n    class=\"demo-form-inline\"\r\n  >\r\n    <el-form-item label=\"标题\" required>\r\n      <el-input v-model=\"form.title\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"跳转地址(没有则不填)\" required>\r\n      <el-input v-model=\"form.url\" clearable />\r\n    </el-form-item>\r\n\r\n    <el-form-item\r\n      label=\"图片\"\r\n      prop=\"img\"\r\n      :rules=\"[{ required: true, message: '请上传图片' }]\"\r\n    >\r\n      <el-upload\r\n        class=\"upload-demo\"\r\n        style=\"width: 114px\"\r\n        :show-file-list=\"false\"\r\n        drag\r\n        :headers=\"headers\"\r\n        :action=\"`${proxy.BASE_API_URL}index/upload`\"\r\n        :on-success=\"successUpload\"\r\n        :on-error=\"handleErr\"\r\n        :multiple=\"false\"\r\n      >\r\n        <img\r\n          v-if=\"form.img\"\r\n          :src=\"proxy.IMG_BASE_URL + form.img\"\r\n          width=\"100%\"\r\n          class=\"avatar\" />\r\n        <el-icon v-else class=\"avatar-uploader-icon\"><Plus /></el-icon\r\n      ></el-upload>\r\n    </el-form-item>\r\n  </el-form>\r\n</template>\r\n\r\n<script setup>\r\nimport { rolesEnums } from \"@/config/enums\";\r\nimport {\r\n  onBeforeUnmount,\r\n  nextTick,\r\n  ref,\r\n  shallowRef,\r\n  onMounted,\r\n  getCurrentInstance,\r\n} from \"vue\";\r\nimport { getTokenAUTH } from \"@/utils/auth\";\r\n\r\nconst form = ref({\r\n  title: \"\",\r\n  img: \"\",\r\n  url: \"\",\r\n});\r\nconst props = defineProps([\"item\"]);\r\nconst { proxy } = getCurrentInstance();\r\n\r\nconst headers = ref({})\r\nonMounted(() => {\r\n  headers.value['Accept-Token'] = getTokenAUTH()\r\n  nextTick(() => {\r\n    form.value = Object.assign(form.value, props.item);\r\n  });\r\n});\r\n\r\nconst successUpload = (res) => {\r\n  form.value.img = res.data.url;\r\n};\r\n\r\nconst handleErr = (err) => {\r\n  if (err.status == 320) {\r\n    form.value.img = JSON.parse(err.message).data.url;\r\n  }\r\n}\r\n\r\n// 编辑器实例，必须用 shallowRef\r\nconst editorRef = shallowRef();\r\n\r\n// 内容 HTML\r\nconst valueHtml = ref(\"<p>hello</p>\");\r\nconst mode = ref(\"default\");\r\n\r\nconst toolbarConfig = {};\r\nconst editorConfig = {\r\n  placeholder: \"请输入内容...\",\r\n  MENU_CONF: {\r\n    uploadImage: {\r\n      fieldName: \"file\",\r\n      maxFileSize: 10 * 1024 * 1024, // 10M\r\n      server: proxy.BASE_API_URL + \"index/uploadX\",\r\n      headers: {\r\n        \"Accept-Token\": getTokenAUTH(),\r\n      },\r\n      customInsert(res, insertFn) {\r\n        const url = proxy.IMG_BASE_URL + res.data.url;\r\n        const alt = res.data.alt;\r\n        const href = res.data.href;\r\n        insertFn(url, alt, href);\r\n      },\r\n    },\r\n  },\r\n};\r\n\r\n// 组件销毁时，也及时销毁编辑器\r\nonBeforeUnmount(() => {\r\n  const editor = editorRef.value;\r\n  if (editor == null) return;\r\n  editor.destroy();\r\n});\r\n\r\nconst handleCreated = (editor) => {\r\n  editorRef.value = editor; // 记录 editor 实例，重要！\r\n};\r\n\r\ndefineExpose({ form });\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.demo-form-inline {\r\n  justify-content: flex-start;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.demo-form-inline .el-input {\r\n  --el-input-width: 220px;\r\n}\r\n\r\n.demo-form-inline .el-select {\r\n  --el-select-width: 220px;\r\n}\r\n\r\n/deep/ .el-radio-group {\r\n  width: 220px;\r\n}\r\n\r\n.form-title {\r\n  text-align: left;\r\n  padding-left: 30px;\r\n  margin: 20px auto 10px;\r\n  height: 44px;\r\n  background-color: #f2f2f2;\r\n  border-radius: 5px;\r\n  line-height: 44px;\r\n}\r\n\r\n/deep/ .el-form-item {\r\n  align-items: flex-start;\r\n}\r\n</style>\r\n"], "mappings": "AA0CA,SAASA,UAAU,QAAQ,gBAAgB;AAC3C,SACEC,eAAe,EACfC,QAAQ,EACRC,GAAG,EACHC,UAAU,EACVC,SAAS,EACTC,kBAAkB,QACb,KAAK;AACZ,SAASC,YAAY,QAAQ,cAAc;;;;;;;IAE3C,MAAMC,IAAI,GAAGL,GAAG,CAAC;MACfM,KAAK,EAAE,EAAE;MACTC,GAAG,EAAE,EAAE;MACPC,GAAG,EAAE;IACP,CAAC,CAAC;IACF,MAAMC,KAAK,GAAGC,OAAqB;IACnC,MAAM;MAAEC;IAAM,CAAC,GAAGR,kBAAkB,CAAC,CAAC;IAEtC,MAAMS,OAAO,GAAGZ,GAAG,CAAC,CAAC,CAAC,CAAC;IACvBE,SAAS,CAAC,MAAM;MACdU,OAAO,CAACC,KAAK,CAAC,cAAc,CAAC,GAAGT,YAAY,CAAC,CAAC;MAC9CL,QAAQ,CAAC,MAAM;QACbM,IAAI,CAACQ,KAAK,GAAGC,MAAM,CAACC,MAAM,CAACV,IAAI,CAACQ,KAAK,EAAEJ,KAAK,CAACO,IAAI,CAAC;MACpD,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,MAAMC,aAAa,GAAIC,GAAG,IAAK;MAC7Bb,IAAI,CAACQ,KAAK,CAACN,GAAG,GAAGW,GAAG,CAACC,IAAI,CAACX,GAAG;IAC/B,CAAC;IAED,MAAMY,SAAS,GAAIC,GAAG,IAAK;MACzB,IAAIA,GAAG,CAACC,MAAM,IAAI,GAAG,EAAE;QACrBjB,IAAI,CAACQ,KAAK,CAACN,GAAG,GAAGgB,IAAI,CAACC,KAAK,CAACH,GAAG,CAACI,OAAO,CAAC,CAACN,IAAI,CAACX,GAAG;MACnD;IACF,CAAC;;IAED;IACA,MAAMkB,SAAS,GAAGzB,UAAU,CAAC,CAAC;;IAE9B;IACA,MAAM0B,SAAS,GAAG3B,GAAG,CAAC,cAAc,CAAC;IACrC,MAAM4B,IAAI,GAAG5B,GAAG,CAAC,SAAS,CAAC;IAE3B,MAAM6B,aAAa,GAAG,CAAC,CAAC;IACxB,MAAMC,YAAY,GAAG;MACnBC,WAAW,EAAE,UAAU;MACvBC,SAAS,EAAE;QACTC,WAAW,EAAE;UACXC,SAAS,EAAE,MAAM;UACjBC,WAAW,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;UAAE;UAC/BC,MAAM,EAAEzB,KAAK,CAAC0B,YAAY,GAAG,eAAe;UAC5CzB,OAAO,EAAE;YACP,cAAc,EAAER,YAAY,CAAC;UAC/B,CAAC;UACDkC,YAAYA,CAACpB,GAAG,EAAEqB,QAAQ,EAAE;YAC1B,MAAM/B,GAAG,GAAGG,KAAK,CAAC6B,YAAY,GAAGtB,GAAG,CAACC,IAAI,CAACX,GAAG;YAC7C,MAAMiC,GAAG,GAAGvB,GAAG,CAACC,IAAI,CAACsB,GAAG;YACxB,MAAMC,IAAI,GAAGxB,GAAG,CAACC,IAAI,CAACuB,IAAI;YAC1BH,QAAQ,CAAC/B,GAAG,EAAEiC,GAAG,EAAEC,IAAI,CAAC;UAC1B;QACF;MACF;IACF,CAAC;;IAED;IACA5C,eAAe,CAAC,MAAM;MACpB,MAAM6C,MAAM,GAAGjB,SAAS,CAACb,KAAK;MAC9B,IAAI8B,MAAM,IAAI,IAAI,EAAE;MACpBA,MAAM,CAACC,OAAO,CAAC,CAAC;IAClB,CAAC,CAAC;IAEF,MAAMC,aAAa,GAAIF,MAAM,IAAK;MAChCjB,SAAS,CAACb,KAAK,GAAG8B,MAAM,CAAC,CAAC;IAC5B,CAAC;IAEDG,QAAY,CAAC;MAAEzC;IAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}