{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { getCurrentInstance, ref } from 'vue';\nimport { getKeysMap, getRowIdentity, toggleRowStatus } from '../util.mjs';\nfunction useExpand(watcherData) {\n  const instance = getCurrentInstance();\n  const defaultExpandAll = ref(false);\n  const expandRows = ref([]);\n  const updateExpandRows = () => {\n    const data = watcherData.data.value || [];\n    const rowKey = watcherData.rowKey.value;\n    if (defaultExpandAll.value) {\n      expandRows.value = data.slice();\n    } else if (rowKey) {\n      const expandRowsMap = getKeysMap(expandRows.value, rowKey);\n      expandRows.value = data.reduce((prev, row) => {\n        const rowId = getRowIdentity(row, rowKey);\n        const rowInfo = expandRowsMap[rowId];\n        if (rowInfo) {\n          prev.push(row);\n        }\n        return prev;\n      }, []);\n    } else {\n      expandRows.value = [];\n    }\n  };\n  const toggleRowExpansion = (row, expanded) => {\n    const changed = toggleRowStatus(expandRows.value, row, expanded, void 0, void 0, void 0, watcherData.rowKey.value);\n    if (changed) {\n      instance.emit(\"expand-change\", row, expandRows.value.slice());\n    }\n  };\n  const setExpandRowKeys = rowKeys => {\n    instance.store.assertRowKey();\n    const data = watcherData.data.value || [];\n    const rowKey = watcherData.rowKey.value;\n    const keysMap = getKeysMap(data, rowKey);\n    expandRows.value = rowKeys.reduce((prev, cur) => {\n      const info = keysMap[cur];\n      if (info) {\n        prev.push(info.row);\n      }\n      return prev;\n    }, []);\n  };\n  const isRowExpanded = row => {\n    const rowKey = watcherData.rowKey.value;\n    if (rowKey) {\n      const expandMap = getKeysMap(expandRows.value, rowKey);\n      return !!expandMap[getRowIdentity(row, rowKey)];\n    }\n    return expandRows.value.includes(row);\n  };\n  return {\n    updateExpandRows,\n    toggleRowExpansion,\n    setExpandRowKeys,\n    isRowExpanded,\n    states: {\n      expandRows,\n      defaultExpandAll\n    }\n  };\n}\nexport { useExpand as default };", "map": {"version": 3, "names": ["useExpand", "watcherData", "instance", "getCurrentInstance", "defaultExpandAll", "ref", "expandRows", "updateExpandRows", "data", "value", "<PERSON><PERSON><PERSON>", "slice", "expandRowsMap", "getKeysMap", "reduce", "prev", "row", "rowId", "getRowIdentity", "rowInfo", "push", "toggleRowExpansion", "expanded", "changed", "toggleRowStatus", "emit", "setExpandRowKeys", "row<PERSON>eys", "store", "assertRowKey", "keysMap", "cur", "info", "isRowExpanded", "expandMap", "includes", "states"], "sources": ["../../../../../../../packages/components/table/src/store/expand.ts"], "sourcesContent": ["import { getCurrentInstance, ref } from 'vue'\nimport { getKeysMap, getRowIdentity, toggleRowStatus } from '../util'\n\nimport type { Ref } from 'vue'\nimport type { WatcherPropsData } from '.'\nimport type { DefaultRow, Table } from '../table/defaults'\n\nfunction useExpand<T extends DefaultRow>(watcherData: WatcherPropsData<T>) {\n  const instance = getCurrentInstance() as Table<T>\n  const defaultExpandAll = ref(false)\n  const expandRows: Ref<T[]> = ref([])\n  const updateExpandRows = () => {\n    const data = watcherData.data.value || []\n    const rowKey = watcherData.rowKey.value\n    if (defaultExpandAll.value) {\n      expandRows.value = data.slice()\n    } else if (rowKey) {\n      // TODO：这里的代码可以优化\n      const expandRowsMap = getKeysMap(expandRows.value, rowKey)\n      expandRows.value = data.reduce((prev: T[], row: T) => {\n        const rowId = getRowIdentity(row, rowKey)\n        const rowInfo = expandRowsMap[rowId]\n        if (rowInfo) {\n          prev.push(row)\n        }\n        return prev\n      }, [])\n    } else {\n      expandRows.value = []\n    }\n  }\n\n  const toggleRowExpansion = (row: T, expanded?: boolean) => {\n    const changed = toggleRowStatus(\n      expandRows.value,\n      row,\n      expanded,\n      undefined,\n      undefined,\n      undefined,\n      watcherData.rowKey.value\n    )\n    if (changed) {\n      instance.emit('expand-change', row, expandRows.value.slice())\n    }\n  }\n\n  const setExpandRowKeys = (rowKeys: (string | number)[]) => {\n    instance.store.assertRowKey()\n    // TODO：这里的代码可以优化\n    const data = watcherData.data.value || []\n    const rowKey = watcherData.rowKey.value\n    const keysMap = getKeysMap(data, rowKey)\n    expandRows.value = rowKeys.reduce((prev: T[], cur) => {\n      const info = keysMap[cur]\n      if (info) {\n        prev.push(info.row)\n      }\n      return prev\n    }, [])\n  }\n\n  const isRowExpanded = (row: T): boolean => {\n    const rowKey = watcherData.rowKey.value\n    if (rowKey) {\n      const expandMap = getKeysMap(expandRows.value, rowKey)\n      return !!expandMap[getRowIdentity(row, rowKey)]\n    }\n    return expandRows.value.includes(row)\n  }\n  return {\n    updateExpandRows,\n    toggleRowExpansion,\n    setExpandRowKeys,\n    isRowExpanded,\n    states: {\n      expandRows,\n      defaultExpandAll,\n    },\n  }\n}\n\nexport default useExpand\n"], "mappings": ";;;;;AAEA,SAASA,SAASA,CAACC,WAAW,EAAE;EAC9B,MAAMC,QAAQ,GAAGC,kBAAkB,EAAE;EACrC,MAAMC,gBAAgB,GAAGC,GAAG,CAAC,KAAK,CAAC;EACnC,MAAMC,UAAU,GAAGD,GAAG,CAAC,EAAE,CAAC;EAC1B,MAAME,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,IAAI,GAAGP,WAAW,CAACO,IAAI,CAACC,KAAK,IAAI,EAAE;IACzC,MAAMC,MAAM,GAAGT,WAAW,CAACS,MAAM,CAACD,KAAK;IACvC,IAAIL,gBAAgB,CAACK,KAAK,EAAE;MAC1BH,UAAU,CAACG,KAAK,GAAGD,IAAI,CAACG,KAAK,EAAE;IACrC,CAAK,MAAM,IAAID,MAAM,EAAE;MACjB,MAAME,aAAa,GAAGC,UAAU,CAACP,UAAU,CAACG,KAAK,EAAEC,MAAM,CAAC;MAC1DJ,UAAU,CAACG,KAAK,GAAGD,IAAI,CAACM,MAAM,CAAC,CAACC,IAAI,EAAEC,GAAG,KAAK;QAC5C,MAAMC,KAAK,GAAGC,cAAc,CAACF,GAAG,EAAEN,MAAM,CAAC;QACzC,MAAMS,OAAO,GAAGP,aAAa,CAACK,KAAK,CAAC;QACpC,IAAIE,OAAO,EAAE;UACXJ,IAAI,CAACK,IAAI,CAACJ,GAAG,CAAC;QACxB;QACQ,OAAOD,IAAI;MACnB,CAAO,EAAE,EAAE,CAAC;IACZ,CAAK,MAAM;MACLT,UAAU,CAACG,KAAK,GAAG,EAAE;IAC3B;EACA,CAAG;EACD,MAAMY,kBAAkB,GAAGA,CAACL,GAAG,EAAEM,QAAQ,KAAK;IAC5C,MAAMC,OAAO,GAAGC,eAAe,CAAClB,UAAU,CAACG,KAAK,EAAEO,GAAG,EAAEM,QAAQ,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAErB,WAAW,CAACS,MAAM,CAACD,KAAK,CAAC;IAClH,IAAIc,OAAO,EAAE;MACXrB,QAAQ,CAACuB,IAAI,CAAC,eAAe,EAAET,GAAG,EAAEV,UAAU,CAACG,KAAK,CAACE,KAAK,EAAE,CAAC;IACnE;EACA,CAAG;EACD,MAAMe,gBAAgB,GAAIC,OAAO,IAAK;IACpCzB,QAAQ,CAAC0B,KAAK,CAACC,YAAY,EAAE;IAC7B,MAAMrB,IAAI,GAAGP,WAAW,CAACO,IAAI,CAACC,KAAK,IAAI,EAAE;IACzC,MAAMC,MAAM,GAAGT,WAAW,CAACS,MAAM,CAACD,KAAK;IACvC,MAAMqB,OAAO,GAAGjB,UAAU,CAACL,IAAI,EAAEE,MAAM,CAAC;IACxCJ,UAAU,CAACG,KAAK,GAAGkB,OAAO,CAACb,MAAM,CAAC,CAACC,IAAI,EAAEgB,GAAG,KAAK;MAC/C,MAAMC,IAAI,GAAGF,OAAO,CAACC,GAAG,CAAC;MACzB,IAAIC,IAAI,EAAE;QACRjB,IAAI,CAACK,IAAI,CAACY,IAAI,CAAChB,GAAG,CAAC;MAC3B;MACM,OAAOD,IAAI;IACjB,CAAK,EAAE,EAAE,CAAC;EACV,CAAG;EACD,MAAMkB,aAAa,GAAIjB,GAAG,IAAK;IAC7B,MAAMN,MAAM,GAAGT,WAAW,CAACS,MAAM,CAACD,KAAK;IACvC,IAAIC,MAAM,EAAE;MACV,MAAMwB,SAAS,GAAGrB,UAAU,CAACP,UAAU,CAACG,KAAK,EAAEC,MAAM,CAAC;MACtD,OAAO,CAAC,CAACwB,SAAS,CAAChB,cAAc,CAACF,GAAG,EAAEN,MAAM,CAAC,CAAC;IACrD;IACI,OAAOJ,UAAU,CAACG,KAAK,CAAC0B,QAAQ,CAACnB,GAAG,CAAC;EACzC,CAAG;EACD,OAAO;IACLT,gBAAgB;IAChBc,kBAAkB;IAClBK,gBAAgB;IAChBO,aAAa;IACbG,MAAM,EAAE;MACN9B,UAAU;MACVF;IACN;EACA,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}