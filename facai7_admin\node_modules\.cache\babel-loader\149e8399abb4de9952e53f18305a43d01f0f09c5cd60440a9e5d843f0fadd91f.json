{"ast": null, "code": "import { getCurrentInstance, nextTick, onMounted, ref } from \"vue\";\nexport default {\n  __name: 'userCountForm',\n  props: [\"item\"],\n  setup(__props, {\n    expose: __expose\n  }) {\n    const form = ref({\n      id: \"\",\n      sfz_name: \"\",\n      shares_money: \"\",\n      yuebao_money: \"\",\n      yuebao_earn: \"\",\n      yuebao_time: \"\",\n      yuebao_num: \"\",\n      recharge_num: \"\",\n      withdraw_money: \"\",\n      withdraw_num: \"\",\n      signin_money: \"\",\n      signin_num: \"\",\n      raffle_money: \"\",\n      raffle_num: \"\",\n      team_invite: \"\",\n      team_item: \"\",\n      team_item_v1: \"\",\n      team_item_v2: \"\",\n      team_item_v3: \"\",\n      team_invite_v1: \"\",\n      team_invite_v2: \"\",\n      team_invite_v3: \"\",\n      team_invite_num: \"\",\n      team_invite_user: \"\",\n      team_recharge: \"\",\n      team_recharge_v1: \"\",\n      team_recharge_v2: \"\",\n      team_recharge_v3: \"\",\n      income_money: \"\",\n      invest_money: \"\",\n      invest_not_finish: \"\",\n      invest_num: \"\",\n      user_points: \"\",\n      user_coupon: \"\",\n      bonus_num: \"\",\n      bonus_money: \"\"\n    });\n    const props = __props;\n    onMounted(() => {\n      nextTick(() => {\n        form.value = Object.assign(form.value, props.item);\n        getUserState();\n      });\n    });\n    const {\n      proxy\n    } = getCurrentInstance();\n    const getUserState = async () => {\n      const res = await proxy.$http({\n        method: \"get\",\n        url: \"user/getUserInfos?id=\" + form.value.id\n      });\n      if (res.code == 0) {\n        form.value = Object.assign(form.value, res.data);\n      }\n    };\n    __expose({\n      form\n    });\n    const __returned__ = {\n      form,\n      props,\n      proxy,\n      getUserState,\n      getCurrentInstance,\n      nextTick,\n      onMounted,\n      ref\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["getCurrentInstance", "nextTick", "onMounted", "ref", "form", "id", "sfz_name", "shares_money", "yuebao_money", "yuebao_earn", "yuebao_time", "yuebao_num", "recharge_num", "withdraw_money", "withdraw_num", "signin_money", "signin_num", "raffle_money", "raffle_num", "team_invite", "team_item", "team_item_v1", "team_item_v2", "team_item_v3", "team_invite_v1", "team_invite_v2", "team_invite_v3", "team_invite_num", "team_invite_user", "team_recharge", "team_recharge_v1", "team_recharge_v2", "team_recharge_v3", "income_money", "invest_money", "invest_not_finish", "invest_num", "user_points", "user_coupon", "bonus_num", "bonus_money", "props", "__props", "value", "Object", "assign", "item", "getUserState", "proxy", "res", "$http", "method", "url", "code", "data", "__expose"], "sources": ["D:/WorkSpace/facai7/facai7_admin/src/views/userManage/components/userList/userCountForm.vue"], "sourcesContent": ["<template>\r\n  <el-form\r\n    label-width=\"140px\"\r\n    :inline=\"true\"\r\n    :model=\"form\"\r\n    class=\"demo-form-inline\"\r\n  >\r\n    <el-form-item label=\"用户名ID\" required>\r\n      <el-input v-model=\"form.id\" disabled clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"用户名\" required>\r\n      <el-input v-model=\"form.sfz_name\" disabled clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"股权金额\" required>\r\n      <el-input v-model=\"form.shares_money\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"余额宝金额\" required>\r\n      <el-input v-model=\"form.yuebao_money\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"余额宝收益\" required>\r\n      <el-input v-model=\"form.yuebao_earn\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"余额宝结算时间\" required>\r\n      <el-input v-model=\"form.yuebao_time\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"余额宝结算次数\" required>\r\n      <el-input v-model=\"form.yuebao_num\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"累计充值次数\" required>\r\n      <el-input v-model=\"form.recharge_num\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"累计提现额度\" required>\r\n      <el-input v-model=\"form.withdraw_money\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"累计提现次数\" required>\r\n      <el-input v-model=\"form.withdraw_num\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"用户签到额度\" required>\r\n      <el-input v-model=\"form.signin_money\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"用户签到次数\" required>\r\n      <el-input v-model=\"form.signin_num\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"用户抽奖收益\" required>\r\n      <el-input v-model=\"form.raffle_money\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"用户抽奖次数\" required>\r\n      <el-input v-model=\"form.raffle_num\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"团队投资金额\" required>\r\n      <el-input v-model=\"form.team_invite\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"团队投资项目\" required>\r\n      <el-input v-model=\"form.team_item\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"团队投资v1金额\" required>\r\n      <el-input v-model=\"form.team_item_v1\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"团队投资v2金额\" required>\r\n      <el-input v-model=\"form.team_item_v2\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"团队投资v3金额\" required>\r\n      <el-input v-model=\"form.team_item_v3\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"团队返利v1金额\" required>\r\n      <el-input v-model=\"form.team_invite_v1\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"团队返利v2金额\" required>\r\n      <el-input v-model=\"form.team_invite_v2\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"团队返利v3金额\" required>\r\n      <el-input v-model=\"form.team_invite_v3\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"团队返利次数\" required>\r\n      <el-input v-model=\"form.team_invite_num\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"团队人数\" required>\r\n      <el-input v-model=\"form.team_invite_user\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"团队充值金额\" required>\r\n      <el-input v-model=\"form.team_recharge\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"团队充值v1金额\" required>\r\n      <el-input v-model=\"form.team_recharge_v1\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"团队充值v2金额\" required>\r\n      <el-input v-model=\"form.team_recharge_v2\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"团队充值v3金额\" required>\r\n      <el-input v-model=\"form.team_recharge_v3\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"用户总收益\" required>\r\n      <el-input v-model=\"form.income_money\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"用户投资额度\" required>\r\n      <el-input v-model=\"form.invest_money\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"用户在订购中金额\" required>\r\n      <el-input v-model=\"form.invest_not_finish\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"用户总订阅数量\" required>\r\n      <el-input v-model=\"form.invest_num\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"用户积分\" required>\r\n      <el-input v-model=\"form.user_points\" clearable />\r\n    </el-form-item>\r\n\r\n    <el-form-item label=\"用户优惠券\" required>\r\n      <el-input v-model=\"form.user_coupon\" clearable />\r\n    </el-form-item>\r\n\r\n    <el-form-item label=\"用户奖金次数\" required>\r\n      <el-input v-model=\"form.bonus_num\" clearable />\r\n    </el-form-item>\r\n\r\n    <el-form-item label=\"用户奖金收益\" required>\r\n      <el-input v-model=\"form.bonus_money\" clearable />\r\n    </el-form-item>\r\n  </el-form>\r\n</template>\r\n\r\n<script setup>\r\nimport { getCurrentInstance, nextTick, onMounted, ref } from \"vue\";\r\n\r\nconst form = ref({\r\n  id: \"\",\r\n  sfz_name: \"\",\r\n  shares_money: \"\",\r\n  yuebao_money: \"\",\r\n  yuebao_earn: \"\",\r\n  yuebao_time: \"\",\r\n  yuebao_num: \"\",\r\n  recharge_num: \"\",\r\n  withdraw_money: \"\",\r\n  withdraw_num: \"\",\r\n  signin_money: \"\",\r\n  signin_num: \"\",\r\n  raffle_money: \"\",\r\n  raffle_num: \"\",\r\n  team_invite: \"\",\r\n  team_item: \"\",\r\n  team_item_v1: \"\",\r\n  team_item_v2: \"\",\r\n  team_item_v3: \"\",\r\n  team_invite_v1: \"\",\r\n  team_invite_v2: \"\",\r\n  team_invite_v3: \"\",\r\n  team_invite_num: \"\",\r\n  team_invite_user: \"\",\r\n  team_recharge: \"\",\r\n  team_recharge_v1: \"\",\r\n  team_recharge_v2: \"\",\r\n  team_recharge_v3: \"\",\r\n  income_money: \"\",\r\n  invest_money: \"\",\r\n  invest_not_finish: \"\",\r\n  invest_num: \"\",\r\n  user_points: \"\",\r\n  user_coupon: \"\",\r\n  bonus_num: \"\",\r\n  bonus_money: \"\",\r\n});\r\nconst props = defineProps([\"item\"]);\r\n\r\nonMounted(() => {\r\n  nextTick(() => {\r\n    form.value = Object.assign(form.value, props.item);\r\n    getUserState();\r\n  });\r\n});\r\n\r\nconst { proxy } = getCurrentInstance();\r\nconst getUserState = async () => {\r\n  const res = await proxy.$http({\r\n    method: \"get\",\r\n    url: \"user/getUserInfos?id=\" + form.value.id,\r\n  });\r\n\r\n  if (res.code == 0) {\r\n    form.value = Object.assign(form.value, res.data);\r\n  }\r\n};\r\n\r\ndefineExpose({ form });\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.demo-form-inline {\r\n  justify-content: flex-start;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n.demo-form-inline .el-input {\r\n  --el-input-width: 260px;\r\n}\r\n\r\n.demo-form-inline .el-select {\r\n  --el-select-width: 260px;\r\n}\r\n/deep/ .el-radio-group {\r\n  width: 220px;\r\n}\r\n.form-title {\r\n  text-align: left;\r\n  padding-left: 30px;\r\n  margin: 20px auto 10px;\r\n  height: 44px;\r\n  background-color: #f2f2f2;\r\n  border-radius: 5px;\r\n  line-height: 44px;\r\n}\r\n/deep/ .el-form-item {\r\n  align-items: flex-start;\r\n}\r\n</style>\r\n"], "mappings": "AA0HA,SAASA,kBAAkB,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,GAAG,QAAQ,KAAK;;;;;;;IAElE,MAAMC,IAAI,GAAGD,GAAG,CAAC;MACfE,EAAE,EAAE,EAAE;MACNC,QAAQ,EAAE,EAAE;MACZC,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAE,EAAE;MACfC,WAAW,EAAE,EAAE;MACfC,UAAU,EAAE,EAAE;MACdC,YAAY,EAAE,EAAE;MAChBC,cAAc,EAAE,EAAE;MAClBC,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE,EAAE;MAChBC,UAAU,EAAE,EAAE;MACdC,YAAY,EAAE,EAAE;MAChBC,UAAU,EAAE,EAAE;MACdC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE,EAAE;MAChBC,cAAc,EAAE,EAAE;MAClBC,cAAc,EAAE,EAAE;MAClBC,cAAc,EAAE,EAAE;MAClBC,eAAe,EAAE,EAAE;MACnBC,gBAAgB,EAAE,EAAE;MACpBC,aAAa,EAAE,EAAE;MACjBC,gBAAgB,EAAE,EAAE;MACpBC,gBAAgB,EAAE,EAAE;MACpBC,gBAAgB,EAAE,EAAE;MACpBC,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE,EAAE;MAChBC,iBAAiB,EAAE,EAAE;MACrBC,UAAU,EAAE,EAAE;MACdC,WAAW,EAAE,EAAE;MACfC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbC,WAAW,EAAE;IACf,CAAC,CAAC;IACF,MAAMC,KAAK,GAAGC,OAAqB;IAEnCxC,SAAS,CAAC,MAAM;MACdD,QAAQ,CAAC,MAAM;QACbG,IAAI,CAACuC,KAAK,GAAGC,MAAM,CAACC,MAAM,CAACzC,IAAI,CAACuC,KAAK,EAAEF,KAAK,CAACK,IAAI,CAAC;QAClDC,YAAY,CAAC,CAAC;MAChB,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,MAAM;MAAEC;IAAM,CAAC,GAAGhD,kBAAkB,CAAC,CAAC;IACtC,MAAM+C,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,MAAME,GAAG,GAAG,MAAMD,KAAK,CAACE,KAAK,CAAC;QAC5BC,MAAM,EAAE,KAAK;QACbC,GAAG,EAAE,uBAAuB,GAAGhD,IAAI,CAACuC,KAAK,CAACtC;MAC5C,CAAC,CAAC;MAEF,IAAI4C,GAAG,CAACI,IAAI,IAAI,CAAC,EAAE;QACjBjD,IAAI,CAACuC,KAAK,GAAGC,MAAM,CAACC,MAAM,CAACzC,IAAI,CAACuC,KAAK,EAAEM,GAAG,CAACK,IAAI,CAAC;MAClD;IACF,CAAC;IAEDC,QAAY,CAAC;MAAEnD;IAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}