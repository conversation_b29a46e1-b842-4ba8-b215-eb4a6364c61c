{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, createElementBlock as _createElementBlock } from \"vue\";\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createBlock(_component_el_form, {\n    \"label-width\": \"100px\",\n    inline: true,\n    model: $setup.form,\n    class: \"demo-form-inline\"\n  }, {\n    default: _withCtx(() => [_cache[6] || (_cache[6] = _createElementVNode(\"h4\", {\n      class: \"form-title\"\n    }, \"买家信息\", -1 /* CACHED */)), $props.formType !== 'add' ? (_openBlock(), _createBlock(_component_el_form_item, {\n      key: 0,\n      label: \"用户名\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.username,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.form.username = $event),\n        clearable: \"\",\n        disabled: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    })) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_form_item, {\n      label: \"手机\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.phone,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.form.phone = $event),\n        disabled: $props.formType != 'add',\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\", \"disabled\"])]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" <el-form-item label=\\\"订单号\\\" required>\\r\\n      <el-input v-model=\\\"form.deliver_order_no\\\" clearable />\\r\\n    </el-form-item> \"), _createCommentVNode(\" <el-form-item label=\\\"商品名称\\\"  required>\\r\\n      <el-input v-model=\\\"form.goods_title\\\" :disabled=\\\"formType != 'add'\\\" clearable />\\r\\n    </el-form-item> \"), _createCommentVNode(\" <el-form-item label=\\\"姓名\\\"  required>\\r\\n      <el-input v-model=\\\"form.address_name\\\" clearable :disabled=\\\"formType != 'add'\\\" />\\r\\n    </el-form-item> \"), _createCommentVNode(\" <el-form-item label=\\\"收货手机号\\\" required>\\r\\n      <el-input v-model=\\\"form.address_phone\\\" :disabled=\\\"formType != 'add'\\\" clearable />\\r\\n    </el-form-item> \"), _createVNode(_component_el_form_item, {\n      label: \"商品\",\n      prop: \"goods_id\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_select, {\n        modelValue: $setup.form.goods_id,\n        \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.form.goods_id = $event),\n        placeholder: \"请选择商品\",\n        clearable: \"\"\n      }, {\n        default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.goodsList, item => {\n          return _openBlock(), _createBlock(_component_el_option, {\n            label: item.title,\n            key: item.title,\n            value: item.id\n          }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n        }), 128 /* KEYED_FRAGMENT */))]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" <el-form-item label=\\\"市区\\\" required>\\r\\n      <el-input v-model=\\\"form.address_city\\\" :disabled=\\\"formType != 'add'\\\" clearable />\\r\\n    </el-form-item> \"), _createCommentVNode(\" <el-form-item label=\\\"详细地址\\\" required>\\r\\n      <el-input v-model=\\\"form.address_place\\\" :disabled=\\\"formType != 'add'\\\" clearable />\\r\\n    </el-form-item> \"), _cache[7] || (_cache[7] = _createElementVNode(\"h4\", {\n      class: \"form-title\"\n    }, \"发货信息\", -1 /* CACHED */)), _createVNode(_component_el_form_item, {\n      label: \"订单状态\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_select, {\n        modelValue: $setup.form.status,\n        \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.form.status = $event),\n        placeholder: \"\",\n        clearable: \"\"\n      }, {\n        default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.sendStatusEnums, item => {\n          return _openBlock(), _createBlock(_component_el_option, {\n            label: item.label,\n            value: item.value\n          }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n        }), 256 /* UNKEYED_FRAGMENT */))]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"发货公司\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.deliver_title,\n        \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.form.deliver_title = $event),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"发货单号\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.deliver_order_no,\n        \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.form.deliver_order_no = $event),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" <el-form-item label=\\\"发货时间\\\" required>\\r\\n      <el-date-picker\\r\\n        v-model=\\\"form.deliver_time\\\"\\r\\n        type=\\\"datetime\\\"\\r\\n        value-format=\\\"YYYY-MM-DD HH:mm:ss\\\"\\r\\n        placeholder=\\\"发货时间\\\"\\r\\n        style=\\\"width: 100%\\\"\\r\\n      />\\r\\n    </el-form-item> \")]),\n    _: 1 /* STABLE */,\n    __: [6, 7]\n  }, 8 /* PROPS */, [\"model\"]);\n}", "map": {"version": 3, "names": ["_createBlock", "_component_el_form", "inline", "model", "$setup", "form", "class", "_createElementVNode", "$props", "formType", "_component_el_form_item", "label", "required", "_createVNode", "_component_el_input", "username", "$event", "clearable", "disabled", "phone", "_createCommentVNode", "prop", "_component_el_select", "goods_id", "placeholder", "_createElementBlock", "_Fragment", "_renderList", "goodsList", "item", "_component_el_option", "title", "key", "value", "id", "status", "sendStatusEnums", "deliver_title", "deliver_order_no"], "sources": ["D:\\WorkSpace\\facai7\\facai7_admin\\src\\views\\goodsManage\\components\\goodsRecords\\editPop.vue"], "sourcesContent": ["<template>\r\n  <el-form\r\n    label-width=\"100px\"\r\n    :inline=\"true\"\r\n    :model=\"form\"\r\n    class=\"demo-form-inline\"\r\n  >\r\n    <h4 class=\"form-title\">买家信息</h4>\r\n    <el-form-item label=\"用户名\" v-if=\"formType !== 'add'\" required>\r\n      <el-input v-model=\"form.username\" clearable disabled />\r\n    </el-form-item>\r\n    <el-form-item label=\"手机\" required>\r\n      <el-input v-model=\"form.phone\" :disabled=\"formType != 'add'\" clearable />\r\n    </el-form-item>\r\n    <!-- <el-form-item label=\"订单号\" required>\r\n      <el-input v-model=\"form.deliver_order_no\" clearable />\r\n    </el-form-item> -->\r\n    <!-- <el-form-item label=\"商品名称\"  required>\r\n      <el-input v-model=\"form.goods_title\" :disabled=\"formType != 'add'\" clearable />\r\n    </el-form-item> -->\r\n    <!-- <el-form-item label=\"姓名\"  required>\r\n      <el-input v-model=\"form.address_name\" clearable :disabled=\"formType != 'add'\" />\r\n    </el-form-item> -->\r\n    <!-- <el-form-item label=\"收货手机号\" required>\r\n      <el-input v-model=\"form.address_phone\" :disabled=\"formType != 'add'\" clearable />\r\n    </el-form-item> -->\r\n    <el-form-item label=\"商品\" prop=\"goods_id\">\r\n      <el-select v-model=\"form.goods_id\" placeholder=\"请选择商品\" clearable>\r\n        <el-option\r\n          v-for=\"item in goodsList\"\r\n          :label=\"item.title\"\r\n          :key=\"item.title\"\r\n          :value=\"item.id\"\r\n        />\r\n      </el-select>\r\n    </el-form-item>\r\n    <!-- <el-form-item label=\"市区\" required>\r\n      <el-input v-model=\"form.address_city\" :disabled=\"formType != 'add'\" clearable />\r\n    </el-form-item> -->\r\n    <!-- <el-form-item label=\"详细地址\" required>\r\n      <el-input v-model=\"form.address_place\" :disabled=\"formType != 'add'\" clearable />\r\n    </el-form-item> -->\r\n\r\n    <h4 class=\"form-title\">发货信息</h4>\r\n    <el-form-item label=\"订单状态\" required>\r\n      <el-select v-model=\"form.status\" placeholder=\"\" clearable>\r\n        <el-option\r\n          v-for=\"item in sendStatusEnums\"\r\n          :label=\"item.label\"\r\n          :value=\"item.value\"\r\n        />\r\n      </el-select>\r\n    </el-form-item>\r\n    <el-form-item label=\"发货公司\" required>\r\n      <el-input v-model=\"form.deliver_title\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"发货单号\" required>\r\n      <el-input v-model=\"form.deliver_order_no\" clearable />\r\n    </el-form-item>\r\n    <!-- <el-form-item label=\"发货时间\" required>\r\n      <el-date-picker\r\n        v-model=\"form.deliver_time\"\r\n        type=\"datetime\"\r\n        value-format=\"YYYY-MM-DD HH:mm:ss\"\r\n        placeholder=\"发货时间\"\r\n        style=\"width: 100%\"\r\n      />\r\n    </el-form-item> -->\r\n  </el-form>\r\n</template>\r\n\r\n<script setup>\r\nimport { nextTick, onMounted, ref, getCurrentInstance } from \"vue\";\r\nimport { rolesEnums, getLabelByVal, sendStatusEnums } from \"@/config/enums\";\r\nimport moment from \"moment\";\r\n\r\nconst form = ref({\r\n  username: \"\",\r\n  phone: \"\",\r\n  deliver_order_no: \"\",\r\n  deliver_title: \"\",\r\n  address_name: \"\",\r\n  address_phone: \"\",\r\n  address_city: \"\",\r\n  address_place: \"\",\r\n  status: \"\",\r\n  goods_id: \"\",\r\n  deliver_title: \"\",\r\n  deliver_time: \"\",\r\n});\r\nconst props = defineProps([\"item\", \"formType\"]);\r\n\r\nonMounted(() => {\r\n  nextTick(() => {\r\n    form.value = Object.assign(form, props.item);\r\n    if (form.value.deliver_time) {\r\n      form.value.deliver_time = moment(new Date(props.item.deliver_time * 1000)).format('YYYY-MM-DD HH:mm:ss')\r\n    }\r\n  });\r\n  getGoodsList();\r\n});\r\n\r\nconst { proxy } = getCurrentInstance();\r\n\r\nconst goodsList = ref([]);\r\n\r\nconst getGoodsList = async () => {\r\n  const res = await proxy.$http({\r\n    method: \"get\",\r\n    url: \"/Goods/getGoodsLists\",\r\n    params: {\r\n      limit: 1000000\r\n    }\r\n  });\r\n  if (res.code == 0) {\r\n    goodsList.value = res.data.data;\r\n  }\r\n};\r\n\r\ndefineExpose({ form });\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.demo-form-inline {\r\n  justify-content: flex-start;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n.demo-form-inline .el-input {\r\n  --el-input-width: 220px;\r\n}\r\n\r\n.demo-form-inline .el-select {\r\n  --el-select-width: 220px;\r\n}\r\n/deep/ .el-radio-group {\r\n  width: 220px;\r\n}\r\n.form-title {\r\n  text-align: left;\r\n  padding-left: 30px;\r\n  margin: 20px auto 10px;\r\n  height: 44px;\r\n  background-color: #f2f2f2;\r\n  border-radius: 5px;\r\n  line-height: 44px;\r\n  width: 100%;\r\n}\r\n/deep/ .el-form-item {\r\n  align-items: flex-start;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;uBACEA,YAAA,CAmEUC,kBAAA;IAlER,aAAW,EAAC,OAAO;IAClBC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IACZC,KAAK,EAAC;;sBAEN,MAAgC,C,0BAAhCC,mBAAA,CAAgC;MAA5BD,KAAK,EAAC;IAAY,GAAC,MAAI,qBACKE,MAAA,CAAAC,QAAQ,c,cAAxCT,YAAA,CAEeU,uBAAA;;MAFDC,KAAK,EAAC,KAAK;MAA2BC,QAAQ,EAAR;;wBAClD,MAAuD,CAAvDC,YAAA,CAAuDC,mBAAA;oBAApCV,MAAA,CAAAC,IAAI,CAACU,QAAQ;mEAAbX,MAAA,CAAAC,IAAI,CAACU,QAAQ,GAAAC,MAAA;QAAEC,SAAS,EAAT,EAAS;QAACC,QAAQ,EAAR;;;6CAE9CL,YAAA,CAEeH,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,QAAQ,EAAR;;wBACvB,MAAyE,CAAzEC,YAAA,CAAyEC,mBAAA;oBAAtDV,MAAA,CAAAC,IAAI,CAACc,KAAK;mEAAVf,MAAA,CAAAC,IAAI,CAACc,KAAK,GAAAH,MAAA;QAAGE,QAAQ,EAAEV,MAAA,CAAAC,QAAQ;QAAWQ,SAAS,EAAT;;;QAE/DG,mBAAA,oIAEmB,EACnBA,mBAAA,iKAEmB,EACnBA,mBAAA,gKAEmB,EACnBA,mBAAA,mKAEmB,EACnBP,YAAA,CASeH,uBAAA;MATDC,KAAK,EAAC,IAAI;MAACU,IAAI,EAAC;;wBAC5B,MAOY,CAPZR,YAAA,CAOYS,oBAAA;oBAPQlB,MAAA,CAAAC,IAAI,CAACkB,QAAQ;mEAAbnB,MAAA,CAAAC,IAAI,CAACkB,QAAQ,GAAAP,MAAA;QAAEQ,WAAW,EAAC,OAAO;QAACP,SAAS,EAAT;;0BAEnD,MAAyB,E,kBAD3BQ,mBAAA,CAKEC,SAAA,QAAAC,WAAA,CAJevB,MAAA,CAAAwB,SAAS,EAAjBC,IAAI;+BADb7B,YAAA,CAKE8B,oBAAA;YAHCnB,KAAK,EAAEkB,IAAI,CAACE,KAAK;YACjBC,GAAG,EAAEH,IAAI,CAACE,KAAK;YACfE,KAAK,EAAEJ,IAAI,CAACK;;;;;;QAInBd,mBAAA,+JAEmB,EACnBA,mBAAA,kKAEmB,E,0BAEnBb,mBAAA,CAAgC;MAA5BD,KAAK,EAAC;IAAY,GAAC,MAAI,qBAC3BO,YAAA,CAQeH,uBAAA;MARDC,KAAK,EAAC,MAAM;MAACC,QAAQ,EAAR;;wBACzB,MAMY,CANZC,YAAA,CAMYS,oBAAA;oBANQlB,MAAA,CAAAC,IAAI,CAAC8B,MAAM;mEAAX/B,MAAA,CAAAC,IAAI,CAAC8B,MAAM,GAAAnB,MAAA;QAAEQ,WAAW,EAAC,EAAE;QAACP,SAAS,EAAT;;0BAE5C,MAA+B,E,kBADjCQ,mBAAA,CAIEC,SAAA,QAAAC,WAAA,CAHevB,MAAA,CAAAgC,eAAe,EAAvBP,IAAI;+BADb7B,YAAA,CAIE8B,oBAAA;YAFCnB,KAAK,EAAEkB,IAAI,CAAClB,KAAK;YACjBsB,KAAK,EAAEJ,IAAI,CAACI;;;;;;QAInBpB,YAAA,CAEeH,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,QAAQ,EAAR;;wBACzB,MAAmD,CAAnDC,YAAA,CAAmDC,mBAAA;oBAAhCV,MAAA,CAAAC,IAAI,CAACgC,aAAa;mEAAlBjC,MAAA,CAAAC,IAAI,CAACgC,aAAa,GAAArB,MAAA;QAAEC,SAAS,EAAT;;;QAEzCJ,YAAA,CAEeH,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,QAAQ,EAAR;;wBACzB,MAAsD,CAAtDC,YAAA,CAAsDC,mBAAA;oBAAnCV,MAAA,CAAAC,IAAI,CAACiC,gBAAgB;mEAArBlC,MAAA,CAAAC,IAAI,CAACiC,gBAAgB,GAAAtB,MAAA;QAAEC,SAAS,EAAT;;;QAE5CG,mBAAA,+RAQmB,C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}