{"ast": null, "code": "import { ref, getCurrentInstance, onMounted } from \"vue\";\nimport { sfzStatusEnums } from \"@/config/enums\";\nimport { ElMessage, ElMessageBox } from \"element-plus\";\nexport default {\n  __name: 'userLevelTwo',\n  props: [\"item\", 'level'],\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const searchForm = ref({\n      phone: \"\",\n      invite: \"\",\n      level: \"\",\n      starttime: \"\",\n      endtime: \"\",\n      blow: 2\n    });\n    const tableData = ref([]);\n    const props = __props;\n    const {\n      proxy\n    } = getCurrentInstance();\n    onMounted(() => {\n      searchForm.value.phone = props.item.phone;\n      searchForm.value.blow = props.level;\n      getList();\n    });\n    const searchList = () => {\n      getList();\n    };\n    const page = ref(1);\n    const limit = ref(10);\n    const totalList = ref(0);\n    const tableLoading = ref(false);\n    const getList = async () => {\n      tableLoading.value = true;\n      const res = await proxy.$http({\n        method: \"get\",\n        url: \"/user/getUserLists\",\n        params: {\n          page: page.value,\n          limit: limit.value,\n          ...searchForm.value\n        }\n      });\n      tableLoading.value = false;\n      if (res.code == 0) {\n        tableData.value = res.data.data;\n        totalList.value = res.data.total;\n      }\n    };\n    const __returned__ = {\n      searchForm,\n      tableData,\n      props,\n      proxy,\n      searchList,\n      page,\n      limit,\n      totalList,\n      tableLoading,\n      getList,\n      ref,\n      getCurrentInstance,\n      onMounted,\n      get sfzStatusEnums() {\n        return sfzStatusEnums;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      get ElMessageBox() {\n        return ElMessageBox;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "getCurrentInstance", "onMounted", "sfzStatusEnums", "ElMessage", "ElMessageBox", "searchForm", "phone", "invite", "level", "starttime", "endtime", "blow", "tableData", "props", "__props", "proxy", "value", "item", "getList", "searchList", "page", "limit", "totalList", "tableLoading", "res", "$http", "method", "url", "params", "code", "data", "total"], "sources": ["D:/WorkSpace/facai7/facai7_admin/src/views/userManage/components/userList/userLevelTwo.vue"], "sourcesContent": ["<template>\r\n  <adminTable\r\n    :tableLoading=\"tableLoading\"\r\n    :totalList=\"totalList\"\r\n    v-model:page=\"page\"\r\n    :hideResetButton=\"true\"\r\n    v-model:limit=\"limit\"\r\n    v-model:searchForm=\"searchForm\"\r\n    :tableData=\"tableData\"\r\n    @search=\"searchList\"\r\n  >\r\n    <template v-slot:form-inline-items>\r\n      <el-form-item>\r\n        <el-input\r\n          v-model=\"searchForm.phone\"\r\n          placeholder=\"手机号码\"\r\n          prop=\"phone\"\r\n          disabled\r\n        />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-select\r\n          v-model=\"searchForm.level\"\r\n          prop=\"invite\"\r\n          placeholder=\"用户关系\"\r\n          \r\n        >\r\n          <el-option label=\"下级\" :value=\"1\" />\r\n          <el-option label=\"下下级\" :value=\"2\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-input\r\n          v-model=\"searchForm.invite\"\r\n          placeholder=\"请输入邀请码\"\r\n          prop=\"invite\"\r\n          clearable\r\n        />\r\n      </el-form-item>\r\n\r\n      <el-form-item>\r\n        <el-col :span=\"11\">\r\n          <el-date-picker\r\n            v-model=\"searchForm.starttime\"\r\n            type=\"datetime\"\r\n            value-format=\"YYYY-MM-DD HH:mm:ss\"\r\n            placeholder=\"注册开始时间\"\r\n            style=\"width: 100%\"\r\n            prop=\"starttime\"\r\n          />\r\n        </el-col>\r\n        <el-col :span=\"2\" class=\"text-center\">\r\n          <span class=\"text-gray-500\">-</span>\r\n        </el-col>\r\n        <el-col :span=\"11\">\r\n          <el-date-picker\r\n            v-model=\"searchForm.endtime\"\r\n            type=\"datetime\"\r\n            value-format=\"YYYY-MM-DD HH:mm:ss\"\r\n            prop=\"endtime\"\r\n            placeholder=\"注册结束时间\"\r\n            style=\"width: 100%\"\r\n          />\r\n        </el-col>\r\n      </el-form-item>\r\n    </template>\r\n    <template v-slot:table>\r\n      <el-table-column prop=\"id\" label=\"ID\" width=\"60\" />\r\n      <el-table-column prop=\"sfz_name\" label=\"用户\" width=\"100\" />\r\n      <!-- <el-table-column prop=\"nickname\" label=\"昵称\" width=\"100\" /> -->\r\n      <el-table-column prop=\"phone\" label=\"手机号码\" width=\"100\" />\r\n      <el-table-column prop=\"invite\" label=\"邀请码\" width=\"70\" />\r\n      <el-table-column prop=\"level_name\" label=\"用户等级\" width=\"70\">\r\n        <template #default=\"scope\">\r\n          {{ scope.row.level_name }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"addtime\" label=\"注册时间\" width=\"280\">\r\n        <template #default=\"scope\">\r\n          注册时间：{{ scope.row.create_at }}\r\n          <br />\r\n          注册IP：{{ scope.row.register_ip }}\r\n          <br />\r\n          登录时间：{{ scope.row.login_time }}\r\n          <br />\r\n          登录地址：{{ scope.row.ip_address }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"会员统计\" width=\"180\">\r\n        <template #default=\"scope\">\r\n          余额：{{ scope.row.money }} <br />\r\n          冻结余额：{{ scope.row.frozen_money }}<br />\r\n          余额宝：{{ scope.row.yuebao_money }}<br />\r\n          充值金额：{{ scope.row.recharge_money }}<br />\r\n          提现金额：{{ scope.row.withdraw_money }}<br />\r\n          投资：{{ scope.row.invest_money }}<br />\r\n          积分：{{ scope.row.user_points }}<br />\r\n        </template>\r\n      </el-table-column>\r\n    </template>\r\n  </adminTable>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, getCurrentInstance, onMounted } from \"vue\";\r\nimport { sfzStatusEnums } from \"@/config/enums\";\r\nimport { ElMessage, ElMessageBox } from \"element-plus\";\r\n\r\nconst searchForm = ref({\r\n  phone: \"\",\r\n  invite: \"\",\r\n  level: \"\",\r\n  starttime: \"\",\r\n  endtime: \"\",\r\n  blow: 2,\r\n});\r\nconst tableData = ref([]);\r\n\r\nconst props = defineProps([\"item\", 'level']);\r\nconst { proxy } = getCurrentInstance();\r\n\r\nonMounted(() => {\r\n  searchForm.value.phone = props.item.phone;\r\n  searchForm.value.blow = props.level;\r\n  getList();\r\n});\r\n\r\nconst searchList = () => {\r\n  getList();\r\n};\r\n\r\nconst page = ref(1);\r\nconst limit = ref(10);\r\nconst totalList = ref(0);\r\nconst tableLoading = ref(false);\r\n\r\nconst getList = async () => {\r\n  tableLoading.value = true;\r\n  const res = await proxy.$http({\r\n    method: \"get\",\r\n    url: \"/user/getUserLists\",\r\n    params: {\r\n      page: page.value,\r\n      limit: limit.value,\r\n      ...searchForm.value,\r\n    },\r\n  });\r\n  tableLoading.value = false;\r\n  if (res.code == 0) {\r\n    tableData.value = res.data.data;\r\n    totalList.value = res.data.total;\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.table-handle-td {\r\n  display: flex;\r\n  cursor: pointer;\r\n  flex-wrap: wrap;\r\n  button {\r\n    margin-bottom: 10px;\r\n    margin-left: 0;\r\n    margin-right: 10px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAwGA,SAASA,GAAG,EAAEC,kBAAkB,EAAEC,SAAS,QAAQ,KAAK;AACxD,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,SAAS,EAAEC,YAAY,QAAQ,cAAc;;;;;;;;IAEtD,MAAMC,UAAU,GAAGN,GAAG,CAAC;MACrBO,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,EAAE;MACVC,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE,EAAE;MACbC,OAAO,EAAE,EAAE;MACXC,IAAI,EAAE;IACR,CAAC,CAAC;IACF,MAAMC,SAAS,GAAGb,GAAG,CAAC,EAAE,CAAC;IAEzB,MAAMc,KAAK,GAAGC,OAA8B;IAC5C,MAAM;MAAEC;IAAM,CAAC,GAAGf,kBAAkB,CAAC,CAAC;IAEtCC,SAAS,CAAC,MAAM;MACdI,UAAU,CAACW,KAAK,CAACV,KAAK,GAAGO,KAAK,CAACI,IAAI,CAACX,KAAK;MACzCD,UAAU,CAACW,KAAK,CAACL,IAAI,GAAGE,KAAK,CAACL,KAAK;MACnCU,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;IAEF,MAAMC,UAAU,GAAGA,CAAA,KAAM;MACvBD,OAAO,CAAC,CAAC;IACX,CAAC;IAED,MAAME,IAAI,GAAGrB,GAAG,CAAC,CAAC,CAAC;IACnB,MAAMsB,KAAK,GAAGtB,GAAG,CAAC,EAAE,CAAC;IACrB,MAAMuB,SAAS,GAAGvB,GAAG,CAAC,CAAC,CAAC;IACxB,MAAMwB,YAAY,GAAGxB,GAAG,CAAC,KAAK,CAAC;IAE/B,MAAMmB,OAAO,GAAG,MAAAA,CAAA,KAAY;MAC1BK,YAAY,CAACP,KAAK,GAAG,IAAI;MACzB,MAAMQ,GAAG,GAAG,MAAMT,KAAK,CAACU,KAAK,CAAC;QAC5BC,MAAM,EAAE,KAAK;QACbC,GAAG,EAAE,oBAAoB;QACzBC,MAAM,EAAE;UACNR,IAAI,EAAEA,IAAI,CAACJ,KAAK;UAChBK,KAAK,EAAEA,KAAK,CAACL,KAAK;UAClB,GAAGX,UAAU,CAACW;QAChB;MACF,CAAC,CAAC;MACFO,YAAY,CAACP,KAAK,GAAG,KAAK;MAC1B,IAAIQ,GAAG,CAACK,IAAI,IAAI,CAAC,EAAE;QACjBjB,SAAS,CAACI,KAAK,GAAGQ,GAAG,CAACM,IAAI,CAACA,IAAI;QAC/BR,SAAS,CAACN,KAAK,GAAGQ,GAAG,CAACM,IAAI,CAACC,KAAK;MAClC;IACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}