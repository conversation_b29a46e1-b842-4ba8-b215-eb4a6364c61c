<?php
namespace app\common\jobs;

use app\common\cache\RedisLock;
use app\common\repository\LevelLogRepository;
use app\common\repository\LevelRepository;
use app\common\repository\UserInfoRepository;
use app\common\repository\UserRelationRepository;
use app\common\repository\UserRepository;
use app\common\utils\Plan;
use think\facade\Db;
use think\facade\Log;
use think\queue\Job;


/**
 * 会员升级(进货)
 * Class LevelUpItemJob
 * @package app\job
 */
class LevelUpItemJob
{
    /**
     * 脚本对象
     * @var
     */
    protected $job;

    /**
     * 尝试次数
     * @var int
     */
    protected $tryMax = 5;

    /**
     * 变量信息
     * @var
     */
    protected $info;

    /**
     * 锁
     * @var
     */
    protected $RedisLock;

    /**
     * 执行
     * @param Job $job
     * @param $data
     */
    public function fire(Job $job, $data)
    {

        //redis锁
        $this->RedisLock  = new RedisLock();
        //对象
        $this->job        = $job;
        //信息
        $this->info       = $data;

        //最大尝试
        if (empty($this->maxTry()))
        {
            return;
        }

        try {

            //等锁
            if (empty($this->waitLock()))
            {
                return;
            }

            $this->exec();

            $job->delete();

        }catch (\Exception $exception)
        {
            Log::channel('job')->error($exception->getFile() . ' ' .  $exception->getLine() . ' ' . $exception->getMessage());
        }finally
        {
            $this->finishLock();
        }

    }


    /**
     * 执行
     */
    public function exec()
    {

        $where      = [];
        $where[]    = ['uid', '=', $this->info['uid']];
        $where[]    = ['level', '<=', 2];

        $UserRelationRepo   = new UserRelationRepository();
        $relations          = $UserRelationRepo->selectByCondition($where);

        if(empty($relations))
        {
            return;
        }


        foreach ($relations as $relation)
        {
            $UserRepo                   = new UserRepository();
            $parentUser                 = $UserRepo->findById($relation['top_id']);

            if (empty($parentUser))
            {
                continue;
            }

            $LevelRepo          = new LevelRepository();

            $where              = [];
            $where[]            = ['id', '>', $parentUser['level']];
            $level              = $LevelRepo->findByCondition($where,'*', ['id' => 'asc']);


            if(!$level)
            {
                continue;
            }

            $where    = [];
            $where[]  = ['top_id', '=', $parentUser['id']];
            $where[]  = ['level', '<=', 99];

            $UserRelationRepo = new UserRelationRepository();
            $members          = $UserRelationRepo->selectByCondition($where,'uid');

            $members          = array_column($members, 'uid');

            $history          = strtotime(date('Y-m-d 00:00:00', 1));
            $tomorrow         = strtotime(date('Y-m-d 00:00:00', strtotime('+1 day',time())));

            $UserInfoRepo     = new UserInfoRepository();

            //3总投资金额
            $teamMoney        = $UserInfoRepo->totalItemMoney($history,$tomorrow,0, $members);

            if($teamMoney < $level['team_invest'])
            {
                continue;
            }


            Db::startTrans();

            try
            {

                $update = [
                    'level'         => $level['id'],
                    'level_name'    => $level['title'],
                    'update_time'   => time()
                ];

                $res          = $UserRepo->updateById($parentUser['id'], $update);

                if (!$res)
                {
                    Db::rollback();
                    return;
                }


                $LevelLogRepo = new LevelLogRepository();

                $insert  = [
                    'uid'           => $parentUser['id'],
                    'username'      => $parentUser['username'],
                    'phone'         => $parentUser['phone'],
                    'is_test'       => $parentUser['is_test'],
                    'lv_id'         => $level['id'],
                    'lv_name'       => $level['title'],
                    'desc'          => '团队投资:从' . $parentUser['level_name'] . '升级到' . $level['title'],
                    'create_time'   => time(),
                    'update_time'   => time()
                ];


                $res = $LevelLogRepo->inserts($insert);

                if (!$res)
                {
                    Db::rollback();
                    return;
                }

                $Plan = new Plan();
                $res  = $Plan->level($parentUser['id']);

                // 提交事务
                Db::commit();


            } catch (\Exception $e)
            {

                // 回滚事务
                Db::rollback();
            }
        }

    }


    public function failed(array $data)
    {
        // ...任务达到最大重试次数后，失败了
    }

    /**
     * 最大尝试
     */
    protected function maxTry(): bool
    {

        if ($this->job->attempts() > $this->tryMax)
        {
            $this->job->delete();
            return false;
        }
        else
        {
            return true;
        }

    }

    /**
     * 等锁
     * @return bool
     */
    protected function waitLock(): bool
    {
        $RedisLock   = new RedisLock();

        $status      = $RedisLock->wait('LevelUpItemJob:' . $this->info['uid'],20,20);

        if (empty($status))
        {
            return false;
        }
        else
        {
            return true;
        }
    }


    /**
     * 释放锁
     * @return void
     */
    protected function finishLock()
    {
        $this->RedisLock->unLock('LevelUpItemJob:' . $this->info['uid']);
    }


}

