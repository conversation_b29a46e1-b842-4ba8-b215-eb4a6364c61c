{"ast": null, "code": "import baseGetTag from './_baseGetTag.js';\nimport isObjectLike from './isObjectLike.js';\n\n/** `Object#toString` result references. */\nvar weakSetTag = '[object WeakSet]';\n\n/**\n * Checks if `value` is classified as a `WeakSet` object.\n *\n * @static\n * @memberOf _\n * @since 4.3.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a weak set, else `false`.\n * @example\n *\n * _.isWeakSet(new WeakSet);\n * // => true\n *\n * _.isWeakSet(new Set);\n * // => false\n */\nfunction isWeakSet(value) {\n  return isObjectLike(value) && baseGetTag(value) == weakSetTag;\n}\nexport default isWeakSet;", "map": {"version": 3, "names": ["baseGetTag", "isObjectLike", "weakSetTag", "isWeakSet", "value"], "sources": ["D:/WorkSpace/facai7/facai7_admin/node_modules/lodash-es/isWeakSet.js"], "sourcesContent": ["import baseGetTag from './_baseGetTag.js';\nimport isObjectLike from './isObjectLike.js';\n\n/** `Object#toString` result references. */\nvar weakSetTag = '[object WeakSet]';\n\n/**\n * Checks if `value` is classified as a `WeakSet` object.\n *\n * @static\n * @memberOf _\n * @since 4.3.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a weak set, else `false`.\n * @example\n *\n * _.isWeakSet(new WeakSet);\n * // => true\n *\n * _.isWeakSet(new Set);\n * // => false\n */\nfunction isWeakSet(value) {\n  return isObjectLike(value) && baseGetTag(value) == weakSetTag;\n}\n\nexport default isWeakSet;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;AACzC,OAAOC,YAAY,MAAM,mBAAmB;;AAE5C;AACA,IAAIC,UAAU,GAAG,kBAAkB;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,KAAK,EAAE;EACxB,OAAOH,YAAY,CAACG,KAAK,CAAC,IAAIJ,UAAU,CAACI,KAAK,CAAC,IAAIF,UAAU;AAC/D;AAEA,eAAeC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}