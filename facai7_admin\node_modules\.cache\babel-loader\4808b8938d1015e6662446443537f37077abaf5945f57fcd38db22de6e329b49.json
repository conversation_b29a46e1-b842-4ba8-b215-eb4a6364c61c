{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, openBlock as _openBlock, createBlock as _createBlock } from \"vue\";\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_date_picker = _resolveComponent(\"el-date-picker\");\n  const _component_el_col = _resolveComponent(\"el-col\");\n  const _component_el_table_column = _resolveComponent(\"el-table-column\");\n  const _component_adminTable = _resolveComponent(\"adminTable\");\n  return _openBlock(), _createBlock(_component_adminTable, {\n    tableLoading: $setup.tableLoading,\n    totalList: $setup.totalList,\n    page: $setup.page,\n    \"onUpdate:page\": _cache[5] || (_cache[5] = $event => $setup.page = $event),\n    hideResetButton: true,\n    limit: $setup.limit,\n    \"onUpdate:limit\": _cache[6] || (_cache[6] = $event => $setup.limit = $event),\n    searchForm: $setup.searchForm,\n    \"onUpdate:searchForm\": _cache[7] || (_cache[7] = $event => $setup.searchForm = $event),\n    tableData: $setup.tableData,\n    onSearch: $setup.searchList\n  }, {\n    \"form-inline-items\": _withCtx(() => [_createVNode(_component_el_form_item, null, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.searchForm.phone,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.searchForm.phone = $event),\n        placeholder: \"手机号码\",\n        prop: \"phone\",\n        disabled: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, null, {\n      default: _withCtx(() => [_createVNode(_component_el_select, {\n        modelValue: $setup.searchForm.level,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.searchForm.level = $event),\n        prop: \"invite\",\n        placeholder: \"用户关系\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_option, {\n          label: \"下级\",\n          value: 1\n        }), _createVNode(_component_el_option, {\n          label: \"下下级\",\n          value: 2\n        })]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, null, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.searchForm.invite,\n        \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.searchForm.invite = $event),\n        placeholder: \"请输入邀请码\",\n        prop: \"invite\",\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, null, {\n      default: _withCtx(() => [_createVNode(_component_el_col, {\n        span: 11\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_date_picker, {\n          modelValue: $setup.searchForm.starttime,\n          \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.searchForm.starttime = $event),\n          type: \"datetime\",\n          \"value-format\": \"YYYY-MM-DD HH:mm:ss\",\n          placeholder: \"注册开始时间\",\n          style: {\n            \"width\": \"100%\"\n          },\n          prop: \"starttime\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 2,\n        class: \"text-center\"\n      }, {\n        default: _withCtx(() => _cache[8] || (_cache[8] = [_createElementVNode(\"span\", {\n          class: \"text-gray-500\"\n        }, \"-\", -1 /* CACHED */)])),\n        _: 1 /* STABLE */,\n        __: [8]\n      }), _createVNode(_component_el_col, {\n        span: 11\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_date_picker, {\n          modelValue: $setup.searchForm.endtime,\n          \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.searchForm.endtime = $event),\n          type: \"datetime\",\n          \"value-format\": \"YYYY-MM-DD HH:mm:ss\",\n          prop: \"endtime\",\n          placeholder: \"注册结束时间\",\n          style: {\n            \"width\": \"100%\"\n          }\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    table: _withCtx(() => [_createVNode(_component_el_table_column, {\n      prop: \"id\",\n      label: \"ID\",\n      width: \"60\"\n    }), _createVNode(_component_el_table_column, {\n      prop: \"sfz_name\",\n      label: \"用户名\",\n      width: \"100\"\n    }), _createCommentVNode(\" <el-table-column prop=\\\"nickname\\\" label=\\\"昵称\\\" width=\\\"100\\\" /> \"), _createVNode(_component_el_table_column, {\n      prop: \"phone\",\n      label: \"手机号码\",\n      width: \"100\"\n    }), _createVNode(_component_el_table_column, {\n      prop: \"invite\",\n      label: \"邀请码\",\n      width: \"70\"\n    }), _createVNode(_component_el_table_column, {\n      prop: \"level_name\",\n      label: \"用户等级\",\n      width: \"70\"\n    }, {\n      default: _withCtx(scope => [_createTextVNode(_toDisplayString(scope.row.level_name), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      prop: \"addtime\",\n      label: \"注册时间\",\n      width: \"280\"\n    }, {\n      default: _withCtx(scope => [_createTextVNode(\" 注册时间：\" + _toDisplayString(scope.row.create_at) + \" \", 1 /* TEXT */), _cache[9] || (_cache[9] = _createElementVNode(\"br\", null, null, -1 /* CACHED */)), _createTextVNode(\" 注册IP：\" + _toDisplayString(scope.row.register_ip) + \" \", 1 /* TEXT */), _cache[10] || (_cache[10] = _createElementVNode(\"br\", null, null, -1 /* CACHED */)), _createTextVNode(\" 登录时间：\" + _toDisplayString(scope.row.login_time) + \" \", 1 /* TEXT */), _cache[11] || (_cache[11] = _createElementVNode(\"br\", null, null, -1 /* CACHED */)), _createTextVNode(\" 登录地址：\" + _toDisplayString(scope.row.ip_address), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"会员统计\",\n      width: \"180\"\n    }, {\n      default: _withCtx(scope => [_createTextVNode(\" 余额：\" + _toDisplayString(scope.row.money) + \" \", 1 /* TEXT */), _cache[12] || (_cache[12] = _createElementVNode(\"br\", null, null, -1 /* CACHED */)), _createTextVNode(\" 冻结余额：\" + _toDisplayString(scope.row.frozen_money), 1 /* TEXT */), _cache[13] || (_cache[13] = _createElementVNode(\"br\", null, null, -1 /* CACHED */)), _createTextVNode(\" 余额宝：\" + _toDisplayString(scope.row.yuebao_money), 1 /* TEXT */), _cache[14] || (_cache[14] = _createElementVNode(\"br\", null, null, -1 /* CACHED */)), _createTextVNode(\" 充值金额：\" + _toDisplayString(scope.row.recharge_money), 1 /* TEXT */), _cache[15] || (_cache[15] = _createElementVNode(\"br\", null, null, -1 /* CACHED */)), _createTextVNode(\" 提现金额：\" + _toDisplayString(scope.row.withdraw_money), 1 /* TEXT */), _cache[16] || (_cache[16] = _createElementVNode(\"br\", null, null, -1 /* CACHED */)), _createTextVNode(\" 投资：\" + _toDisplayString(scope.row.invest_money), 1 /* TEXT */), _cache[17] || (_cache[17] = _createElementVNode(\"br\", null, null, -1 /* CACHED */)), _createTextVNode(\" 积分：\" + _toDisplayString(scope.row.user_points), 1 /* TEXT */), _cache[18] || (_cache[18] = _createElementVNode(\"br\", null, null, -1 /* CACHED */))]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"tableLoading\", \"totalList\", \"page\", \"limit\", \"searchForm\", \"tableData\"]);\n}", "map": {"version": 3, "names": ["_createBlock", "_component_adminTable", "tableLoading", "$setup", "totalList", "page", "$event", "hideResetButton", "limit", "searchForm", "tableData", "onSearch", "searchList", "_withCtx", "_createVNode", "_component_el_form_item", "_component_el_input", "phone", "placeholder", "prop", "disabled", "_component_el_select", "level", "_component_el_option", "label", "value", "invite", "clearable", "_component_el_col", "span", "_component_el_date_picker", "starttime", "type", "style", "class", "_cache", "_createElementVNode", "endtime", "table", "_component_el_table_column", "width", "_createCommentVNode", "default", "scope", "row", "level_name", "_toDisplayString", "create_at", "register_ip", "login_time", "ip_address", "money", "frozen_money", "yuebao_money", "recharge_money", "withdraw_money", "invest_money", "user_points"], "sources": ["D:\\WorkSpace\\facai7\\facai7_admin\\src\\views\\userManage\\components\\userList\\userLevelOne.vue"], "sourcesContent": ["<template>\r\n  <adminTable\r\n    :tableLoading=\"tableLoading\"\r\n    :totalList=\"totalList\"\r\n    v-model:page=\"page\"\r\n    :hideResetButton=\"true\"\r\n    v-model:limit=\"limit\"\r\n    v-model:searchForm=\"searchForm\"\r\n    :tableData=\"tableData\"\r\n    @search=\"searchList\"\r\n  >\r\n    <template v-slot:form-inline-items>\r\n      <el-form-item>\r\n        <el-input\r\n          v-model=\"searchForm.phone\"\r\n          placeholder=\"手机号码\"\r\n          prop=\"phone\"\r\n          disabled\r\n        />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-select\r\n          v-model=\"searchForm.level\"\r\n          prop=\"invite\"\r\n          placeholder=\"用户关系\"\r\n          \r\n        >\r\n          <el-option label=\"下级\" :value=\"1\" />\r\n          <el-option label=\"下下级\" :value=\"2\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-input\r\n          v-model=\"searchForm.invite\"\r\n          placeholder=\"请输入邀请码\"\r\n          prop=\"invite\"\r\n          clearable\r\n        />\r\n      </el-form-item>\r\n\r\n      <el-form-item>\r\n        <el-col :span=\"11\">\r\n          <el-date-picker\r\n            v-model=\"searchForm.starttime\"\r\n            type=\"datetime\"\r\n            value-format=\"YYYY-MM-DD HH:mm:ss\"\r\n            placeholder=\"注册开始时间\"\r\n            style=\"width: 100%\"\r\n            prop=\"starttime\"\r\n          />\r\n        </el-col>\r\n        <el-col :span=\"2\" class=\"text-center\">\r\n          <span class=\"text-gray-500\">-</span>\r\n        </el-col>\r\n        <el-col :span=\"11\">\r\n          <el-date-picker\r\n            v-model=\"searchForm.endtime\"\r\n            type=\"datetime\"\r\n            value-format=\"YYYY-MM-DD HH:mm:ss\"\r\n            prop=\"endtime\"\r\n            placeholder=\"注册结束时间\"\r\n            style=\"width: 100%\"\r\n          />\r\n        </el-col>\r\n      </el-form-item>\r\n    </template>\r\n    <template v-slot:table>\r\n      <el-table-column prop=\"id\" label=\"ID\" width=\"60\" />\r\n      <el-table-column prop=\"sfz_name\" label=\"用户名\" width=\"100\" />\r\n      <!-- <el-table-column prop=\"nickname\" label=\"昵称\" width=\"100\" /> -->\r\n      <el-table-column prop=\"phone\" label=\"手机号码\" width=\"100\" />\r\n      <el-table-column prop=\"invite\" label=\"邀请码\" width=\"70\" />\r\n      <el-table-column prop=\"level_name\" label=\"用户等级\" width=\"70\">\r\n        <template #default=\"scope\">\r\n          {{ scope.row.level_name }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"addtime\" label=\"注册时间\" width=\"280\">\r\n        <template #default=\"scope\">\r\n          注册时间：{{ scope.row.create_at }}\r\n          <br />\r\n          注册IP：{{ scope.row.register_ip }}\r\n          <br />\r\n          登录时间：{{ scope.row.login_time }}\r\n          <br />\r\n          登录地址：{{ scope.row.ip_address }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"会员统计\" width=\"180\">\r\n        <template #default=\"scope\">\r\n          余额：{{ scope.row.money }} <br />\r\n          冻结余额：{{ scope.row.frozen_money }}<br />\r\n          余额宝：{{ scope.row.yuebao_money }}<br />\r\n          充值金额：{{ scope.row.recharge_money }}<br />\r\n          提现金额：{{ scope.row.withdraw_money }}<br />\r\n          投资：{{ scope.row.invest_money }}<br />\r\n          积分：{{ scope.row.user_points }}<br />\r\n        </template>\r\n      </el-table-column>\r\n    </template>\r\n  </adminTable>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, getCurrentInstance, onMounted } from \"vue\";\r\nimport { sfzStatusEnums } from \"@/config/enums\";\r\nimport { ElMessage, ElMessageBox } from \"element-plus\";\r\n\r\nconst searchForm = ref({\r\n  phone: \"\",\r\n  invite: \"\",\r\n  level: \"\",\r\n  starttime: \"\",\r\n  endtime: \"\",\r\n  blow: 1,\r\n});\r\nconst tableData = ref([]);\r\n\r\nconst props = defineProps([\"item\"]);\r\nconst { proxy } = getCurrentInstance();\r\n\r\nonMounted(() => {\r\n  searchForm.value.phone = props.item.phone;\r\n  searchForm.value.blow = 1;\r\n  getList();\r\n});\r\n\r\nconst searchList = () => {\r\n  getList();\r\n};\r\n\r\nconst page = ref(1);\r\nconst limit = ref(10);\r\nconst totalList = ref(0);\r\nconst tableLoading = ref(false);\r\n\r\nconst getList = async () => {\r\n  tableLoading.value = true;\r\n  const res = await proxy.$http({\r\n    method: \"get\",\r\n    url: \"/user/getUserLists\",\r\n    params: {\r\n      page: page.value,\r\n      limit: limit.value,\r\n      ...searchForm.value,\r\n    },\r\n  });\r\n  tableLoading.value = false;\r\n  if (res.code == 0) {\r\n    tableData.value = res.data.data;\r\n    totalList.value = res.data.total;\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.table-handle-td {\r\n  display: flex;\r\n  cursor: pointer;\r\n  flex-wrap: wrap;\r\n  button {\r\n    margin-bottom: 10px;\r\n    margin-left: 0;\r\n    margin-right: 10px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;uBACEA,YAAA,CAmGaC,qBAAA;IAlGVC,YAAY,EAAEC,MAAA,CAAAD,YAAY;IAC1BE,SAAS,EAAED,MAAA,CAAAC,SAAS;IACbC,IAAI,EAAEF,MAAA,CAAAE,IAAI;yDAAJF,MAAA,CAAAE,IAAI,GAAAC,MAAA;IACjBC,eAAe,EAAE,IAAI;IACdC,KAAK,EAAEL,MAAA,CAAAK,KAAK;0DAALL,MAAA,CAAAK,KAAK,GAAAF,MAAA;IACZG,UAAU,EAAEN,MAAA,CAAAM,UAAU;+DAAVN,MAAA,CAAAM,UAAU,GAAAH,MAAA;IAC7BI,SAAS,EAAEP,MAAA,CAAAO,SAAS;IACpBC,QAAM,EAAER,MAAA,CAAAS;;IAEQ,mBAAiB,EAAAC,QAAA,CAChC,MAOe,CAPfC,YAAA,CAOeC,uBAAA;wBANb,MAKE,CALFD,YAAA,CAKEE,mBAAA;oBAJSb,MAAA,CAAAM,UAAU,CAACQ,KAAK;mEAAhBd,MAAA,CAAAM,UAAU,CAACQ,KAAK,GAAAX,MAAA;QACzBY,WAAW,EAAC,MAAM;QAClBC,IAAI,EAAC,OAAO;QACZC,QAAQ,EAAR;;;QAGJN,YAAA,CAUeC,uBAAA;wBATb,MAQY,CARZD,YAAA,CAQYO,oBAAA;oBAPDlB,MAAA,CAAAM,UAAU,CAACa,KAAK;mEAAhBnB,MAAA,CAAAM,UAAU,CAACa,KAAK,GAAAhB,MAAA;QACzBa,IAAI,EAAC,QAAQ;QACbD,WAAW,EAAC;;0BAGZ,MAAmC,CAAnCJ,YAAA,CAAmCS,oBAAA;UAAxBC,KAAK,EAAC,IAAI;UAAEC,KAAK,EAAE;YAC9BX,YAAA,CAAoCS,oBAAA;UAAzBC,KAAK,EAAC,KAAK;UAAEC,KAAK,EAAE;;;;;QAGnCX,YAAA,CAOeC,uBAAA;wBANb,MAKE,CALFD,YAAA,CAKEE,mBAAA;oBAJSb,MAAA,CAAAM,UAAU,CAACiB,MAAM;mEAAjBvB,MAAA,CAAAM,UAAU,CAACiB,MAAM,GAAApB,MAAA;QAC1BY,WAAW,EAAC,QAAQ;QACpBC,IAAI,EAAC,QAAQ;QACbQ,SAAS,EAAT;;;QAIJb,YAAA,CAwBeC,uBAAA;wBAvBb,MASS,CATTD,YAAA,CASSc,iBAAA;QATAC,IAAI,EAAE;MAAE;0BACf,MAOE,CAPFf,YAAA,CAOEgB,yBAAA;sBANS3B,MAAA,CAAAM,UAAU,CAACsB,SAAS;qEAApB5B,MAAA,CAAAM,UAAU,CAACsB,SAAS,GAAAzB,MAAA;UAC7B0B,IAAI,EAAC,UAAU;UACf,cAAY,EAAC,qBAAqB;UAClCd,WAAW,EAAC,QAAQ;UACpBe,KAAmB,EAAnB;YAAA;UAAA,CAAmB;UACnBd,IAAI,EAAC;;;UAGTL,YAAA,CAESc,iBAAA;QAFAC,IAAI,EAAE,CAAC;QAAEK,KAAK,EAAC;;0BACtB,MAAoCC,MAAA,QAAAA,MAAA,OAApCC,mBAAA,CAAoC;UAA9BF,KAAK,EAAC;QAAe,GAAC,GAAC,mB;;;UAE/BpB,YAAA,CASSc,iBAAA;QATAC,IAAI,EAAE;MAAE;0BACf,MAOE,CAPFf,YAAA,CAOEgB,yBAAA;sBANS3B,MAAA,CAAAM,UAAU,CAAC4B,OAAO;qEAAlBlC,MAAA,CAAAM,UAAU,CAAC4B,OAAO,GAAA/B,MAAA;UAC3B0B,IAAI,EAAC,UAAU;UACf,cAAY,EAAC,qBAAqB;UAClCb,IAAI,EAAC,SAAS;UACdD,WAAW,EAAC,QAAQ;UACpBe,KAAmB,EAAnB;YAAA;UAAA;;;;;;IAKSK,KAAK,EAAAzB,QAAA,CACpB,MAAmD,CAAnDC,YAAA,CAAmDyB,0BAAA;MAAlCpB,IAAI,EAAC,IAAI;MAACK,KAAK,EAAC,IAAI;MAACgB,KAAK,EAAC;QAC5C1B,YAAA,CAA2DyB,0BAAA;MAA1CpB,IAAI,EAAC,UAAU;MAACK,KAAK,EAAC,KAAK;MAACgB,KAAK,EAAC;QACnDC,mBAAA,sEAAmE,EACnE3B,YAAA,CAAyDyB,0BAAA;MAAxCpB,IAAI,EAAC,OAAO;MAACK,KAAK,EAAC,MAAM;MAACgB,KAAK,EAAC;QACjD1B,YAAA,CAAwDyB,0BAAA;MAAvCpB,IAAI,EAAC,QAAQ;MAACK,KAAK,EAAC,KAAK;MAACgB,KAAK,EAAC;QACjD1B,YAAA,CAIkByB,0BAAA;MAJDpB,IAAI,EAAC,YAAY;MAACK,KAAK,EAAC,MAAM;MAACgB,KAAK,EAAC;;MACzCE,OAAO,EAAA7B,QAAA,CACU8B,KADH,K,kCACpBA,KAAK,CAACC,GAAG,CAACC,UAAU,iB;;QAG3B/B,YAAA,CAUkByB,0BAAA;MAVDpB,IAAI,EAAC,SAAS;MAACK,KAAK,EAAC,MAAM;MAACgB,KAAK,EAAC;;MACtCE,OAAO,EAAA7B,QAAA,CACX8B,KADkB,K,iBAAE,QACpB,GAAAG,gBAAA,CAAGH,KAAK,CAACC,GAAG,CAACG,SAAS,IAAG,GAC9B,iB,0BAAAX,mBAAA,CAAM,qC,iBAAA,QACD,GAAAU,gBAAA,CAAGH,KAAK,CAACC,GAAG,CAACI,WAAW,IAAG,GAChC,iB,4BAAAZ,mBAAA,CAAM,qC,iBAAA,QACD,GAAAU,gBAAA,CAAGH,KAAK,CAACC,GAAG,CAACK,UAAU,IAAG,GAC/B,iB,4BAAAb,mBAAA,CAAM,qC,iBAAA,QACD,GAAAU,gBAAA,CAAGH,KAAK,CAACC,GAAG,CAACM,UAAU,iB;;QAGhCpC,YAAA,CAUkByB,0BAAA;MAVDf,KAAK,EAAC,MAAM;MAACgB,KAAK,EAAC;;MACvBE,OAAO,EAAA7B,QAAA,CACb8B,KADoB,K,iBAAE,MACtB,GAAAG,gBAAA,CAAGH,KAAK,CAACC,GAAG,CAACO,KAAK,IAAG,GAAC,iB,4BAAAf,mBAAA,CAAM,qC,iBAAA,QAC1B,GAAAU,gBAAA,CAAGH,KAAK,CAACC,GAAG,CAACQ,YAAY,kB,4BAAGhB,mBAAA,CAAM,qC,iBAAA,OACnC,GAAAU,gBAAA,CAAGH,KAAK,CAACC,GAAG,CAACS,YAAY,kB,4BAAGjB,mBAAA,CAAM,qC,iBAAA,QACjC,GAAAU,gBAAA,CAAGH,KAAK,CAACC,GAAG,CAACU,cAAc,kB,4BAAGlB,mBAAA,CAAM,qC,iBAAA,QACpC,GAAAU,gBAAA,CAAGH,KAAK,CAACC,GAAG,CAACW,cAAc,kB,4BAAGnB,mBAAA,CAAM,qC,iBAAA,MACtC,GAAAU,gBAAA,CAAGH,KAAK,CAACC,GAAG,CAACY,YAAY,kB,4BAAGpB,mBAAA,CAAM,qC,iBAAA,MAClC,GAAAU,gBAAA,CAAGH,KAAK,CAACC,GAAG,CAACa,WAAW,kB,4BAAGrB,mBAAA,CAAM,oC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}