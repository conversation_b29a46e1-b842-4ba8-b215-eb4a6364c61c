{"ast": null, "code": "import { nextTick, onMounted, ref, getCurrentInstance } from \"vue\";\nimport { rolesEnums, getLabelByVal, sendStatusEnums } from \"@/config/enums\";\nimport moment from \"moment\";\nexport default {\n  __name: 'editPop',\n  props: [\"item\", \"formType\"],\n  setup(__props, {\n    expose: __expose\n  }) {\n    const form = ref({\n      username: \"\",\n      phone: \"\",\n      deliver_order_no: \"\",\n      deliver_title: \"\",\n      address_name: \"\",\n      address_phone: \"\",\n      address_city: \"\",\n      address_place: \"\",\n      status: \"\",\n      goods_id: \"\",\n      deliver_title: \"\",\n      deliver_time: \"\"\n    });\n    const props = __props;\n    onMounted(() => {\n      nextTick(() => {\n        form.value = Object.assign(form, props.item);\n        if (form.value.deliver_time) {\n          form.value.deliver_time = moment(new Date(props.item.deliver_time * 1000)).format('YYYY-MM-DD HH:mm:ss');\n        }\n      });\n      getGoodsList();\n    });\n    const {\n      proxy\n    } = getCurrentInstance();\n    const goodsList = ref([]);\n    const getGoodsList = async () => {\n      const res = await proxy.$http({\n        method: \"get\",\n        url: \"/Goods/getGoodsLists\",\n        params: {\n          limit: 1000000\n        }\n      });\n      if (res.code == 0) {\n        goodsList.value = res.data.data;\n      }\n    };\n    __expose({\n      form\n    });\n    const __returned__ = {\n      form,\n      props,\n      proxy,\n      goodsList,\n      getGoodsList,\n      nextTick,\n      onMounted,\n      ref,\n      getCurrentInstance,\n      get rolesEnums() {\n        return rolesEnums;\n      },\n      get getLabelByVal() {\n        return getLabelByVal;\n      },\n      get sendStatusEnums() {\n        return sendStatusEnums;\n      },\n      get moment() {\n        return moment;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["nextTick", "onMounted", "ref", "getCurrentInstance", "rolesEnums", "getLabelByVal", "sendStatusEnums", "moment", "form", "username", "phone", "deliver_order_no", "deliver_title", "address_name", "address_phone", "address_city", "address_place", "status", "goods_id", "deliver_time", "props", "__props", "value", "Object", "assign", "item", "Date", "format", "getGoodsList", "proxy", "goodsList", "res", "$http", "method", "url", "params", "limit", "code", "data", "__expose"], "sources": ["D:/WorkSpace/facai7/facai7_admin/src/views/goodsManage/components/goodsRecords/editPop.vue"], "sourcesContent": ["<template>\r\n  <el-form\r\n    label-width=\"100px\"\r\n    :inline=\"true\"\r\n    :model=\"form\"\r\n    class=\"demo-form-inline\"\r\n  >\r\n    <h4 class=\"form-title\">买家信息</h4>\r\n    <el-form-item label=\"用户名\" v-if=\"formType !== 'add'\" required>\r\n      <el-input v-model=\"form.username\" clearable disabled />\r\n    </el-form-item>\r\n    <el-form-item label=\"手机\" required>\r\n      <el-input v-model=\"form.phone\" :disabled=\"formType != 'add'\" clearable />\r\n    </el-form-item>\r\n    <!-- <el-form-item label=\"订单号\" required>\r\n      <el-input v-model=\"form.deliver_order_no\" clearable />\r\n    </el-form-item> -->\r\n    <!-- <el-form-item label=\"商品名称\"  required>\r\n      <el-input v-model=\"form.goods_title\" :disabled=\"formType != 'add'\" clearable />\r\n    </el-form-item> -->\r\n    <!-- <el-form-item label=\"姓名\"  required>\r\n      <el-input v-model=\"form.address_name\" clearable :disabled=\"formType != 'add'\" />\r\n    </el-form-item> -->\r\n    <!-- <el-form-item label=\"收货手机号\" required>\r\n      <el-input v-model=\"form.address_phone\" :disabled=\"formType != 'add'\" clearable />\r\n    </el-form-item> -->\r\n    <el-form-item label=\"商品\" prop=\"goods_id\">\r\n      <el-select v-model=\"form.goods_id\" placeholder=\"请选择商品\" clearable>\r\n        <el-option\r\n          v-for=\"item in goodsList\"\r\n          :label=\"item.title\"\r\n          :key=\"item.title\"\r\n          :value=\"item.id\"\r\n        />\r\n      </el-select>\r\n    </el-form-item>\r\n    <!-- <el-form-item label=\"市区\" required>\r\n      <el-input v-model=\"form.address_city\" :disabled=\"formType != 'add'\" clearable />\r\n    </el-form-item> -->\r\n    <!-- <el-form-item label=\"详细地址\" required>\r\n      <el-input v-model=\"form.address_place\" :disabled=\"formType != 'add'\" clearable />\r\n    </el-form-item> -->\r\n\r\n    <h4 class=\"form-title\">发货信息</h4>\r\n    <el-form-item label=\"订单状态\" required>\r\n      <el-select v-model=\"form.status\" placeholder=\"\" clearable>\r\n        <el-option\r\n          v-for=\"item in sendStatusEnums\"\r\n          :label=\"item.label\"\r\n          :value=\"item.value\"\r\n        />\r\n      </el-select>\r\n    </el-form-item>\r\n    <el-form-item label=\"发货公司\" required>\r\n      <el-input v-model=\"form.deliver_title\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"发货单号\" required>\r\n      <el-input v-model=\"form.deliver_order_no\" clearable />\r\n    </el-form-item>\r\n    <!-- <el-form-item label=\"发货时间\" required>\r\n      <el-date-picker\r\n        v-model=\"form.deliver_time\"\r\n        type=\"datetime\"\r\n        value-format=\"YYYY-MM-DD HH:mm:ss\"\r\n        placeholder=\"发货时间\"\r\n        style=\"width: 100%\"\r\n      />\r\n    </el-form-item> -->\r\n  </el-form>\r\n</template>\r\n\r\n<script setup>\r\nimport { nextTick, onMounted, ref, getCurrentInstance } from \"vue\";\r\nimport { rolesEnums, getLabelByVal, sendStatusEnums } from \"@/config/enums\";\r\nimport moment from \"moment\";\r\n\r\nconst form = ref({\r\n  username: \"\",\r\n  phone: \"\",\r\n  deliver_order_no: \"\",\r\n  deliver_title: \"\",\r\n  address_name: \"\",\r\n  address_phone: \"\",\r\n  address_city: \"\",\r\n  address_place: \"\",\r\n  status: \"\",\r\n  goods_id: \"\",\r\n  deliver_title: \"\",\r\n  deliver_time: \"\",\r\n});\r\nconst props = defineProps([\"item\", \"formType\"]);\r\n\r\nonMounted(() => {\r\n  nextTick(() => {\r\n    form.value = Object.assign(form, props.item);\r\n    if (form.value.deliver_time) {\r\n      form.value.deliver_time = moment(new Date(props.item.deliver_time * 1000)).format('YYYY-MM-DD HH:mm:ss')\r\n    }\r\n  });\r\n  getGoodsList();\r\n});\r\n\r\nconst { proxy } = getCurrentInstance();\r\n\r\nconst goodsList = ref([]);\r\n\r\nconst getGoodsList = async () => {\r\n  const res = await proxy.$http({\r\n    method: \"get\",\r\n    url: \"/Goods/getGoodsLists\",\r\n    params: {\r\n      limit: 1000000\r\n    }\r\n  });\r\n  if (res.code == 0) {\r\n    goodsList.value = res.data.data;\r\n  }\r\n};\r\n\r\ndefineExpose({ form });\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.demo-form-inline {\r\n  justify-content: flex-start;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n.demo-form-inline .el-input {\r\n  --el-input-width: 220px;\r\n}\r\n\r\n.demo-form-inline .el-select {\r\n  --el-select-width: 220px;\r\n}\r\n/deep/ .el-radio-group {\r\n  width: 220px;\r\n}\r\n.form-title {\r\n  text-align: left;\r\n  padding-left: 30px;\r\n  margin: 20px auto 10px;\r\n  height: 44px;\r\n  background-color: #f2f2f2;\r\n  border-radius: 5px;\r\n  line-height: 44px;\r\n  width: 100%;\r\n}\r\n/deep/ .el-form-item {\r\n  align-items: flex-start;\r\n}\r\n</style>\r\n"], "mappings": "AAwEA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,GAAG,EAAEC,kBAAkB,QAAQ,KAAK;AAClE,SAASC,UAAU,EAAEC,aAAa,EAAEC,eAAe,QAAQ,gBAAgB;AAC3E,OAAOC,MAAM,MAAM,QAAQ;;;;;;;IAE3B,MAAMC,IAAI,GAAGN,GAAG,CAAC;MACfO,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,gBAAgB,EAAE,EAAE;MACpBC,aAAa,EAAE,EAAE;MACjBC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,EAAE;MACZN,aAAa,EAAE,EAAE;MACjBO,YAAY,EAAE;IAChB,CAAC,CAAC;IACF,MAAMC,KAAK,GAAGC,OAAiC;IAE/CpB,SAAS,CAAC,MAAM;MACdD,QAAQ,CAAC,MAAM;QACbQ,IAAI,CAACc,KAAK,GAAGC,MAAM,CAACC,MAAM,CAAChB,IAAI,EAAEY,KAAK,CAACK,IAAI,CAAC;QAC5C,IAAIjB,IAAI,CAACc,KAAK,CAACH,YAAY,EAAE;UAC3BX,IAAI,CAACc,KAAK,CAACH,YAAY,GAAGZ,MAAM,CAAC,IAAImB,IAAI,CAACN,KAAK,CAACK,IAAI,CAACN,YAAY,GAAG,IAAI,CAAC,CAAC,CAACQ,MAAM,CAAC,qBAAqB,CAAC;QAC1G;MACF,CAAC,CAAC;MACFC,YAAY,CAAC,CAAC;IAChB,CAAC,CAAC;IAEF,MAAM;MAAEC;IAAM,CAAC,GAAG1B,kBAAkB,CAAC,CAAC;IAEtC,MAAM2B,SAAS,GAAG5B,GAAG,CAAC,EAAE,CAAC;IAEzB,MAAM0B,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,MAAMG,GAAG,GAAG,MAAMF,KAAK,CAACG,KAAK,CAAC;QAC5BC,MAAM,EAAE,KAAK;QACbC,GAAG,EAAE,sBAAsB;QAC3BC,MAAM,EAAE;UACNC,KAAK,EAAE;QACT;MACF,CAAC,CAAC;MACF,IAAIL,GAAG,CAACM,IAAI,IAAI,CAAC,EAAE;QACjBP,SAAS,CAACR,KAAK,GAAGS,GAAG,CAACO,IAAI,CAACA,IAAI;MACjC;IACF,CAAC;IAEDC,QAAY,CAAC;MAAE/B;IAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}