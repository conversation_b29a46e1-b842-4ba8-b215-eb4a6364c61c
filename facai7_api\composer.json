{"name": "topthink/think", "description": "the new thinkphp framework", "type": "project", "keywords": ["framework", "thinkphp", "ORM"], "homepage": "http://thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=7.2.5", "topthink/framework": "^6.1.0", "topthink/think-orm": "^2.0", "topthink/think-multi-app": "^1.0", "topthink/think-view": "^1.0", "topthink/think-captcha": "^3.0", "endroid/qr-code": "3.*", "topthink/think-migration": "^3.1", "zhuzhichao/ip-location-zh": "^2.4", "topthink/think-helper": "^3.1", "topthink/think-queue": "^3.0", "guzzlehttp/guzzle": "^7.9", "ext-iconv": "*", "ext-openssl": "*", "ext-gd": "*", "ext-curl": "*", "ext-json": "*", "topthink/think-filesystem": "^2.0", "vlucas/phpdotenv": "^5.6", "phpgangsta/googleauthenticator": "dev-master", "aliyuncs/oss-sdk-php": "^2.7", "alibabacloud/dysmsapi-20170525": "3.1.1", "firebase/php-jwt": "^6.10", "ext-pdo": "*", "phpoffice/phpspreadsheet": "^1.29", "ext-bcmath": "*"}, "require-dev": {"symfony/var-dumper": "^4.2", "topthink/think-trace": "^1.0"}, "autoload": {"psr-4": {"app\\": "app"}, "psr-0": {"": "extend/"}}, "config": {"preferred-install": "dist"}, "scripts": {"post-autoload-dump": ["@echo 'Skipping service discovery for now'"]}}