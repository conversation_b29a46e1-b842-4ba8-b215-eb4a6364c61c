{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createElementVNode as _createElementVNode, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  style: {\n    \"border\": \"1px solid #ccc\"\n  }\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_Toolbar = _resolveComponent(\"Toolbar\");\n  const _component_Editor = _resolveComponent(\"Editor\");\n  return _openBlock(), _createElementBlock(_Fragment, null, [_createVNode(_component_el_form, {\n    \"label-width\": \"300px\",\n    inline: true,\n    model: $setup.form,\n    class: \"demo-form-inline\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_form_item, {\n      label: \"电话号码(,分割)输入all发给全部用户\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.users,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.form.users = $event),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"标题\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.title,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.form.title = $event),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\"]), _createElementVNode(\"div\", _hoisted_1, [_createVNode(_component_Toolbar, {\n    style: {\n      \"border-bottom\": \"1px solid #ccc\"\n    },\n    editor: $setup.editorRef,\n    defaultConfig: $setup.toolbarConfig,\n    mode: $setup.mode\n  }, null, 8 /* PROPS */, [\"editor\", \"mode\"]), _createVNode(_component_Editor, {\n    style: {\n      \"height\": \"500px\",\n      \"overflow-y\": \"hidden\"\n    },\n    modelValue: $setup.form.content,\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.form.content = $event),\n    defaultConfig: $setup.editorConfig,\n    mode: $setup.mode,\n    onOnCreated: $setup.handleCreated\n  }, null, 8 /* PROPS */, [\"modelValue\", \"mode\"])])], 64 /* STABLE_FRAGMENT */);\n}", "map": {"version": 3, "names": ["style", "_createVNode", "_component_el_form", "inline", "model", "$setup", "form", "class", "_component_el_form_item", "label", "required", "_component_el_input", "users", "$event", "clearable", "title", "_createElementVNode", "_hoisted_1", "_component_Toolbar", "editor", "editor<PERSON><PERSON>", "defaultConfig", "toolbarConfig", "mode", "_component_Editor", "content", "editorConfig", "onOnCreated", "handleCreated"], "sources": ["D:\\WorkSpace\\facai7\\facai7_admin\\src\\views\\operationManage\\components\\messageLists\\editPop.vue"], "sourcesContent": ["<template>\r\n  <el-form\r\n    label-width=\"300px\"\r\n    :inline=\"true\"\r\n    :model=\"form\"\r\n    class=\"demo-form-inline\"\r\n  >\r\n    <el-form-item label=\"电话号码(,分割)输入all发给全部用户\" required>\r\n      <el-input v-model=\"form.users\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"标题\" required>\r\n      <el-input v-model=\"form.title\" clearable />\r\n    </el-form-item>\r\n  </el-form>\r\n  <div style=\"border: 1px solid #ccc\">\r\n      <Toolbar\r\n        style=\"border-bottom: 1px solid #ccc\"\r\n        :editor=\"editorRef\"\r\n        :defaultConfig=\"toolbarConfig\"\r\n        :mode=\"mode\"\r\n      />\r\n      <Editor\r\n        style=\"height: 500px; overflow-y: hidden\"\r\n        v-model=\"form.content\"\r\n        :defaultConfig=\"editorConfig\"\r\n        :mode=\"mode\"\r\n        @onCreated=\"handleCreated\"\r\n      />\r\n    </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { rolesEnums } from \"@/config/enums\";\r\nimport {\r\n  onBeforeUnmount,\r\n  nextTick,\r\n  ref,\r\n  shallowRef,\r\n  onMounted,\r\n  getCurrentInstance,\r\n} from \"vue\";\r\nimport fileUploadHoook from \"@/hooks/fileUpload\";\r\nimport { htmlDecodeByRegExp } from \"@/utils/utils\";\r\nimport { getTokenAUTH } from \"@/utils/auth\";\r\n\r\nconst form = ref({\r\n  users: \"\",\r\n  title: \"\",\r\n  content: \"\",\r\n});\r\nconst props = defineProps([\"item\"]);\r\n\r\nconst headers = ref({})\r\nconst { proxy } = getCurrentInstance();\r\nonMounted(() => {\r\n  headers.value['Accept-Token'] = getTokenAUTH()\r\n  nextTick(() => {\r\n    form.value = Object.assign(form.value, props.item);\r\n    form.value.content = htmlDecodeByRegExp(props.item.content || '');\r\n\r\n  });\r\n});\r\n\r\n// 编辑器实例，必须用 shallowRef\r\nconst editorRef = shallowRef();\r\n\r\n// 内容 HTML\r\nconst valueHtml = ref(\"<p>hello</p>\");\r\nconst mode = ref(\"default\");\r\n\r\nconst toolbarConfig = {};\r\nconst editorConfig = {\r\n  placeholder: \"请输入内容...\",\r\n  MENU_CONF: {\r\n    uploadImage: {\r\n      fieldName: \"file\",\r\n      maxFileSize: 10 * 1024 * 1024, // 10M\r\n      server: proxy.BASE_API_URL + \"index/uploadX\",\r\n      headers: {\r\n        \"Accept-Token\": getTokenAUTH(),\r\n      },\r\n      customInsert(res, insertFn) {\r\n        console.log(res)\r\n        const url = proxy.IMG_BASE_URL + res.data.url;\r\n        const alt = res.data.alt\r\n        const href = res.data.href\r\n        insertFn(url, alt, href);\r\n      },\r\n      onError(file, err, res) {\r\n        console.log(`${file.name} 上传出错`, err, res)\r\n      }\r\n    },\r\n  },\r\n};\r\n\r\n// 组件销毁时，也及时销毁编辑器\r\nonBeforeUnmount(() => {\r\n  const editor = editorRef.value;\r\n  if (editor == null) return;\r\n  editor.destroy();\r\n});\r\n\r\nconst handleCreated = (editor) => {\r\n  editorRef.value = editor; // 记录 editor 实例，重要！\r\n};\r\n\r\ndefineExpose({ form });\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.demo-form-inline {\r\n  justify-content: flex-start;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.demo-form-inline .el-input {\r\n  --el-input-width: 420px;\r\n}\r\n\r\n.demo-form-inline .el-select {\r\n  --el-select-width: 420px;\r\n}\r\n\r\n/deep/ .el-radio-group {\r\n  width: 420px;\r\n}\r\n\r\n.form-title {\r\n  text-align: left;\r\n  padding-left: 30px;\r\n  margin: 20px auto 10px;\r\n  height: 44px;\r\n  background-color: #f2f2f2;\r\n  border-radius: 5px;\r\n  line-height: 44px;\r\n}\r\n\r\n/deep/ .el-form-item {\r\n  align-items: flex-start;\r\n}\r\n</style>\r\n"], "mappings": ";;EAcOA,KAA8B,EAA9B;IAAA;EAAA;AAA8B;;;;;;;6DAbnCC,YAAA,CAYUC,kBAAA;IAXR,aAAW,EAAC,OAAO;IAClBC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IACZC,KAAK,EAAC;;sBAEN,MAEe,CAFfN,YAAA,CAEeO,uBAAA;MAFDC,KAAK,EAAC,sBAAsB;MAACC,QAAQ,EAAR;;wBACzC,MAA2C,CAA3CT,YAAA,CAA2CU,mBAAA;oBAAxBN,MAAA,CAAAC,IAAI,CAACM,KAAK;mEAAVP,MAAA,CAAAC,IAAI,CAACM,KAAK,GAAAC,MAAA;QAAEC,SAAS,EAAT;;;QAEjCb,YAAA,CAEeO,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,QAAQ,EAAR;;wBACvB,MAA2C,CAA3CT,YAAA,CAA2CU,mBAAA;oBAAxBN,MAAA,CAAAC,IAAI,CAACS,KAAK;mEAAVV,MAAA,CAAAC,IAAI,CAACS,KAAK,GAAAF,MAAA;QAAEC,SAAS,EAAT;;;;;gCAGnCE,mBAAA,CAcQ,OAdRC,UAcQ,GAbJhB,YAAA,CAKEiB,kBAAA;IAJAlB,KAAqC,EAArC;MAAA;IAAA,CAAqC;IACpCmB,MAAM,EAAEd,MAAA,CAAAe,SAAS;IACjBC,aAAa,EAAEhB,MAAA,CAAAiB,aAAa;IAC5BC,IAAI,EAAElB,MAAA,CAAAkB;+CAETtB,YAAA,CAMEuB,iBAAA;IALAxB,KAAyC,EAAzC;MAAA;MAAA;IAAA,CAAyC;gBAChCK,MAAA,CAAAC,IAAI,CAACmB,OAAO;+DAAZpB,MAAA,CAAAC,IAAI,CAACmB,OAAO,GAAAZ,MAAA;IACpBQ,aAAa,EAAEhB,MAAA,CAAAqB,YAAY;IAC3BH,IAAI,EAAElB,MAAA,CAAAkB,IAAI;IACVI,WAAS,EAAEtB,MAAA,CAAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}