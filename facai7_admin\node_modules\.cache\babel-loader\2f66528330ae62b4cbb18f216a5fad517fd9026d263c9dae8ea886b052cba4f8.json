{"ast": null, "code": "import { resolveDynamicComponent as _resolveDynamicComponent, openBlock as _openBlock, createBlock as _createBlock, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createTextVNode as _createTextVNode, renderList as _renderList, Fragment as _Fragment, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode } from \"vue\";\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_menu_item = _resolveComponent(\"el-menu-item\");\n  const _component_ArrowRight = _resolveComponent(\"ArrowRight\");\n  const _component_el_menu_item_group = _resolveComponent(\"el-menu-item-group\");\n  const _component_el_sub_menu = _resolveComponent(\"el-sub-menu\");\n  const _component_el_menu = _resolveComponent(\"el-menu\");\n  return _openBlock(), _createBlock(_component_el_menu, {\n    \"default-active\": $setup.activeKey,\n    class: \"el-menu-vertical-demo layout-menu\",\n    collapse: $setup.props.isCollapse,\n    onOpen: $setup.handleOpen,\n    onClose: $setup.handleClose,\n    \"unique-opened\": true\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_menu_item, {\n      index: \"1\",\n      onClick: $setup.gotohome\n    }, {\n      title: _withCtx(() => _cache[0] || (_cache[0] = [_createTextVNode(\"首页\", -1 /* CACHED */)])),\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [(_openBlock(), _createBlock(_resolveDynamicComponent(\"menu\")))]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.menuList, (item, index) => {\n      return _openBlock(), _createBlock(_component_el_sub_menu, {\n        index: item.index,\n        key: item.index\n      }, {\n        title: _withCtx(() => [_createVNode(_component_el_icon, null, {\n          default: _withCtx(() => [(_openBlock(), _createBlock(_resolveDynamicComponent(item.icon)))]),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */), _createElementVNode(\"span\", null, _toDisplayString(item.name), 1 /* TEXT */)]),\n        default: _withCtx(() => [_createVNode(_component_el_menu_item_group, null, {\n          default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(item.children, (child, cIndex) => {\n            return _openBlock(), _createBlock(_component_el_menu_item, {\n              onClick: $event => $setup.selectItem(item, child),\n              index: child.index,\n              key: child.path\n            }, {\n              default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n                default: _withCtx(() => [_createVNode(_component_ArrowRight)]),\n                _: 1 /* STABLE */\n              }), _createTextVNode(_toDisplayString(child.name), 1 /* TEXT */)]),\n              _: 2 /* DYNAMIC */\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\", \"index\"]);\n          }), 128 /* KEYED_FRAGMENT */))]),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"index\"]);\n    }), 128 /* KEYED_FRAGMENT */))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"default-active\", \"collapse\"]);\n}", "map": {"version": 3, "names": ["_createBlock", "_component_el_menu", "$setup", "active<PERSON><PERSON>", "class", "collapse", "props", "isCollapse", "onOpen", "handleOpen", "onClose", "handleClose", "_createVNode", "_component_el_menu_item", "index", "onClick", "gotohome", "title", "_withCtx", "_cache", "_component_el_icon", "_resolveDynamicComponent", "_createElementBlock", "_Fragment", "_renderList", "menuList", "item", "_component_el_sub_menu", "key", "icon", "_createElementVNode", "_toDisplayString", "name", "_component_el_menu_item_group", "children", "child", "cIndex", "$event", "selectItem", "path", "_component_ArrowRight"], "sources": ["D:\\WorkSpace\\facai7\\facai7_admin\\src\\layout\\leftMenu.vue"], "sourcesContent": ["<template>\r\n  <el-menu\r\n    :default-active=\"activeKey\"\r\n    class=\"el-menu-vertical-demo layout-menu\"\r\n    :collapse=\"props.isCollapse\"\r\n    @open=\"handleOpen\"\r\n    @close=\"handleClose\"\r\n    :unique-opened=\"true\"\r\n  >\r\n    <el-menu-item index=\"1\" @click=\"gotohome\">\r\n      <el-icon><component is=\"menu\"></component></el-icon>\r\n      <template #title>首页</template>\r\n    </el-menu-item>\r\n    <el-sub-menu\r\n      :index=\"item.index\"\r\n      :key=\"item.index\"\r\n      v-for=\"(item, index) in menuList\"\r\n    >\r\n      <template #title>\r\n        <el-icon>\r\n          <component :is=\"item.icon\"></component>\r\n        </el-icon>\r\n        <span>{{ item.name }}</span>\r\n      </template>\r\n      <el-menu-item-group>\r\n        <el-menu-item\r\n          @click=\"selectItem(item, child)\"\r\n          :index=\"child.index\"\r\n          :key=\"child.path\"\r\n          v-for=\"(child, cIndex) in item.children\"\r\n          ><el-icon> <ArrowRight /> </el-icon>{{ child.name }}</el-menu-item\r\n        >\r\n      </el-menu-item-group>\r\n    </el-sub-menu>\r\n  </el-menu>\r\n</template>\r\n\r\n<script setup>\r\nimport store from \"@/store\";\r\nimport { ref, watch } from \"vue\";\r\nimport { useRoute, useRouter } from \"vue-router/dist/vue-router\";\r\nconst menuList = ref([\r\n  {\r\n    name: \"用户管理\",\r\n    index: \"1\",\r\n    path: \"/\",\r\n    icon: \"User\",\r\n    children: [\r\n      {\r\n        name: \"用户列表\",\r\n        index: \"1-1\",\r\n        path: \"/userList\",\r\n      },\r\n      {\r\n        name: \"团队列表\",\r\n        index: \"1-41\",\r\n        path: \"/teamList\",\r\n      },\r\n\r\n      {\r\n        name: \"钱包管理\",\r\n        index: \"1-8\",\r\n        path: \"/userWallet\",\r\n      },\r\n      {\r\n        name: \"地址管理\",\r\n        index: \"1-9\",\r\n        path: \"/userAddress\",\r\n      },\r\n      {\r\n        name: \"实名审核\",\r\n        index: \"1-11\",\r\n        path: \"/realnameList\",\r\n      },\r\n      {\r\n        name: \"用户登录记录\",\r\n        index: \"13-13\",\r\n        path: \"/userLoginLog\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"资金管理\",\r\n    index: \"11\",\r\n    path: \"/\",\r\n    icon: \"Coin\",\r\n    children: [\r\n      {\r\n        name: \"普通充值\",\r\n        index: \"11-2\",\r\n        path: \"/chargeList\",\r\n      },\r\n      {\r\n        name: \"usdt充值\",\r\n        index: \"11-15\",\r\n        path: \"/UsdtChargeList\",\r\n      },\r\n      {\r\n        name: \"提款列表\",\r\n        index: \"11-3\",\r\n        path: \"/withdrawList\",\r\n      },\r\n      {\r\n        name: \"转账列表\",\r\n        index: \"11-42\",\r\n        path: \"/transferLogLists\",\r\n      },\r\n      {\r\n        name: \"流水记录\",\r\n        index: \"11-4\",\r\n        path: \"/flowList\",\r\n      },\r\n      {\r\n        name: \"余额宝记录\",\r\n        index: \"11-41\",\r\n        path: \"/yuebaoLogLists\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"抽奖管理\",\r\n    index: \"12\",\r\n    path: \"/\",\r\n    icon: \"GoldMedal\",\r\n    children: [\r\n      // {\r\n      //   name: \"集字列表\",\r\n      //   index: \"12-8\",\r\n      //   path: \"/collectWords\",\r\n      // },\r\n      // {\r\n      //   name: \"集字记录\",\r\n      //   index: \"12-10\",\r\n      //   path: \"/wordsLogLists\",\r\n      // },\r\n      // {\r\n      //   name: \"大奖设置\",\r\n      //   index: \"12-9\",\r\n      //   path: \"/bigRaffleLists\",\r\n      // },\r\n      // {\r\n      //   name: \"大奖记录\",\r\n      //   index: \"12-11\",\r\n      //   path: \"/bigRaffleLogLists\",\r\n      // },\r\n      {\r\n        name: \"幸运抽奖\",\r\n        index: \"12-3\",\r\n        path: \"/lottory\",\r\n      },\r\n      {\r\n        name: \"抽奖记录\",\r\n        index: \"12-4\",\r\n        path: \"/lottoryDetails\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"团队管理\",\r\n    index: \"13\",\r\n    path: \"/\",\r\n    icon: \"Baseball\",\r\n    children: [\r\n      {\r\n        name: \"用户等级设置\",\r\n        index: \"13-5\",\r\n        path: \"/memberLevel\",\r\n      },\r\n      {\r\n        name: \"用户升级记录\",\r\n        index: \"13-12\",\r\n        path: \"/levelLogLists\",\r\n      },\r\n      {\r\n        name: \"团队等级设置\",\r\n        index: \"13-7\",\r\n        path: \"/teamLevel\",\r\n      },\r\n      {\r\n        name: \"团队升级记录\",\r\n        index: \"13-21\",\r\n        path: \"/teamLevelLogLists\",\r\n      },\r\n      // {\r\n      //   name: \"团队奖励记录\",\r\n      //   index: \"13-22\",\r\n      //   path: \"/teamRewardLogLists\",\r\n      // },\r\n    ],\r\n  },\r\n  {\r\n    name: \"签到管理\",\r\n    index: \"14\",\r\n    path: \"/\",\r\n    icon: \"Present\",\r\n    children: [\r\n      {\r\n        name: \"签到记录\",\r\n        index: \"14-4\",\r\n        path: \"/signInLists\",\r\n      },\r\n      {\r\n        name: \"签到奖品领取记录\",\r\n        index: \"14-6\",\r\n        path: \"/giftsList\",\r\n      },\r\n      {\r\n        name: \"签到奖品设置\",\r\n        index: \"14-11\",\r\n        path: \"/signRewardTypes\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"拼团管理\",\r\n    index: \"15\",\r\n    path: \"/\",\r\n    icon: \"Guide\",\r\n    children: [\r\n      {\r\n        name: \"拼团类型\",\r\n        index: \"15-12\",\r\n        path: \"/pintuanType\",\r\n      },\r\n      {\r\n        name: \"拼团列表\",\r\n        index: \"15-13\",\r\n        path: \"/pintuanList\",\r\n      },\r\n      {\r\n        name: \"拼团记录\",\r\n        index: \"15-14\",\r\n        path: \"/pintuanRecords\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"项目管理\",\r\n    index: \"2\",\r\n    path: \"/\",\r\n    icon: \"WindPower\",\r\n    children: [\r\n      {\r\n        name: \"项目分类\",\r\n        index: \"2-1\",\r\n        path: \"/projectType\",\r\n      },\r\n      {\r\n        name: \"项目列表\",\r\n        index: \"2-2\",\r\n        path: \"/projectList\",\r\n      },\r\n      {\r\n        name: \"购买记录\",\r\n        index: \"2-6\",\r\n        path: \"/buyRecordList\",\r\n      },\r\n      {\r\n        name: \"结算记录\",\r\n        index: \"2-7\",\r\n        path: \"/projectLogList\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"支付模块\",\r\n    index: \"8\",\r\n    path: \"/\",\r\n    icon: \"Wallet\",\r\n    children: [\r\n      {\r\n        name: \"支付类型\",\r\n        index: \"8-3\",\r\n        path: \"/rechargeType\",\r\n      },\r\n      {\r\n        name: \"支付上游\",\r\n        index: \"8-5\",\r\n        path: \"/paymentUpper\",\r\n      },\r\n      {\r\n        name: \"支付账号\",\r\n        index: \"8-7\",\r\n        path: \"/paymentAccount\",\r\n      },\r\n      {\r\n        name: \"支付渠道\",\r\n        index: \"8-6\",\r\n        path: \"/paymentChannel\",\r\n      },\r\n      {\r\n        name: \"银行列表\",\r\n        index: \"8-8\",\r\n        path: \"/bankList\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"商品管理\",\r\n    index: \"3\",\r\n    path: \"/\",\r\n    icon: \"Handbag\",\r\n    children: [\r\n      {\r\n        name: \"商品分类\",\r\n        index: \"3-1\",\r\n        path: \"/goodsType\",\r\n      },\r\n      {\r\n        name: \"商品列表\",\r\n        index: \"3-2\",\r\n        path: \"/goodsList\",\r\n      },\r\n\r\n      {\r\n        name: \"购买记录\",\r\n        index: \"3-3\",\r\n        path: \"/goodsRecords\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"运营管理\",\r\n    index: \"4\",\r\n    path: \"/\",\r\n    icon: \"Pointer\",\r\n    children: [\r\n      {\r\n        name: \"优惠券列表\",\r\n        index: \"23-6\",\r\n        path: \"/discountList\",\r\n      },\r\n      {\r\n        name: \"轮播图\",\r\n        index: \"4-15\",\r\n        path: \"/bannerList\",\r\n      },\r\n      {\r\n        name: \"站内信\",\r\n        index: \"41-15\",\r\n        path: \"/messageLists\",\r\n      },\r\n      {\r\n        name: \"文章类型\",\r\n        index: \"4-10\",\r\n        path: \"/newsTypeList\",\r\n      },\r\n      {\r\n        name: \"文章资讯\",\r\n        index: \"4-1\",\r\n        path: \"/newsList\",\r\n      },\r\n\r\n      // {\r\n      //     name: '站内信',\r\n      //     index: '4-5',\r\n      //     path: '/letters'\r\n      // },\r\n\r\n      {\r\n        name: \"短信记录\",\r\n        index: \"4-8\",\r\n        path: \"/smsCodeLists\",\r\n      },\r\n      {\r\n        name: \"用户反馈\",\r\n        index: \"4-9\",\r\n        path: \"/userFeedback\",\r\n      },\r\n      {\r\n        name: \"问答列表\",\r\n        index: \"4-12\",\r\n        path: \"/questionList\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"系统设置\",\r\n    index: \"5\",\r\n    path: \"/\",\r\n    icon: \"Setting\",\r\n    children: [\r\n      {\r\n        name: \"参数设置\",\r\n        index: \"5-1\",\r\n        path: \"/paramsSetting\",\r\n      },\r\n      // {\r\n      //     name: '网站介绍',\r\n      //     index: '5-2',\r\n      //     path: '/siteDesc'\r\n      // },\r\n\r\n      {\r\n        name: \"谷歌密钥\",\r\n        index: \"5-4\",\r\n        path: \"/googleKeys\",\r\n      },\r\n    ],\r\n  },\r\n  // {\r\n  //     name: '视频管理',\r\n  //     index: '6',\r\n  //     path: '/',\r\n  //     icon: 'Film',\r\n  //     children: [\r\n  // {\r\n  //     name: '视频分类',\r\n  //     index: '6-1',\r\n  //     path: '/videoType'\r\n  // },{\r\n  //     name: '视频管理',\r\n  //     index: '6-2',\r\n  //     path: '/videoList'\r\n  // },\r\n  //         {\r\n  //             name: '宣传视频',\r\n  //             index: '6-3',\r\n  //             path: '/xuanchuanVideo'\r\n  //         }\r\n  //     ]\r\n  // },\r\n  {\r\n    name: \"后台管理\",\r\n    index: \"7\",\r\n    path: \"/\",\r\n    icon: \"Guide\",\r\n    children: [\r\n      {\r\n        name: \"后台用户\",\r\n        index: \"7-1\",\r\n        path: \"/backendUser\",\r\n      },\r\n      {\r\n        name: \"登录日志\",\r\n        index: \"7-2\",\r\n        path: \"/loginLog\",\r\n      },\r\n    ],\r\n  },\r\n]);\r\n\r\nconst router = useRouter();\r\nconst selectItem = (item, child) => {\r\n  // const selectItem\r\n  let arr = [\r\n    {\r\n      name: \"首页\",\r\n      index: \"1\",\r\n      path: \"/home\",\r\n    },\r\n    item,\r\n    child,\r\n  ];\r\n  store.commit(\"updateBreakCum\", arr);\r\n  router.push(child.path);\r\n};\r\n\r\nconst activeKey = ref(\"1\");\r\nconst route = useRoute();\r\n\r\nwatch(\r\n  () => route.path,\r\n  (newPath, oldPath) => {\r\n    menuList.value.map((item) => {\r\n      let res = item.children.filter((_t) => _t.path === newPath);\r\n      if (res.length > 0) {\r\n        activeKey.value = res[0].index;\r\n      }\r\n    });\r\n  },\r\n  { immediate: true }\r\n);\r\n\r\nconst gotohome = () => {\r\n  let arr = [\r\n    {\r\n      name: \"首页\",\r\n      index: \"1\",\r\n      path: \"/home\",\r\n    },\r\n  ];\r\n  store.commit(\"updateBreakCum\", arr);\r\n  router.push(\"/home\");\r\n};\r\n\r\nconst handleOpen = (key, keyPath) => {};\r\nconst handleClose = (key, keyPath) => {};\r\n\r\nconst props = defineProps([\"isCollapse\"]);\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.layout-menu {\r\n  // width: 220px;\r\n  height: 100%;\r\n  background-color: #eee;\r\n  transition: all 0.25s;\r\n  overflow-y: auto;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;uBACEA,YAAA,CAiCUC,kBAAA;IAhCP,gBAAc,EAAEC,MAAA,CAAAC,SAAS;IAC1BC,KAAK,EAAC,mCAAmC;IACxCC,QAAQ,EAAEH,MAAA,CAAAI,KAAK,CAACC,UAAU;IAC1BC,MAAI,EAAEN,MAAA,CAAAO,UAAU;IAChBC,OAAK,EAAER,MAAA,CAAAS,WAAW;IAClB,eAAa,EAAE;;sBAEhB,MAGe,CAHfC,YAAA,CAGeC,uBAAA;MAHDC,KAAK,EAAC,GAAG;MAAEC,OAAK,EAAEb,MAAA,CAAAc;;MAEnBC,KAAK,EAAAC,QAAA,CAAC,MAAEC,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,mB;wBADnB,MAAoD,CAApDP,YAAA,CAAoDQ,kBAAA;0BAA3C,MAAiC,E,cAAjCpB,YAAA,CAAiCqB,wBAAA,W;;;;2BAG5CC,mBAAA,CAoBcC,SAAA,QAAAC,WAAA,CAjBYtB,MAAA,CAAAuB,QAAQ,GAAxBC,IAAI,EAAEZ,KAAK;2BAHrBd,YAAA,CAoBc2B,sBAAA;QAnBXb,KAAK,EAAEY,IAAI,CAACZ,KAAK;QACjBc,GAAG,EAAEF,IAAI,CAACZ;;QAGAG,KAAK,EAAAC,QAAA,CACd,MAEU,CAFVN,YAAA,CAEUQ,kBAAA;4BADR,MAAuC,E,cAAvCpB,YAAA,CAAuCqB,wBAAA,CAAvBK,IAAI,CAACG,IAAI,I;;sCAE3BC,mBAAA,CAA4B,cAAAC,gBAAA,CAAnBL,IAAI,CAACM,IAAI,iB;0BAEpB,MAQqB,CARrBpB,YAAA,CAQqBqB,6BAAA;4BAHjB,MAAwC,E,kBAJ1CX,mBAAA,CAK+EC,SAAA,QAAAC,WAAA,CADnDE,IAAI,CAACQ,QAAQ,GAA/BC,KAAK,EAAEC,MAAM;iCAJvBpC,YAAA,CAK+Ea,uBAAA;cAJ5EE,OAAK,EAAAsB,MAAA,IAAEnC,MAAA,CAAAoC,UAAU,CAACZ,IAAI,EAAES,KAAK;cAC7BrB,KAAK,EAAEqB,KAAK,CAACrB,KAAK;cAClBc,GAAG,EAAEO,KAAK,CAACI;;gCAEX,MAAmC,CAAnC3B,YAAA,CAAmCQ,kBAAA;kCAAzB,MAAc,CAAdR,YAAA,CAAc4B,qBAAA,E;;oDAAcL,KAAK,CAACH,IAAI,iB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}