{"ast": null, "code": "import { inject, ref, h } from 'vue';\nimport { debounce } from 'lodash-unified';\nimport { getCell, getColumnByCell, createTablePopper, removePopper } from '../util.mjs';\nimport { TABLE_INJECTION_KEY } from '../tokens.mjs';\nimport { hasClass, addClass, removeClass } from '../../../../utils/dom/style.mjs';\nfunction isGreaterThan(a, b, epsilon = 0.03) {\n  return a - b > epsilon;\n}\nfunction useEvents(props) {\n  const parent = inject(TABLE_INJECTION_KEY);\n  const tooltipContent = ref(\"\");\n  const tooltipTrigger = ref(h(\"div\"));\n  const handleEvent = (event, row, name) => {\n    var _a, _b, _c;\n    const table = parent;\n    const cell = getCell(event);\n    let column = null;\n    const namespace = (_a = table == null ? void 0 : table.vnode.el) == null ? void 0 : _a.dataset.prefix;\n    if (cell) {\n      column = getColumnByCell({\n        columns: (_c = (_b = props.store) == null ? void 0 : _b.states.columns.value) != null ? _c : []\n      }, cell, namespace);\n      if (column) {\n        table == null ? void 0 : table.emit(`cell-${name}`, row, column, cell, event);\n      }\n    }\n    table == null ? void 0 : table.emit(`row-${name}`, row, column, event);\n  };\n  const handleDoubleClick = (event, row) => {\n    handleEvent(event, row, \"dblclick\");\n  };\n  const handleClick = (event, row) => {\n    var _a;\n    (_a = props.store) == null ? void 0 : _a.commit(\"setCurrentRow\", row);\n    handleEvent(event, row, \"click\");\n  };\n  const handleContextMenu = (event, row) => {\n    handleEvent(event, row, \"contextmenu\");\n  };\n  const handleMouseEnter = debounce(index => {\n    var _a;\n    (_a = props.store) == null ? void 0 : _a.commit(\"setHoverRow\", index);\n  }, 30);\n  const handleMouseLeave = debounce(() => {\n    var _a;\n    (_a = props.store) == null ? void 0 : _a.commit(\"setHoverRow\", null);\n  }, 30);\n  const getPadding = el => {\n    const style = window.getComputedStyle(el, null);\n    const paddingLeft = Number.parseInt(style.paddingLeft, 10) || 0;\n    const paddingRight = Number.parseInt(style.paddingRight, 10) || 0;\n    const paddingTop = Number.parseInt(style.paddingTop, 10) || 0;\n    const paddingBottom = Number.parseInt(style.paddingBottom, 10) || 0;\n    return {\n      left: paddingLeft,\n      right: paddingRight,\n      top: paddingTop,\n      bottom: paddingBottom\n    };\n  };\n  const toggleRowClassByCell = (rowSpan, event, toggle) => {\n    var _a;\n    let node = (_a = event == null ? void 0 : event.target) == null ? void 0 : _a.parentNode;\n    while (rowSpan > 1) {\n      node = node == null ? void 0 : node.nextSibling;\n      if (!node || node.nodeName !== \"TR\") break;\n      toggle(node, \"hover-row hover-fixed-row\");\n      rowSpan--;\n    }\n  };\n  const handleCellMouseEnter = (event, row, tooltipOptions) => {\n    var _a, _b, _c, _d, _e, _f;\n    if (!parent) return;\n    const table = parent;\n    const cell = getCell(event);\n    const namespace = (_a = table == null ? void 0 : table.vnode.el) == null ? void 0 : _a.dataset.prefix;\n    let column = null;\n    if (cell) {\n      column = getColumnByCell({\n        columns: (_c = (_b = props.store) == null ? void 0 : _b.states.columns.value) != null ? _c : []\n      }, cell, namespace);\n      if (!column) {\n        return;\n      }\n      if (cell.rowSpan > 1) {\n        toggleRowClassByCell(cell.rowSpan, event, addClass);\n      }\n      const hoverState = table.hoverState = {\n        cell,\n        column,\n        row\n      };\n      table == null ? void 0 : table.emit(\"cell-mouse-enter\", hoverState.row, hoverState.column, hoverState.cell, event);\n    }\n    if (!tooltipOptions) {\n      return;\n    }\n    const cellChild = event.target.querySelector(\".cell\");\n    if (!(hasClass(cellChild, `${namespace}-tooltip`) && cellChild.childNodes.length)) {\n      return;\n    }\n    const range = document.createRange();\n    range.setStart(cellChild, 0);\n    range.setEnd(cellChild, cellChild.childNodes.length);\n    const {\n      width: rangeWidth,\n      height: rangeHeight\n    } = range.getBoundingClientRect();\n    const {\n      width: cellChildWidth,\n      height: cellChildHeight\n    } = cellChild.getBoundingClientRect();\n    const {\n      top,\n      left,\n      right,\n      bottom\n    } = getPadding(cellChild);\n    const horizontalPadding = left + right;\n    const verticalPadding = top + bottom;\n    if (isGreaterThan(rangeWidth + horizontalPadding, cellChildWidth) || isGreaterThan(rangeHeight + verticalPadding, cellChildHeight) || isGreaterThan(cellChild.scrollWidth, cellChildWidth)) {\n      createTablePopper(tooltipOptions, (_d = (cell == null ? void 0 : cell.innerText) || (cell == null ? void 0 : cell.textContent)) != null ? _d : \"\", row, column, cell, table);\n    } else if (((_e = removePopper) == null ? void 0 : _e.trigger) === cell) {\n      (_f = removePopper) == null ? void 0 : _f();\n    }\n  };\n  const handleCellMouseLeave = event => {\n    const cell = getCell(event);\n    if (!cell) return;\n    if (cell.rowSpan > 1) {\n      toggleRowClassByCell(cell.rowSpan, event, removeClass);\n    }\n    const oldHoverState = parent == null ? void 0 : parent.hoverState;\n    parent == null ? void 0 : parent.emit(\"cell-mouse-leave\", oldHoverState == null ? void 0 : oldHoverState.row, oldHoverState == null ? void 0 : oldHoverState.column, oldHoverState == null ? void 0 : oldHoverState.cell, event);\n  };\n  return {\n    handleDoubleClick,\n    handleClick,\n    handleContextMenu,\n    handleMouseEnter,\n    handleMouseLeave,\n    handleCellMouseEnter,\n    handleCellMouseLeave,\n    tooltipContent,\n    tooltipTrigger\n  };\n}\nexport { useEvents as default };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "a", "b", "epsilon", "useEvents", "props", "parent", "inject", "TABLE_INJECTION_KEY", "tooltipContent", "ref", "tooltipTrigger", "h", "handleEvent", "event", "row", "name", "_a", "_b", "_c", "table", "cell", "getCell", "column", "namespace", "vnode", "el", "dataset", "prefix", "getColumnByCell", "columns", "store", "states", "value", "emit", "handleDoubleClick", "handleClick", "commit", "handleContextMenu", "handleMouseEnter", "debounce", "index", "handleMouseLeave", "getPadding", "style", "window", "getComputedStyle", "paddingLeft", "Number", "parseInt", "paddingRight", "paddingTop", "paddingBottom", "left", "right", "top", "bottom", "toggleRowClassByCell", "rowSpan", "toggle", "node", "target", "parentNode", "nextS<PERSON>ling", "nodeName", "handleCellMouseEnter", "tooltipOptions", "_d", "_e", "_f", "addClass", "hoverState", "cellChild", "querySelector", "hasClass", "childNodes", "length", "range", "document", "createRange", "setStart", "setEnd", "width", "rangeWidth", "height", "rangeHeight", "getBoundingClientRect", "cell<PERSON><PERSON>d<PERSON><PERSON><PERSON>", "cellChildHeight", "horizontalPadding", "verticalPadding", "scrollWidth", "createTablePopper", "innerText", "textContent", "removePopper", "trigger", "handleCellMouseLeave", "removeClass", "oldHoverState"], "sources": ["../../../../../../../packages/components/table/src/table-body/events-helper.ts"], "sourcesContent": ["import { h, inject, ref } from 'vue'\nimport { debounce } from 'lodash-unified'\nimport { addClass, hasClass, removeClass } from '@element-plus/utils'\nimport {\n  createTablePopper,\n  getCell,\n  getColumnByCell,\n  removePopper,\n} from '../util'\nimport { TABLE_INJECTION_KEY } from '../tokens'\n\nimport type { TableColumnCtx } from '../table-column/defaults'\nimport type { TableBodyProps } from './defaults'\nimport type { TableOverflowTooltipOptions } from '../util'\nimport type { DefaultRow } from '../table/defaults'\n\nfunction isGreaterThan(a: number, b: number, epsilon = 0.03) {\n  return a - b > epsilon\n}\n\nfunction useEvents<T extends DefaultRow>(props: Partial<TableBodyProps<T>>) {\n  const parent = inject(TABLE_INJECTION_KEY)\n  const tooltipContent = ref('')\n  const tooltipTrigger = ref(h('div'))\n  const handleEvent = (event: Event, row: T, name: string) => {\n    const table = parent\n    const cell = getCell(event)\n    let column: TableColumnCtx<T> | null = null\n    const namespace = table?.vnode.el?.dataset.prefix\n    if (cell) {\n      column = getColumnByCell(\n        {\n          columns: props.store?.states.columns.value ?? [],\n        },\n        cell,\n        namespace\n      )\n      if (column) {\n        table?.emit(`cell-${name}`, row, column, cell, event)\n      }\n    }\n    table?.emit(`row-${name}`, row, column, event)\n  }\n  const handleDoubleClick = (event: Event, row: T) => {\n    handleEvent(event, row, 'dblclick')\n  }\n  const handleClick = (event: Event, row: T) => {\n    props.store?.commit('setCurrentRow', row)\n    handleEvent(event, row, 'click')\n  }\n  const handleContextMenu = (event: Event, row: T) => {\n    handleEvent(event, row, 'contextmenu')\n  }\n  const handleMouseEnter = debounce((index: number) => {\n    props.store?.commit('setHoverRow', index)\n  }, 30)\n  const handleMouseLeave = debounce(() => {\n    props.store?.commit('setHoverRow', null)\n  }, 30)\n  const getPadding = (el: HTMLElement) => {\n    const style = window.getComputedStyle(el, null)\n    const paddingLeft = Number.parseInt(style.paddingLeft, 10) || 0\n    const paddingRight = Number.parseInt(style.paddingRight, 10) || 0\n    const paddingTop = Number.parseInt(style.paddingTop, 10) || 0\n    const paddingBottom = Number.parseInt(style.paddingBottom, 10) || 0\n    return {\n      left: paddingLeft,\n      right: paddingRight,\n      top: paddingTop,\n      bottom: paddingBottom,\n    }\n  }\n\n  const toggleRowClassByCell = (\n    rowSpan: number,\n    event: MouseEvent,\n    toggle: (el: Element, cls: string) => void\n  ) => {\n    let node: Node | null | undefined = (event?.target as Element | null)\n      ?.parentNode\n    while (rowSpan > 1) {\n      node = node?.nextSibling\n      if (!node || node.nodeName !== 'TR') break\n      toggle(node as Element, 'hover-row hover-fixed-row')\n      rowSpan--\n    }\n  }\n\n  const handleCellMouseEnter = (\n    event: MouseEvent,\n    row: T,\n    tooltipOptions: TableOverflowTooltipOptions\n  ) => {\n    if (!parent) return\n    const table = parent\n    const cell = getCell(event)\n    const namespace = table?.vnode.el?.dataset.prefix\n    let column: TableColumnCtx<T> | null = null\n    if (cell) {\n      column = getColumnByCell(\n        {\n          columns: props.store?.states.columns.value ?? [],\n        },\n        cell,\n        namespace\n      )\n      if (!column) {\n        return\n      }\n      if (cell.rowSpan > 1) {\n        toggleRowClassByCell(cell.rowSpan, event, addClass)\n      }\n      const hoverState = (table.hoverState = {\n        cell,\n        column: column as any,\n        row,\n      })\n      table?.emit(\n        'cell-mouse-enter',\n        hoverState.row,\n        hoverState.column,\n        hoverState.cell,\n        event\n      )\n    }\n\n    if (!tooltipOptions) {\n      return\n    }\n\n    // 判断是否text-overflow, 如果是就显示tooltip\n    const cellChild = (event.target as HTMLElement).querySelector(\n      '.cell'\n    ) as HTMLElement\n    if (\n      !(\n        hasClass(cellChild, `${namespace}-tooltip`) &&\n        cellChild.childNodes.length\n      )\n    ) {\n      return\n    }\n    // use range width instead of scrollWidth to determine whether the text is overflowing\n    // to address a potential FireFox bug: https://bugzilla.mozilla.org/show_bug.cgi?id=1074543#c3\n    const range = document.createRange()\n    range.setStart(cellChild, 0)\n    range.setEnd(cellChild, cellChild.childNodes.length)\n    /** detail: https://github.com/element-plus/element-plus/issues/10790\n     *  What went wrong?\n     *  UI > Browser > Zoom, In Blink/WebKit, getBoundingClientRect() sometimes returns inexact values, probably due to lost precision during internal calculations. In the example above:\n     *    - Expected: 188\n     *    - Actual: 188.00000762939453\n     */\n    const { width: rangeWidth, height: rangeHeight } =\n      range.getBoundingClientRect()\n    const { width: cellChildWidth, height: cellChildHeight } =\n      cellChild.getBoundingClientRect()\n\n    const { top, left, right, bottom } = getPadding(cellChild)\n    const horizontalPadding = left + right\n    const verticalPadding = top + bottom\n    if (\n      isGreaterThan(rangeWidth + horizontalPadding, cellChildWidth) ||\n      isGreaterThan(rangeHeight + verticalPadding, cellChildHeight) ||\n      // When using a high-resolution screen, it is possible that a returns cellChild.scrollWidth value of 1921 and\n      // cellChildWidth returns a value of 1920.994140625. #16856 #16673\n      isGreaterThan(cellChild.scrollWidth, cellChildWidth)\n    ) {\n      createTablePopper(\n        tooltipOptions,\n        (cell?.innerText || cell?.textContent) ?? '',\n        row,\n        column,\n        cell,\n        table\n      )\n    } else if (removePopper?.trigger === cell) {\n      removePopper?.()\n    }\n  }\n  const handleCellMouseLeave = (event: MouseEvent) => {\n    const cell = getCell(event)\n    if (!cell) return\n    if (cell.rowSpan > 1) {\n      toggleRowClassByCell(cell.rowSpan, event, removeClass)\n    }\n    const oldHoverState = parent?.hoverState\n    parent?.emit(\n      'cell-mouse-leave',\n      oldHoverState?.row,\n      oldHoverState?.column,\n      oldHoverState?.cell,\n      event\n    )\n  }\n\n  return {\n    handleDoubleClick,\n    handleClick,\n    handleContextMenu,\n    handleMouseEnter,\n    handleMouseLeave,\n    handleCellMouseEnter,\n    handleCellMouseLeave,\n    tooltipContent,\n    tooltipTrigger,\n  }\n}\n\nexport default useEvents\n"], "mappings": ";;;;;AAUA,SAASA,aAAaA,CAACC,CAAC,EAAEC,CAAC,EAAEC,OAAO,GAAG,IAAI,EAAE;EAC3C,OAAOF,CAAC,GAAGC,CAAC,GAAGC,OAAO;AACxB;AACA,SAASC,SAASA,CAACC,KAAK,EAAE;EACxB,MAAMC,MAAM,GAAGC,MAAM,CAACC,mBAAmB,CAAC;EAC1C,MAAMC,cAAc,GAAGC,GAAG,CAAC,EAAE,CAAC;EAC9B,MAAMC,cAAc,GAAGD,GAAG,CAACE,CAAC,CAAC,KAAK,CAAC,CAAC;EACpC,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,GAAG,EAAEC,IAAI,KAAK;IACxC,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IACd,MAAMC,KAAK,GAAGd,MAAM;IACpB,MAAMe,IAAI,GAAGC,OAAO,CAACR,KAAK,CAAC;IAC3B,IAAIS,MAAM,GAAG,IAAI;IACjB,MAAMC,SAAS,GAAG,CAACP,EAAE,GAAGG,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACK,KAAK,CAACC,EAAE,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGT,EAAE,CAACU,OAAO,CAACC,MAAM;IACrG,IAAIP,IAAI,EAAE;MACRE,MAAM,GAAGM,eAAe,CAAC;QACvBC,OAAO,EAAE,CAACX,EAAE,GAAG,CAACD,EAAE,GAAGb,KAAK,CAAC0B,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGb,EAAE,CAACc,MAAM,CAACF,OAAO,CAACG,KAAK,KAAK,IAAI,GAAGd,EAAE,GAAG;MACrG,CAAO,EAAEE,IAAI,EAAEG,SAAS,CAAC;MACnB,IAAID,MAAM,EAAE;QACVH,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACc,IAAI,CAAC,QAAQlB,IAAI,EAAE,EAAED,GAAG,EAAEQ,MAAM,EAAEF,IAAI,EAAEP,KAAK,CAAC;MACrF;IACA;IACIM,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACc,IAAI,CAAC,OAAOlB,IAAI,EAAE,EAAED,GAAG,EAAEQ,MAAM,EAAET,KAAK,CAAC;EAC1E,CAAG;EACD,MAAMqB,iBAAiB,GAAGA,CAACrB,KAAK,EAAEC,GAAG,KAAK;IACxCF,WAAW,CAACC,KAAK,EAAEC,GAAG,EAAE,UAAU,CAAC;EACvC,CAAG;EACD,MAAMqB,WAAW,GAAGA,CAACtB,KAAK,EAAEC,GAAG,KAAK;IAClC,IAAIE,EAAE;IACN,CAACA,EAAE,GAAGZ,KAAK,CAAC0B,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGd,EAAE,CAACoB,MAAM,CAAC,eAAe,EAAEtB,GAAG,CAAC;IACrEF,WAAW,CAACC,KAAK,EAAEC,GAAG,EAAE,OAAO,CAAC;EACpC,CAAG;EACD,MAAMuB,iBAAiB,GAAGA,CAACxB,KAAK,EAAEC,GAAG,KAAK;IACxCF,WAAW,CAACC,KAAK,EAAEC,GAAG,EAAE,aAAa,CAAC;EAC1C,CAAG;EACD,MAAMwB,gBAAgB,GAAGC,QAAQ,CAAEC,KAAK,IAAK;IAC3C,IAAIxB,EAAE;IACN,CAACA,EAAE,GAAGZ,KAAK,CAAC0B,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGd,EAAE,CAACoB,MAAM,CAAC,aAAa,EAAEI,KAAK,CAAC;EACzE,CAAG,EAAE,EAAE,CAAC;EACN,MAAMC,gBAAgB,GAAGF,QAAQ,CAAC,MAAM;IACtC,IAAIvB,EAAE;IACN,CAACA,EAAE,GAAGZ,KAAK,CAAC0B,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGd,EAAE,CAACoB,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC;EACxE,CAAG,EAAE,EAAE,CAAC;EACN,MAAMM,UAAU,GAAIjB,EAAE,IAAK;IACzB,MAAMkB,KAAK,GAAGC,MAAM,CAACC,gBAAgB,CAACpB,EAAE,EAAE,IAAI,CAAC;IAC/C,MAAMqB,WAAW,GAAGC,MAAM,CAACC,QAAQ,CAACL,KAAK,CAACG,WAAW,EAAE,EAAE,CAAC,IAAI,CAAC;IAC/D,MAAMG,YAAY,GAAGF,MAAM,CAACC,QAAQ,CAACL,KAAK,CAACM,YAAY,EAAE,EAAE,CAAC,IAAI,CAAC;IACjE,MAAMC,UAAU,GAAGH,MAAM,CAACC,QAAQ,CAACL,KAAK,CAACO,UAAU,EAAE,EAAE,CAAC,IAAI,CAAC;IAC7D,MAAMC,aAAa,GAAGJ,MAAM,CAACC,QAAQ,CAACL,KAAK,CAACQ,aAAa,EAAE,EAAE,CAAC,IAAI,CAAC;IACnE,OAAO;MACLC,IAAI,EAAEN,WAAW;MACjBO,KAAK,EAAEJ,YAAY;MACnBK,GAAG,EAAEJ,UAAU;MACfK,MAAM,EAAEJ;IACd,CAAK;EACL,CAAG;EACD,MAAMK,oBAAoB,GAAGA,CAACC,OAAO,EAAE5C,KAAK,EAAE6C,MAAM,KAAK;IACvD,IAAI1C,EAAE;IACN,IAAI2C,IAAI,GAAG,CAAC3C,EAAE,GAAGH,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC+C,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG5C,EAAE,CAAC6C,UAAU;IACxF,OAAOJ,OAAO,GAAG,CAAC,EAAE;MAClBE,IAAI,GAAGA,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACG,WAAW;MAC/C,IAAI,CAACH,IAAI,IAAIA,IAAI,CAACI,QAAQ,KAAK,IAAI,EACjC;MACFL,MAAM,CAACC,IAAI,EAAE,2BAA2B,CAAC;MACzCF,OAAO,EAAE;IACf;EACA,CAAG;EACD,MAAMO,oBAAoB,GAAGA,CAACnD,KAAK,EAAEC,GAAG,EAAEmD,cAAc,KAAK;IAC3D,IAAIjD,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEgD,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAC1B,IAAI,CAAC/D,MAAM,EACT;IACF,MAAMc,KAAK,GAAGd,MAAM;IACpB,MAAMe,IAAI,GAAGC,OAAO,CAACR,KAAK,CAAC;IAC3B,MAAMU,SAAS,GAAG,CAACP,EAAE,GAAGG,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACK,KAAK,CAACC,EAAE,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGT,EAAE,CAACU,OAAO,CAACC,MAAM;IACrG,IAAIL,MAAM,GAAG,IAAI;IACjB,IAAIF,IAAI,EAAE;MACRE,MAAM,GAAGM,eAAe,CAAC;QACvBC,OAAO,EAAE,CAACX,EAAE,GAAG,CAACD,EAAE,GAAGb,KAAK,CAAC0B,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGb,EAAE,CAACc,MAAM,CAACF,OAAO,CAACG,KAAK,KAAK,IAAI,GAAGd,EAAE,GAAG;MACrG,CAAO,EAAEE,IAAI,EAAEG,SAAS,CAAC;MACnB,IAAI,CAACD,MAAM,EAAE;QACX;MACR;MACM,IAAIF,IAAI,CAACqC,OAAO,GAAG,CAAC,EAAE;QACpBD,oBAAoB,CAACpC,IAAI,CAACqC,OAAO,EAAE5C,KAAK,EAAEwD,QAAQ,CAAC;MAC3D;MACM,MAAMC,UAAU,GAAGnD,KAAK,CAACmD,UAAU,GAAG;QACpClD,IAAI;QACJE,MAAM;QACNR;MACR,CAAO;MACDK,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACc,IAAI,CAAC,kBAAkB,EAAEqC,UAAU,CAACxD,GAAG,EAAEwD,UAAU,CAAChD,MAAM,EAAEgD,UAAU,CAAClD,IAAI,EAAEP,KAAK,CAAC;IACxH;IACI,IAAI,CAACoD,cAAc,EAAE;MACnB;IACN;IACI,MAAMM,SAAS,GAAG1D,KAAK,CAAC+C,MAAM,CAACY,aAAa,CAAC,OAAO,CAAC;IACrD,IAAI,EAAEC,QAAQ,CAACF,SAAS,EAAE,GAAGhD,SAAS,UAAU,CAAC,IAAIgD,SAAS,CAACG,UAAU,CAACC,MAAM,CAAC,EAAE;MACjF;IACN;IACI,MAAMC,KAAK,GAAGC,QAAQ,CAACC,WAAW,EAAE;IACpCF,KAAK,CAACG,QAAQ,CAACR,SAAS,EAAE,CAAC,CAAC;IAC5BK,KAAK,CAACI,MAAM,CAACT,SAAS,EAAEA,SAAS,CAACG,UAAU,CAACC,MAAM,CAAC;IACpD,MAAM;MAAEM,KAAK,EAAEC,UAAU;MAAEC,MAAM,EAAEC;IAAW,CAAE,GAAGR,KAAK,CAACS,qBAAqB,EAAE;IAChF,MAAM;MAAEJ,KAAK,EAAEK,cAAc;MAAEH,MAAM,EAAEI;IAAe,CAAE,GAAGhB,SAAS,CAACc,qBAAqB,EAAE;IAC5F,MAAM;MAAE/B,GAAG;MAAEF,IAAI;MAAEC,KAAK;MAAEE;IAAM,CAAE,GAAGb,UAAU,CAAC6B,SAAS,CAAC;IAC1D,MAAMiB,iBAAiB,GAAGpC,IAAI,GAAGC,KAAK;IACtC,MAAMoC,eAAe,GAAGnC,GAAG,GAAGC,MAAM;IACpC,IAAIxD,aAAa,CAACmF,UAAU,GAAGM,iBAAiB,EAAEF,cAAc,CAAC,IAAIvF,aAAa,CAACqF,WAAW,GAAGK,eAAe,EAAEF,eAAe,CAAC,IAAIxF,aAAa,CAACwE,SAAS,CAACmB,WAAW,EAAEJ,cAAc,CAAC,EAAE;MAC1LK,iBAAiB,CAAC1B,cAAc,EAAE,CAACC,EAAE,GAAG,CAAC9C,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACwE,SAAS,MAAMxE,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACyE,WAAW,CAAC,KAAK,IAAI,GAAG3B,EAAE,GAAG,EAAE,EAAEpD,GAAG,EAAEQ,MAAM,EAAEF,IAAI,EAAED,KAAK,CAAC;IAClL,CAAK,MAAM,IAAI,CAAC,CAACgD,EAAE,GAAG2B,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG3B,EAAE,CAAC4B,OAAO,MAAM3E,IAAI,EAAE;MACvE,CAACgD,EAAE,GAAG0B,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG1B,EAAE,EAAE;IACjD;EACA,CAAG;EACD,MAAM4B,oBAAoB,GAAInF,KAAK,IAAK;IACtC,MAAMO,IAAI,GAAGC,OAAO,CAACR,KAAK,CAAC;IAC3B,IAAI,CAACO,IAAI,EACP;IACF,IAAIA,IAAI,CAACqC,OAAO,GAAG,CAAC,EAAE;MACpBD,oBAAoB,CAACpC,IAAI,CAACqC,OAAO,EAAE5C,KAAK,EAAEoF,WAAW,CAAC;IAC5D;IACI,MAAMC,aAAa,GAAG7F,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACiE,UAAU;IACjEjE,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC4B,IAAI,CAAC,kBAAkB,EAAEiE,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACpF,GAAG,EAAEoF,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC5E,MAAM,EAAE4E,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC9E,IAAI,EAAEP,KAAK,CAAC;EACpO,CAAG;EACD,OAAO;IACLqB,iBAAiB;IACjBC,WAAW;IACXE,iBAAiB;IACjBC,gBAAgB;IAChBG,gBAAgB;IAChBuB,oBAAoB;IACpBgC,oBAAoB;IACpBxF,cAAc;IACdE;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}