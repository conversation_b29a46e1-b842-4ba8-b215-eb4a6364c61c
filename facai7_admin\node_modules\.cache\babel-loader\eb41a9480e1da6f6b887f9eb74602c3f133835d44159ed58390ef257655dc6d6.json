{"ast": null, "code": "import nativeCreate from './_nativeCreate.js';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Checks if a hash value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf Hash\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction hashHas(key) {\n  var data = this.__data__;\n  return nativeCreate ? data[key] !== undefined : hasOwnProperty.call(data, key);\n}\nexport default hashHas;", "map": {"version": 3, "names": ["nativeCreate", "objectProto", "Object", "prototype", "hasOwnProperty", "hashHas", "key", "data", "__data__", "undefined", "call"], "sources": ["D:/WorkSpace/facai7/facai7_admin/node_modules/lodash-es/_hashHas.js"], "sourcesContent": ["import nativeCreate from './_nativeCreate.js';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Checks if a hash value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf Hash\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction hashHas(key) {\n  var data = this.__data__;\n  return nativeCreate ? (data[key] !== undefined) : hasOwnProperty.call(data, key);\n}\n\nexport default hashHas;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,oBAAoB;;AAE7C;AACA,IAAIC,WAAW,GAAGC,MAAM,CAACC,SAAS;;AAElC;AACA,IAAIC,cAAc,GAAGH,WAAW,CAACG,cAAc;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,GAAG,EAAE;EACpB,IAAIC,IAAI,GAAG,IAAI,CAACC,QAAQ;EACxB,OAAOR,YAAY,GAAIO,IAAI,CAACD,GAAG,CAAC,KAAKG,SAAS,GAAIL,cAAc,CAACM,IAAI,CAACH,IAAI,EAAED,GAAG,CAAC;AAClF;AAEA,eAAeD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}