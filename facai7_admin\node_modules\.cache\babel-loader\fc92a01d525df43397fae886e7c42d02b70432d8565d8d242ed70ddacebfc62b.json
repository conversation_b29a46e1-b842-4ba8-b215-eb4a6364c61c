{"ast": null, "code": "import { getCurrentInstance, nextTick, onMounted, ref } from \"vue\";\nimport { rolesEnums } from \"@/config/enums\";\nimport { getTokenAUTH } from \"@/utils/auth\";\nexport default {\n  __name: 'editPop',\n  props: [\"item\"],\n  setup(__props, {\n    expose: __expose\n  }) {\n    const form = ref({\n      title: \"\",\n      money: \"\",\n      release: \"\",\n      shares: \"\",\n      cycle: \"\",\n      desc: \"\",\n      img: \"\"\n    });\n    const props = __props;\n    const {\n      proxy\n    } = getCurrentInstance();\n    const headers = ref({});\n    onMounted(() => {\n      headers.value['Accept-Token'] = getTokenAUTH();\n      nextTick(() => {\n        form.value = Object.assign(form, props.item);\n      });\n    });\n    const successUpload = res => {\n      form.value.img = res.data.url;\n    };\n    const handleErr = err => {\n      if (err.status == 320) {\n        form.value.img = JSON.parse(err.message).data.url;\n      }\n    };\n    __expose({\n      form\n    });\n    const __returned__ = {\n      form,\n      props,\n      proxy,\n      headers,\n      successUpload,\n      handleErr,\n      getCurrentInstance,\n      nextTick,\n      onMounted,\n      ref,\n      get rolesEnums() {\n        return rolesEnums;\n      },\n      get getTokenAUTH() {\n        return getTokenAUTH;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["getCurrentInstance", "nextTick", "onMounted", "ref", "rolesEnums", "getTokenAUTH", "form", "title", "money", "release", "shares", "cycle", "desc", "img", "props", "__props", "proxy", "headers", "value", "Object", "assign", "item", "successUpload", "res", "data", "url", "handleErr", "err", "status", "JSON", "parse", "message", "__expose"], "sources": ["D:/WorkSpace/facai7/facai7_admin/src/views/projectManage/components/stockLists/editPop.vue"], "sourcesContent": ["<template>\r\n  <el-form\r\n    label-width=\"100px\"\r\n    :inline=\"true\"\r\n    :model=\"form\"\r\n    class=\"demo-form-inline\"\r\n  >\r\n    <el-form-item label=\"股权名称\" required>\r\n      <el-input v-model=\"form.title\" clearable />\r\n    </el-form-item>\r\n\r\n    <el-form-item label=\"每股的钱\" required>\r\n      <el-input v-model=\"form.money\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"每日释放\" required>\r\n      <el-input v-model=\"form.release\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"股权\" required>\r\n      <el-input v-model=\"form.shares\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"周期\" required>\r\n      <el-input v-model=\"form.cycle\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"股权描述\" required>\r\n      <el-input v-model=\"form.desc\" clearable />\r\n    </el-form-item>\r\n    <el-form-item\r\n      label=\"股权图片\"\r\n      prop=\"img\"\r\n      :rules=\"[{ required: true, message: '请上传图片' }]\"\r\n    >\r\n      <el-upload\r\n        class=\"upload-demo\"\r\n        style=\"width: 114px\"\r\n        :show-file-list=\"false\"\r\n        drag\r\n        :headers=\"headers\"\r\n        :action=\"`${proxy.BASE_API_URL}index/upload`\"\r\n        :on-success=\"successUpload\"\r\n        :on-error=\"handleErr\"\r\n        :multiple=\"false\"\r\n      >\r\n        <img width=\"100\" v-if=\"form.img\" :src=\"proxy.IMG_BASE_URL + form.img\" class=\"avatar\" />\r\n        <el-icon v-else class=\"avatar-uploader-icon\"><Plus /></el-icon\r\n      ></el-upload>\r\n    </el-form-item>\r\n  </el-form>\r\n</template>\r\n\r\n<script setup>\r\nimport { getCurrentInstance, nextTick, onMounted, ref } from \"vue\";\r\nimport { rolesEnums } from \"@/config/enums\";\r\nimport { getTokenAUTH } from \"@/utils/auth\";\r\n\r\nconst form = ref({\r\n  title: \"\",\r\n  money: \"\",\r\n  release: \"\",\r\n  shares: \"\",\r\n  cycle: \"\",\r\n  desc: \"\",\r\n  img: \"\",\r\n});\r\nconst props = defineProps([\"item\"]);\r\n\r\nconst { proxy } = getCurrentInstance()\r\n\r\nconst headers = ref({})\r\nonMounted(() => {\r\n  headers.value['Accept-Token'] = getTokenAUTH()\r\n  nextTick(() => {\r\n    form.value = Object.assign(form, props.item);\r\n  });\r\n});\r\n\r\nconst successUpload = (res) => {\r\n  form.value.img = res.data.url;\r\n};\r\n\r\nconst handleErr = (err) => {\r\n  if (err.status == 320) {\r\n    form.value.img = JSON.parse(err.message).data.url;\r\n  }\r\n}\r\n\r\ndefineExpose({ form });\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.demo-form-inline {\r\n  justify-content: flex-start;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n.demo-form-inline .el-input {\r\n  --el-input-width: 220px;\r\n}\r\n\r\n.demo-form-inline .el-select {\r\n  --el-select-width: 220px;\r\n}\r\n/deep/ .el-radio-group {\r\n  width: 220px;\r\n}\r\n.form-title {\r\n  text-align: left;\r\n  padding-left: 30px;\r\n  margin: 20px auto 10px;\r\n  height: 44px;\r\n  background-color: #f2f2f2;\r\n  border-radius: 5px;\r\n  line-height: 44px;\r\n}\r\n/deep/ .el-form-item {\r\n  align-items: flex-start;\r\n}\r\n</style>\r\n"], "mappings": "AAkDA,SAASA,kBAAkB,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,GAAG,QAAQ,KAAK;AAClE,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,YAAY,QAAQ,cAAc;;;;;;;IAE3C,MAAMC,IAAI,GAAGH,GAAG,CAAC;MACfI,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE,EAAE;MACVC,KAAK,EAAE,EAAE;MACTC,IAAI,EAAE,EAAE;MACRC,GAAG,EAAE;IACP,CAAC,CAAC;IACF,MAAMC,KAAK,GAAGC,OAAqB;IAEnC,MAAM;MAAEC;IAAM,CAAC,GAAGhB,kBAAkB,CAAC,CAAC;IAEtC,MAAMiB,OAAO,GAAGd,GAAG,CAAC,CAAC,CAAC,CAAC;IACvBD,SAAS,CAAC,MAAM;MACde,OAAO,CAACC,KAAK,CAAC,cAAc,CAAC,GAAGb,YAAY,CAAC,CAAC;MAC9CJ,QAAQ,CAAC,MAAM;QACbK,IAAI,CAACY,KAAK,GAAGC,MAAM,CAACC,MAAM,CAACd,IAAI,EAAEQ,KAAK,CAACO,IAAI,CAAC;MAC9C,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,MAAMC,aAAa,GAAIC,GAAG,IAAK;MAC7BjB,IAAI,CAACY,KAAK,CAACL,GAAG,GAAGU,GAAG,CAACC,IAAI,CAACC,GAAG;IAC/B,CAAC;IAED,MAAMC,SAAS,GAAIC,GAAG,IAAK;MACzB,IAAIA,GAAG,CAACC,MAAM,IAAI,GAAG,EAAE;QACrBtB,IAAI,CAACY,KAAK,CAACL,GAAG,GAAGgB,IAAI,CAACC,KAAK,CAACH,GAAG,CAACI,OAAO,CAAC,CAACP,IAAI,CAACC,GAAG;MACnD;IACF,CAAC;IAEDO,QAAY,CAAC;MAAE1B;IAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}