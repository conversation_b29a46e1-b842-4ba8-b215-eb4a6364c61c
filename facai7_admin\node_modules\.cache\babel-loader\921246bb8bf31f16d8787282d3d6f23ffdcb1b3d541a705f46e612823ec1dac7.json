{"ast": null, "code": "import { getCurrentInstance, nextTick, onMounted, ref } from \"vue\";\nimport { withdrawTypeEnums, getLabelByVal, openEnums } from \"@/config/enums\";\nimport { getTokenAUTH } from \"@/utils/auth\";\nexport default {\n  __name: 'editPop',\n  props: [\"item\"],\n  setup(__props, {\n    expose: __expose\n  }) {\n    const form = ref({\n      title: \"\",\n      type: \"\",\n      rate: \"\",\n      bank_name: \"\",\n      bank_branch: \"\",\n      bank_account: \"\",\n      coin_name: \"USDT\",\n      coin_account: \"\",\n      coin_blockchain: \"\",\n      alipay_account: \"\",\n      img: \"\",\n      status: \"\",\n      bank_owner: \"\",\n      remark: \"\"\n    });\n    const props = __props;\n    const headers = ref({});\n    onMounted(() => {\n      headers.value['Accept-Token'] = getTokenAUTH();\n      nextTick(() => {\n        form.value = Object.assign(form, props.item);\n        form.value.coin_name = \"USDT\";\n      });\n    });\n    const {\n      proxy\n    } = getCurrentInstance();\n    const alipaySuccessUpload = res => {\n      form.value.img = res.data.url;\n    };\n    const handleErr1 = err => {\n      if (err.status == 320) {\n        form.value.img = JSON.parse(err.message).data.url;\n      }\n    };\n    const handleErr2 = err => {\n      if (err.status == 320) {\n        form.value.img = JSON.parse(err.message).data.url;\n      }\n    };\n    const wxsuccessUpload = res => {\n      form.value.img = res.data.url;\n    };\n    __expose({\n      form\n    });\n    const __returned__ = {\n      form,\n      props,\n      headers,\n      proxy,\n      alipaySuccessUpload,\n      handleErr1,\n      handleErr2,\n      wxsuccessUpload,\n      getCurrentInstance,\n      nextTick,\n      onMounted,\n      ref,\n      get withdrawTypeEnums() {\n        return withdrawTypeEnums;\n      },\n      get getLabelByVal() {\n        return getLabelByVal;\n      },\n      get openEnums() {\n        return openEnums;\n      },\n      get getTokenAUTH() {\n        return getTokenAUTH;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["getCurrentInstance", "nextTick", "onMounted", "ref", "withdrawTypeEnums", "getLabelByVal", "openEnums", "getTokenAUTH", "form", "title", "type", "rate", "bank_name", "bank_branch", "bank_account", "coin_name", "coin_account", "coin_blockchain", "alipay_account", "img", "status", "bank_owner", "remark", "props", "__props", "headers", "value", "Object", "assign", "item", "proxy", "alipaySuccessUpload", "res", "data", "url", "handleErr1", "err", "JSON", "parse", "message", "handleErr2", "wxsuccessUpload", "__expose"], "sources": ["D:/WorkSpace/facai7/facai7_admin/src/views/payments/components/paymentAccount/editPop.vue"], "sourcesContent": ["<template>\r\n  <el-form\r\n    label-width=\"100px\"\r\n    :inline=\"true\"\r\n    :model=\"form\"\r\n    class=\"demo-form-inline\"\r\n  >\r\n    <el-form-item label=\"标题\" required>\r\n      <el-input v-model=\"form.title\" />\r\n    </el-form-item>\r\n\r\n    <el-form-item label=\"充值方式\" required>\r\n      <el-select v-model=\"form.type\" placeholder=\"充值方式\" clearable>\r\n        <el-option\r\n          v-for=\"item in withdrawTypeEnums\"\r\n          :label=\"item.label\"\r\n          :key=\"item.label\"\r\n          :value=\"item.value\"\r\n        />\r\n      </el-select>\r\n    </el-form-item>\r\n\r\n    <el-form-item label=\"汇率\" required>\r\n      <el-input v-model=\"form.rate\" />\r\n    </el-form-item>\r\n\r\n    <template v-if=\"form.type == 0\">\r\n      <el-form-item label=\"姓名\"  required>\r\n              <el-input v-model=\"form.bank_owner\"  clearable />\r\n          </el-form-item>\r\n      <el-form-item label=\"开户行\" required>\r\n        <el-input v-model=\"form.bank_name\" clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"支行\" required>\r\n        <el-input v-model=\"form.bank_branch\" clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"卡号\" required>\r\n        <el-input v-model=\"form.bank_account\" clearable />\r\n      </el-form-item>\r\n    </template>\r\n    <template v-if=\"form.type == 1\">\r\n      <el-form-item label=\"虚拟币名称\" required>\r\n        <el-input v-model=\"form.coin_name\" disabled clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"钱包地址\" required>\r\n        <el-input v-model=\"form.coin_account\" clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"区块链\" required>\r\n        <el-input v-model=\"form.coin_blockchain\" clearable />\r\n      </el-form-item>\r\n      \r\n    </template>\r\n    <template v-if=\"form.type == 2\">\r\n      <el-form-item label=\"账号\" required>\r\n        <el-input v-model=\"form.alipay_account\" clearable />\r\n      </el-form-item>\r\n      <br />\r\n      <!-- <el-form-item label=\"收款码\" required>\r\n        <img :src=\"form.alipay_img\" alt=\"\" />\r\n      </el-form-item> -->\r\n    </template>\r\n    <template v-if=\"form.type == 4\">\r\n      <el-form-item label=\"收款人\" required>\r\n        <el-input v-model=\"form.bank_name\" clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"账号\" required>\r\n        <el-input v-model=\"form.bank_account\" clearable />\r\n      </el-form-item>\r\n      \r\n    </template>\r\n\r\n    <el-form-item label=\"开启状态\" prop=\"status\">\r\n      <el-select v-model=\"form.status\" placeholder=\"开启状态\" clearable>\r\n        <el-option\r\n          v-for=\"item in openEnums\"\r\n          :label=\"item.label\"\r\n          :key=\"item.label\"\r\n          :value=\"item.value\"\r\n        />\r\n      </el-select>\r\n    </el-form-item>\r\n    <el-form-item label=\"备注\" required>\r\n      <el-input v-model=\"form.remark\" clearable />\r\n    </el-form-item>\r\n    <el-form-item\r\n      v-if=\"form.type == 3\"\r\n      label=\"微信收款码\"\r\n      prop=\"img\"\r\n      :rules=\"[{ required: true, message: '请上传收款码' }]\"\r\n    >\r\n      <el-upload\r\n        class=\"upload-demo\"\r\n        style=\"width: 114px\"\r\n        :show-file-list=\"false\"\r\n        drag\r\n        :headers=\"headers\"\r\n        :action=\"`${proxy.BASE_API_URL}index/upload`\"\r\n        :on-success=\"wxsuccessUpload\"\r\n        :on-error=\"handleErr1\"\r\n        :multiple=\"false\"\r\n      >\r\n        <img\r\n          v-if=\"form.img\"\r\n          :src=\"proxy.IMG_BASE_URL + form.img\"\r\n          width=\"100%\"\r\n          class=\"avatar\" />\r\n        <el-icon v-else class=\"avatar-uploader-icon\"><Plus /></el-icon\r\n      ></el-upload>\r\n    </el-form-item>\r\n    <el-form-item\r\n      v-if=\"form.type == 2\"\r\n      label=\"支付宝收款码\"\r\n      prop=\"alipay_img\"\r\n      :rules=\"[{ required: true, message: '请上传收款码' }]\"\r\n    >\r\n      <el-upload\r\n        class=\"upload-demo\"\r\n        style=\"width: 114px\"\r\n        :show-file-list=\"false\"\r\n        drag\r\n        :headers=\"headers\"\r\n        :action=\"`${proxy.BASE_API_URL}index/upload`\"\r\n        :on-success=\"alipaySuccessUpload\"\r\n        :on-error=\"handleErr2\"\r\n        :multiple=\"false\"\r\n      >\r\n        <img\r\n          v-if=\"form.img\"\r\n          :src=\"proxy.IMG_BASE_URL + form.img\"\r\n          width=\"100%\"\r\n          class=\"avatar\" />\r\n        <el-icon v-else class=\"avatar-uploader-icon\"><Plus /></el-icon\r\n      ></el-upload>\r\n    </el-form-item>\r\n    <el-form-item\r\n      v-if=\"form.type == 4\"\r\n      label=\"收款码（如有）\"\r\n      prop=\"img\"\r\n      :rules=\"[{ required: true, message: '请上传收款码' }]\"\r\n    >\r\n      <el-upload\r\n        class=\"upload-demo\"\r\n        style=\"width: 114px\"\r\n        :show-file-list=\"false\"\r\n        drag\r\n        :headers=\"headers\"\r\n        :action=\"`${proxy.BASE_API_URL}index/upload`\"\r\n        :on-success=\"alipaySuccessUpload\"\r\n        :on-error=\"handleErr2\"\r\n        :multiple=\"false\"\r\n      >\r\n        <img\r\n          v-if=\"form.img\"\r\n          :src=\"proxy.IMG_BASE_URL + form.img\"\r\n          width=\"100%\"\r\n          class=\"avatar\" />\r\n        <el-icon v-else class=\"avatar-uploader-icon\"><Plus /></el-icon\r\n      ></el-upload>\r\n    </el-form-item>\r\n      <el-form-item\r\n       v-if=\"form.type == 1\"\r\n      label=\"钱包二维码\"\r\n      prop=\"img\"\r\n      :rules=\"[{ required: true, message: '请上传钱包二维码' }]\"\r\n    >\r\n      <el-upload\r\n        class=\"upload-demo\"\r\n        style=\"width: 114px\"\r\n        :show-file-list=\"false\"\r\n        drag\r\n        :headers=\"headers\"\r\n        :action=\"`${proxy.BASE_API_URL}index/upload`\"\r\n        :on-success=\"wxsuccessUpload\"\r\n        :on-error=\"handleErr1\"\r\n        :multiple=\"false\"\r\n      >\r\n        <img\r\n          v-if=\"form.img\"\r\n          :src=\"proxy.IMG_BASE_URL + form.img\"\r\n          width=\"100%\"\r\n          class=\"avatar\" />\r\n        <el-icon v-else class=\"avatar-uploader-icon\"><Plus /></el-icon\r\n      ></el-upload>\r\n    </el-form-item>\r\n  </el-form>\r\n</template>\r\n\r\n<script setup>\r\nimport { getCurrentInstance, nextTick, onMounted, ref } from \"vue\";\r\nimport { withdrawTypeEnums, getLabelByVal, openEnums } from \"@/config/enums\";\r\nimport { getTokenAUTH } from \"@/utils/auth\";\r\n\r\nconst form = ref({\r\n  title: \"\",\r\n  type: \"\",\r\n  rate: \"\",\r\n  bank_name: \"\",\r\n  bank_branch: \"\",\r\n  bank_account: \"\",\r\n  coin_name: \"USDT\",\r\n  coin_account: \"\",\r\n  coin_blockchain: \"\",\r\n  alipay_account: \"\",\r\n  img: \"\",\r\n  status: \"\",\r\n  bank_owner: \"\",\r\n  remark: \"\",\r\n});\r\nconst props = defineProps([\"item\"]);\r\n\r\nconst headers = ref({})\r\nonMounted(() => {\r\n  headers.value['Accept-Token'] = getTokenAUTH()\r\n  nextTick(() => {\r\n    form.value = Object.assign(form, props.item);\r\n    form.value.coin_name = \"USDT\"\r\n  });\r\n});\r\n\r\nconst { proxy } = getCurrentInstance();\r\n\r\nconst alipaySuccessUpload = (res) => {\r\n  form.value.img = res.data.url;\r\n};\r\n\r\nconst handleErr1 = (err) => {\r\n  if (err.status == 320) {\r\n    form.value.img = JSON.parse(err.message).data.url;\r\n  }\r\n}\r\n\r\nconst handleErr2 = (err) => {\r\n  if (err.status == 320) {\r\n    form.value.img = JSON.parse(err.message).data.url;\r\n  }\r\n}\r\n\r\nconst wxsuccessUpload = (res) => {\r\n  form.value.img = res.data.url;\r\n};\r\n\r\ndefineExpose({ form });\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.demo-form-inline {\r\n  justify-content: flex-start;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n.demo-form-inline .el-input {\r\n  --el-input-width: 220px;\r\n}\r\n\r\n.demo-form-inline .el-select {\r\n  --el-select-width: 220px;\r\n}\r\n/deep/ .el-radio-group {\r\n  width: 220px;\r\n}\r\n.form-title {\r\n  text-align: left;\r\n  padding-left: 30px;\r\n  margin: 20px auto 10px;\r\n  height: 44px;\r\n  background-color: #f2f2f2;\r\n  border-radius: 5px;\r\n  line-height: 44px;\r\n}\r\n/deep/ .el-form-item {\r\n  align-items: flex-start;\r\n}\r\n</style>\r\n"], "mappings": "AA4LA,SAASA,kBAAkB,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,GAAG,QAAQ,KAAK;AAClE,SAASC,iBAAiB,EAAEC,aAAa,EAAEC,SAAS,QAAQ,gBAAgB;AAC5E,SAASC,YAAY,QAAQ,cAAc;;;;;;;IAE3C,MAAMC,IAAI,GAAGL,GAAG,CAAC;MACfM,KAAK,EAAE,EAAE;MACTC,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,EAAE;MACRC,SAAS,EAAE,EAAE;MACbC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE,MAAM;MACjBC,YAAY,EAAE,EAAE;MAChBC,eAAe,EAAE,EAAE;MACnBC,cAAc,EAAE,EAAE;MAClBC,GAAG,EAAE,EAAE;MACPC,MAAM,EAAE,EAAE;MACVC,UAAU,EAAE,EAAE;MACdC,MAAM,EAAE;IACV,CAAC,CAAC;IACF,MAAMC,KAAK,GAAGC,OAAqB;IAEnC,MAAMC,OAAO,GAAGtB,GAAG,CAAC,CAAC,CAAC,CAAC;IACvBD,SAAS,CAAC,MAAM;MACduB,OAAO,CAACC,KAAK,CAAC,cAAc,CAAC,GAAGnB,YAAY,CAAC,CAAC;MAC9CN,QAAQ,CAAC,MAAM;QACbO,IAAI,CAACkB,KAAK,GAAGC,MAAM,CAACC,MAAM,CAACpB,IAAI,EAAEe,KAAK,CAACM,IAAI,CAAC;QAC5CrB,IAAI,CAACkB,KAAK,CAACX,SAAS,GAAG,MAAM;MAC/B,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,MAAM;MAAEe;IAAM,CAAC,GAAG9B,kBAAkB,CAAC,CAAC;IAEtC,MAAM+B,mBAAmB,GAAIC,GAAG,IAAK;MACnCxB,IAAI,CAACkB,KAAK,CAACP,GAAG,GAAGa,GAAG,CAACC,IAAI,CAACC,GAAG;IAC/B,CAAC;IAED,MAAMC,UAAU,GAAIC,GAAG,IAAK;MAC1B,IAAIA,GAAG,CAAChB,MAAM,IAAI,GAAG,EAAE;QACrBZ,IAAI,CAACkB,KAAK,CAACP,GAAG,GAAGkB,IAAI,CAACC,KAAK,CAACF,GAAG,CAACG,OAAO,CAAC,CAACN,IAAI,CAACC,GAAG;MACnD;IACF,CAAC;IAED,MAAMM,UAAU,GAAIJ,GAAG,IAAK;MAC1B,IAAIA,GAAG,CAAChB,MAAM,IAAI,GAAG,EAAE;QACrBZ,IAAI,CAACkB,KAAK,CAACP,GAAG,GAAGkB,IAAI,CAACC,KAAK,CAACF,GAAG,CAACG,OAAO,CAAC,CAACN,IAAI,CAACC,GAAG;MACnD;IACF,CAAC;IAED,MAAMO,eAAe,GAAIT,GAAG,IAAK;MAC/BxB,IAAI,CAACkB,KAAK,CAACP,GAAG,GAAGa,GAAG,CAACC,IAAI,CAACC,GAAG;IAC/B,CAAC;IAEDQ,QAAY,CAAC;MAAElC;IAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}