{"ast": null, "code": "import { nextTick, onMounted, ref } from 'vue';\nimport { userWalletTypeEnums, getLabelByVal } from '@/config/enums';\nexport default {\n  __name: 'editPop',\n  props: ['item'],\n  setup(__props, {\n    expose: __expose\n  }) {\n    const form = ref({\n      // recharge_money: '',\n      // withdraw_money: '',\n      phone: '',\n      default: '',\n      type: '',\n      bank_name: '',\n      bank_branch: '',\n      bank_account: '',\n      coin_name: '',\n      coin_blockchain: '',\n      coin_account: '',\n      alipay_account: ''\n    });\n    const props = __props;\n    onMounted(() => {\n      nextTick(() => {\n        form.value = Object.assign(form, props.item);\n      });\n    });\n    __expose({\n      form\n    });\n    const __returned__ = {\n      form,\n      props,\n      nextTick,\n      onMounted,\n      ref,\n      get userWalletTypeEnums() {\n        return userWalletTypeEnums;\n      },\n      get getLabelByVal() {\n        return getLabelByVal;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["nextTick", "onMounted", "ref", "userWalletTypeEnums", "getLabelByVal", "form", "phone", "default", "type", "bank_name", "bank_branch", "bank_account", "coin_name", "coin_blockchain", "coin_account", "alipay_account", "props", "__props", "value", "Object", "assign", "item", "__expose"], "sources": ["D:/WorkSpace/facai7/facai7_admin/src/views/userManage/components/userWallet/editPop.vue"], "sourcesContent": ["<template>\r\n    <el-form label-width=\"100px\" :inline=\"true\" :model=\"form\" class=\"demo-form-inline\">\r\n        <el-form-item label=\"手机号码\" required >\r\n            <el-input v-model=\"form.phone\"  />\r\n        </el-form-item>\r\n        <!-- <el-form-item label=\"总充值\" required >\r\n            <el-input v-model=\"form.recharge_money\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"总提款\" required >\r\n            <el-input v-model=\"form.withdraw_money\" disabled />\r\n        </el-form-item> -->\r\n        <el-form-item label=\"钱包类型\" required>\r\n            <el-select v-model=\"form.type\" placeholder=\"\" clearable>\r\n                <el-option v-for=\"item in userWalletTypeEnums\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"是否默认\" required>\r\n            <el-radio-group v-model=\"form.default\">\r\n                <el-radio :value=\"1\">是</el-radio>\r\n                <el-radio :value=\"0\">否</el-radio>\r\n            </el-radio-group>\r\n        </el-form-item>\r\n       \r\n        <template v-if=\"form.type == 0\">\r\n            <el-form-item label=\"银行名称\"  required>\r\n                <el-input v-model=\"form.bank_name\"  clearable />\r\n            </el-form-item>\r\n            <el-form-item label=\"银行支行\"  required>\r\n                <el-input v-model=\"form.bank_branch\"  clearable />\r\n            </el-form-item>\r\n            <el-form-item label=\"银行账号\"  required>\r\n                <el-input v-model=\"form.bank_account\"  clearable />\r\n            </el-form-item>\r\n        </template>\r\n        <template v-if=\"form.type == 1\">\r\n            <el-form-item label=\"币种\"  required>\r\n                <el-input v-model=\"form.coin_name\"  clearable />\r\n            </el-form-item>\r\n            <el-form-item label=\"区块链\"  required>\r\n                <el-input v-model=\"form.coin_blockchain\"  clearable />\r\n            </el-form-item>\r\n            <el-form-item label=\"地址\"  required>\r\n                <el-input v-model=\"form.coin_account\"  clearable />\r\n            </el-form-item>\r\n        </template>\r\n        <template v-if=\"form.type == 2 || form.type == 3\">\r\n            <el-form-item label=\"账号\"  required>\r\n                <el-input v-model=\"form.alipay_account\"  clearable />\r\n            </el-form-item>\r\n            <br>\r\n           \r\n        </template>\r\n    </el-form>\r\n</template>\r\n\r\n<script setup>\r\nimport { nextTick, onMounted, ref } from 'vue'\r\nimport {userWalletTypeEnums, getLabelByVal} from '@/config/enums'\r\n\r\nconst form = ref({\r\n    // recharge_money: '',\r\n    // withdraw_money: '',\r\n    phone: '',\r\n    default: '',\r\n    type: '',\r\n    bank_name: '',\r\n    bank_branch: '',\r\n    bank_account: '',\r\n    coin_name: '',\r\n    coin_blockchain: '',\r\n    coin_account: '',\r\n    alipay_account: '',\r\n})\r\nconst props = defineProps(['item'])\r\n\r\nonMounted(() => {\r\n    nextTick(()=> {\r\n        form.value = Object.assign(form, props.item)\r\n    })\r\n})\r\n\r\ndefineExpose({form})\r\n\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.demo-form-inline {\r\n    justify-content: flex-start;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n}\r\n.demo-form-inline .el-input {\r\n    --el-input-width: 220px;\r\n}\r\n\r\n.demo-form-inline .el-select {\r\n    --el-select-width: 220px;\r\n}\r\n/deep/ .el-radio-group {\r\n    width: 220px;\r\n}\r\n.form-title {\r\n text-align: left;\r\n padding-left: 30px;\r\n margin: 20px auto 10px;\r\n height: 44px;\r\n background-color: #f2f2f2;\r\n border-radius: 5px;\r\n line-height: 44px;\r\n}\r\n/deep/  .el-form-item {\r\n    align-items: flex-start;\r\n}\r\n</style>"], "mappings": "AAwDA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,GAAG,QAAQ,KAAK;AAC9C,SAAQC,mBAAmB,EAAEC,aAAa,QAAO,gBAAgB;;;;;;;IAEjE,MAAMC,IAAI,GAAGH,GAAG,CAAC;MACb;MACA;MACAI,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,IAAI,EAAE,EAAE;MACRC,SAAS,EAAE,EAAE;MACbC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE,EAAE;MACbC,eAAe,EAAE,EAAE;MACnBC,YAAY,EAAE,EAAE;MAChBC,cAAc,EAAE;IACpB,CAAC,CAAC;IACF,MAAMC,KAAK,GAAGC,OAAqB;IAEnChB,SAAS,CAAC,MAAM;MACZD,QAAQ,CAAC,MAAK;QACVK,IAAI,CAACa,KAAK,GAAGC,MAAM,CAACC,MAAM,CAACf,IAAI,EAAEW,KAAK,CAACK,IAAI,CAAC;MAChD,CAAC,CAAC;IACN,CAAC,CAAC;IAEFC,QAAY,CAAC;MAACjB;IAAI,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}