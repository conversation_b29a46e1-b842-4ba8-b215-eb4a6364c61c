{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Spanish (Dominican Republic) [es-do]\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var monthsShortDot = 'ene._feb._mar._abr._may._jun._jul._ago._sep._oct._nov._dic.'.split('_'),\n    monthsShort = 'ene_feb_mar_abr_may_jun_jul_ago_sep_oct_nov_dic'.split('_'),\n    monthsParse = [/^ene/i, /^feb/i, /^mar/i, /^abr/i, /^may/i, /^jun/i, /^jul/i, /^ago/i, /^sep/i, /^oct/i, /^nov/i, /^dic/i],\n    monthsRegex = /^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre|ene\\.?|feb\\.?|mar\\.?|abr\\.?|may\\.?|jun\\.?|jul\\.?|ago\\.?|sep\\.?|oct\\.?|nov\\.?|dic\\.?)/i;\n  var esDo = moment.defineLocale('es-do', {\n    months: 'enero_febrero_marzo_abril_mayo_junio_julio_agosto_septiembre_octubre_noviembre_diciembre'.split('_'),\n    monthsShort: function (m, format) {\n      if (!m) {\n        return monthsShortDot;\n      } else if (/-MMM-/.test(format)) {\n        return monthsShort[m.month()];\n      } else {\n        return monthsShortDot[m.month()];\n      }\n    },\n    monthsRegex: monthsRegex,\n    monthsShortRegex: monthsRegex,\n    monthsStrictRegex: /^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)/i,\n    monthsShortStrictRegex: /^(ene\\.?|feb\\.?|mar\\.?|abr\\.?|may\\.?|jun\\.?|jul\\.?|ago\\.?|sep\\.?|oct\\.?|nov\\.?|dic\\.?)/i,\n    monthsParse: monthsParse,\n    longMonthsParse: monthsParse,\n    shortMonthsParse: monthsParse,\n    weekdays: 'domingo_lunes_martes_miércoles_jueves_viernes_sábado'.split('_'),\n    weekdaysShort: 'dom._lun._mar._mié._jue._vie._sáb.'.split('_'),\n    weekdaysMin: 'do_lu_ma_mi_ju_vi_sá'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'h:mm A',\n      LTS: 'h:mm:ss A',\n      L: 'DD/MM/YYYY',\n      LL: 'D [de] MMMM [de] YYYY',\n      LLL: 'D [de] MMMM [de] YYYY h:mm A',\n      LLLL: 'dddd, D [de] MMMM [de] YYYY h:mm A'\n    },\n    calendar: {\n      sameDay: function () {\n        return '[hoy a la' + (this.hours() !== 1 ? 's' : '') + '] LT';\n      },\n      nextDay: function () {\n        return '[mañana a la' + (this.hours() !== 1 ? 's' : '') + '] LT';\n      },\n      nextWeek: function () {\n        return 'dddd [a la' + (this.hours() !== 1 ? 's' : '') + '] LT';\n      },\n      lastDay: function () {\n        return '[ayer a la' + (this.hours() !== 1 ? 's' : '') + '] LT';\n      },\n      lastWeek: function () {\n        return '[el] dddd [pasado a la' + (this.hours() !== 1 ? 's' : '') + '] LT';\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'en %s',\n      past: 'hace %s',\n      s: 'unos segundos',\n      ss: '%d segundos',\n      m: 'un minuto',\n      mm: '%d minutos',\n      h: 'una hora',\n      hh: '%d horas',\n      d: 'un día',\n      dd: '%d días',\n      w: 'una semana',\n      ww: '%d semanas',\n      M: 'un mes',\n      MM: '%d meses',\n      y: 'un año',\n      yy: '%d años'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}º/,\n    ordinal: '%dº',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return esDo;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "monthsShortDot", "split", "monthsShort", "<PERSON><PERSON><PERSON>e", "monthsRegex", "esDo", "defineLocale", "months", "m", "format", "test", "month", "monthsShortRegex", "monthsStrictRegex", "monthsShortStrictRegex", "longMonthsParse", "shortMonthsParse", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "hours", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "mm", "h", "hh", "d", "dd", "w", "ww", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy"], "sources": ["D:/WorkSpace/facai7/facai7_admin/node_modules/moment/locale/es-do.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Spanish (Dominican Republic) [es-do]\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var monthsShortDot =\n            'ene._feb._mar._abr._may._jun._jul._ago._sep._oct._nov._dic.'.split(\n                '_'\n            ),\n        monthsShort = 'ene_feb_mar_abr_may_jun_jul_ago_sep_oct_nov_dic'.split('_'),\n        monthsParse = [\n            /^ene/i,\n            /^feb/i,\n            /^mar/i,\n            /^abr/i,\n            /^may/i,\n            /^jun/i,\n            /^jul/i,\n            /^ago/i,\n            /^sep/i,\n            /^oct/i,\n            /^nov/i,\n            /^dic/i,\n        ],\n        monthsRegex =\n            /^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre|ene\\.?|feb\\.?|mar\\.?|abr\\.?|may\\.?|jun\\.?|jul\\.?|ago\\.?|sep\\.?|oct\\.?|nov\\.?|dic\\.?)/i;\n\n    var esDo = moment.defineLocale('es-do', {\n        months: 'enero_febrero_marzo_abril_mayo_junio_julio_agosto_septiembre_octubre_noviembre_diciembre'.split(\n            '_'\n        ),\n        monthsShort: function (m, format) {\n            if (!m) {\n                return monthsShortDot;\n            } else if (/-MMM-/.test(format)) {\n                return monthsShort[m.month()];\n            } else {\n                return monthsShortDot[m.month()];\n            }\n        },\n        monthsRegex: monthsRegex,\n        monthsShortRegex: monthsRegex,\n        monthsStrictRegex:\n            /^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)/i,\n        monthsShortStrictRegex:\n            /^(ene\\.?|feb\\.?|mar\\.?|abr\\.?|may\\.?|jun\\.?|jul\\.?|ago\\.?|sep\\.?|oct\\.?|nov\\.?|dic\\.?)/i,\n        monthsParse: monthsParse,\n        longMonthsParse: monthsParse,\n        shortMonthsParse: monthsParse,\n        weekdays: 'domingo_lunes_martes_miércoles_jueves_viernes_sábado'.split('_'),\n        weekdaysShort: 'dom._lun._mar._mié._jue._vie._sáb.'.split('_'),\n        weekdaysMin: 'do_lu_ma_mi_ju_vi_sá'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'h:mm A',\n            LTS: 'h:mm:ss A',\n            L: 'DD/MM/YYYY',\n            LL: 'D [de] MMMM [de] YYYY',\n            LLL: 'D [de] MMMM [de] YYYY h:mm A',\n            LLLL: 'dddd, D [de] MMMM [de] YYYY h:mm A',\n        },\n        calendar: {\n            sameDay: function () {\n                return '[hoy a la' + (this.hours() !== 1 ? 's' : '') + '] LT';\n            },\n            nextDay: function () {\n                return '[mañana a la' + (this.hours() !== 1 ? 's' : '') + '] LT';\n            },\n            nextWeek: function () {\n                return 'dddd [a la' + (this.hours() !== 1 ? 's' : '') + '] LT';\n            },\n            lastDay: function () {\n                return '[ayer a la' + (this.hours() !== 1 ? 's' : '') + '] LT';\n            },\n            lastWeek: function () {\n                return (\n                    '[el] dddd [pasado a la' +\n                    (this.hours() !== 1 ? 's' : '') +\n                    '] LT'\n                );\n            },\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'en %s',\n            past: 'hace %s',\n            s: 'unos segundos',\n            ss: '%d segundos',\n            m: 'un minuto',\n            mm: '%d minutos',\n            h: 'una hora',\n            hh: '%d horas',\n            d: 'un día',\n            dd: '%d días',\n            w: 'una semana',\n            ww: '%d semanas',\n            M: 'un mes',\n            MM: '%d meses',\n            y: 'un año',\n            yy: '%d años',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}º/,\n        ordinal: '%dº',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return esDo;\n\n})));\n"], "mappings": "AAAA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,cAAc,GACV,6DAA6D,CAACC,KAAK,CAC/D,GACJ,CAAC;IACLC,WAAW,GAAG,iDAAiD,CAACD,KAAK,CAAC,GAAG,CAAC;IAC1EE,WAAW,GAAG,CACV,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,CACV;IACDC,WAAW,GACP,kLAAkL;EAE1L,IAAIC,IAAI,GAAGN,MAAM,CAACO,YAAY,CAAC,OAAO,EAAE;IACpCC,MAAM,EAAE,0FAA0F,CAACN,KAAK,CACpG,GACJ,CAAC;IACDC,WAAW,EAAE,SAAAA,CAAUM,CAAC,EAAEC,MAAM,EAAE;MAC9B,IAAI,CAACD,CAAC,EAAE;QACJ,OAAOR,cAAc;MACzB,CAAC,MAAM,IAAI,OAAO,CAACU,IAAI,CAACD,MAAM,CAAC,EAAE;QAC7B,OAAOP,WAAW,CAACM,CAAC,CAACG,KAAK,CAAC,CAAC,CAAC;MACjC,CAAC,MAAM;QACH,OAAOX,cAAc,CAACQ,CAAC,CAACG,KAAK,CAAC,CAAC,CAAC;MACpC;IACJ,CAAC;IACDP,WAAW,EAAEA,WAAW;IACxBQ,gBAAgB,EAAER,WAAW;IAC7BS,iBAAiB,EACb,8FAA8F;IAClGC,sBAAsB,EAClB,yFAAyF;IAC7FX,WAAW,EAAEA,WAAW;IACxBY,eAAe,EAAEZ,WAAW;IAC5Ba,gBAAgB,EAAEb,WAAW;IAC7Bc,QAAQ,EAAE,sDAAsD,CAAChB,KAAK,CAAC,GAAG,CAAC;IAC3EiB,aAAa,EAAE,oCAAoC,CAACjB,KAAK,CAAC,GAAG,CAAC;IAC9DkB,WAAW,EAAE,sBAAsB,CAAClB,KAAK,CAAC,GAAG,CAAC;IAC9CmB,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,QAAQ;MACZC,GAAG,EAAE,WAAW;MAChBC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,uBAAuB;MAC3BC,GAAG,EAAE,8BAA8B;MACnCC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,SAAAA,CAAA,EAAY;QACjB,OAAO,WAAW,IAAI,IAAI,CAACC,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,MAAM;MACjE,CAAC;MACDC,OAAO,EAAE,SAAAA,CAAA,EAAY;QACjB,OAAO,cAAc,IAAI,IAAI,CAACD,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,MAAM;MACpE,CAAC;MACDE,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,OAAO,YAAY,IAAI,IAAI,CAACF,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,MAAM;MAClE,CAAC;MACDG,OAAO,EAAE,SAAAA,CAAA,EAAY;QACjB,OAAO,YAAY,IAAI,IAAI,CAACH,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,MAAM;MAClE,CAAC;MACDI,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,OACI,wBAAwB,IACvB,IAAI,CAACJ,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,GAC/B,MAAM;MAEd,CAAC;MACDK,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAE,SAAS;MACfC,CAAC,EAAE,eAAe;MAClBC,EAAE,EAAE,aAAa;MACjBhC,CAAC,EAAE,WAAW;MACdiC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,UAAU;IAClCC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOnD,IAAI;AAEf,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}