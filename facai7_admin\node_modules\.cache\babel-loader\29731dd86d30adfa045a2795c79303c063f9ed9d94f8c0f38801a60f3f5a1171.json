{"ast": null, "code": "import { rolesEnums } from \"@/config/enums\";\nimport { onBeforeUnmount, nextTick, ref, shallowRef, onMounted, getCurrentInstance } from \"vue\";\nimport fileUploadHoook from \"@/hooks/fileUpload\";\nimport { htmlDecodeByRegExp } from \"@/utils/utils\";\nimport { getTokenAUTH } from \"@/utils/auth\";\nexport default {\n  __name: 'editPop',\n  props: [\"item\"],\n  setup(__props, {\n    expose: __expose\n  }) {\n    const form = ref({\n      users: \"\",\n      title: \"\",\n      content: \"\"\n    });\n    const props = __props;\n    const headers = ref({});\n    const {\n      proxy\n    } = getCurrentInstance();\n    onMounted(() => {\n      headers.value['Accept-Token'] = getTokenAUTH();\n      nextTick(() => {\n        form.value = Object.assign(form.value, props.item);\n        form.value.content = htmlDecodeByRegExp(props.item.content || '');\n      });\n    });\n\n    // 编辑器实例，必须用 shallowRef\n    const editorRef = shallowRef();\n\n    // 内容 HTML\n    const valueHtml = ref(\"<p>hello</p>\");\n    const mode = ref(\"default\");\n    const toolbarConfig = {};\n    const editorConfig = {\n      placeholder: \"请输入内容...\",\n      MENU_CONF: {\n        uploadImage: {\n          fieldName: \"file\",\n          maxFileSize: 10 * 1024 * 1024,\n          // 10M\n          server: proxy.BASE_API_URL + \"index/uploadX\",\n          headers: {\n            \"Accept-Token\": getTokenAUTH()\n          },\n          customInsert(res, insertFn) {\n            console.log(res);\n            const url = proxy.IMG_BASE_URL + res.data.url;\n            const alt = res.data.alt;\n            const href = res.data.href;\n            insertFn(url, alt, href);\n          },\n          onError(file, err, res) {\n            console.log(`${file.name} 上传出错`, err, res);\n          }\n        }\n      }\n    };\n\n    // 组件销毁时，也及时销毁编辑器\n    onBeforeUnmount(() => {\n      const editor = editorRef.value;\n      if (editor == null) return;\n      editor.destroy();\n    });\n    const handleCreated = editor => {\n      editorRef.value = editor; // 记录 editor 实例，重要！\n    };\n    __expose({\n      form\n    });\n    const __returned__ = {\n      form,\n      props,\n      headers,\n      proxy,\n      editorRef,\n      valueHtml,\n      mode,\n      toolbarConfig,\n      editorConfig,\n      handleCreated,\n      get rolesEnums() {\n        return rolesEnums;\n      },\n      onBeforeUnmount,\n      nextTick,\n      ref,\n      shallowRef,\n      onMounted,\n      getCurrentInstance,\n      get fileUploadHoook() {\n        return fileUploadHoook;\n      },\n      get htmlDecodeByRegExp() {\n        return htmlDecodeByRegExp;\n      },\n      get getTokenAUTH() {\n        return getTokenAUTH;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["rolesEnums", "onBeforeUnmount", "nextTick", "ref", "shallowRef", "onMounted", "getCurrentInstance", "fileUploadHoook", "htmlDecodeByRegExp", "getTokenAUTH", "form", "users", "title", "content", "props", "__props", "headers", "proxy", "value", "Object", "assign", "item", "editor<PERSON><PERSON>", "valueHtml", "mode", "toolbarConfig", "editorConfig", "placeholder", "MENU_CONF", "uploadImage", "fieldName", "maxFileSize", "server", "BASE_API_URL", "customInsert", "res", "insertFn", "console", "log", "url", "IMG_BASE_URL", "data", "alt", "href", "onError", "file", "err", "name", "editor", "destroy", "handleCreated", "__expose"], "sources": ["D:/WorkSpace/facai7/facai7_admin/src/views/operationManage/components/messageLists/editPop.vue"], "sourcesContent": ["<template>\r\n  <el-form\r\n    label-width=\"300px\"\r\n    :inline=\"true\"\r\n    :model=\"form\"\r\n    class=\"demo-form-inline\"\r\n  >\r\n    <el-form-item label=\"电话号码(,分割)输入all发给全部用户\" required>\r\n      <el-input v-model=\"form.users\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"标题\" required>\r\n      <el-input v-model=\"form.title\" clearable />\r\n    </el-form-item>\r\n  </el-form>\r\n  <div style=\"border: 1px solid #ccc\">\r\n      <Toolbar\r\n        style=\"border-bottom: 1px solid #ccc\"\r\n        :editor=\"editorRef\"\r\n        :defaultConfig=\"toolbarConfig\"\r\n        :mode=\"mode\"\r\n      />\r\n      <Editor\r\n        style=\"height: 500px; overflow-y: hidden\"\r\n        v-model=\"form.content\"\r\n        :defaultConfig=\"editorConfig\"\r\n        :mode=\"mode\"\r\n        @onCreated=\"handleCreated\"\r\n      />\r\n    </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { rolesEnums } from \"@/config/enums\";\r\nimport {\r\n  onBeforeUnmount,\r\n  nextTick,\r\n  ref,\r\n  shallowRef,\r\n  onMounted,\r\n  getCurrentInstance,\r\n} from \"vue\";\r\nimport fileUploadHoook from \"@/hooks/fileUpload\";\r\nimport { htmlDecodeByRegExp } from \"@/utils/utils\";\r\nimport { getTokenAUTH } from \"@/utils/auth\";\r\n\r\nconst form = ref({\r\n  users: \"\",\r\n  title: \"\",\r\n  content: \"\",\r\n});\r\nconst props = defineProps([\"item\"]);\r\n\r\nconst headers = ref({})\r\nconst { proxy } = getCurrentInstance();\r\nonMounted(() => {\r\n  headers.value['Accept-Token'] = getTokenAUTH()\r\n  nextTick(() => {\r\n    form.value = Object.assign(form.value, props.item);\r\n    form.value.content = htmlDecodeByRegExp(props.item.content || '');\r\n\r\n  });\r\n});\r\n\r\n// 编辑器实例，必须用 shallowRef\r\nconst editorRef = shallowRef();\r\n\r\n// 内容 HTML\r\nconst valueHtml = ref(\"<p>hello</p>\");\r\nconst mode = ref(\"default\");\r\n\r\nconst toolbarConfig = {};\r\nconst editorConfig = {\r\n  placeholder: \"请输入内容...\",\r\n  MENU_CONF: {\r\n    uploadImage: {\r\n      fieldName: \"file\",\r\n      maxFileSize: 10 * 1024 * 1024, // 10M\r\n      server: proxy.BASE_API_URL + \"index/uploadX\",\r\n      headers: {\r\n        \"Accept-Token\": getTokenAUTH(),\r\n      },\r\n      customInsert(res, insertFn) {\r\n        console.log(res)\r\n        const url = proxy.IMG_BASE_URL + res.data.url;\r\n        const alt = res.data.alt\r\n        const href = res.data.href\r\n        insertFn(url, alt, href);\r\n      },\r\n      onError(file, err, res) {\r\n        console.log(`${file.name} 上传出错`, err, res)\r\n      }\r\n    },\r\n  },\r\n};\r\n\r\n// 组件销毁时，也及时销毁编辑器\r\nonBeforeUnmount(() => {\r\n  const editor = editorRef.value;\r\n  if (editor == null) return;\r\n  editor.destroy();\r\n});\r\n\r\nconst handleCreated = (editor) => {\r\n  editorRef.value = editor; // 记录 editor 实例，重要！\r\n};\r\n\r\ndefineExpose({ form });\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.demo-form-inline {\r\n  justify-content: flex-start;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.demo-form-inline .el-input {\r\n  --el-input-width: 420px;\r\n}\r\n\r\n.demo-form-inline .el-select {\r\n  --el-select-width: 420px;\r\n}\r\n\r\n/deep/ .el-radio-group {\r\n  width: 420px;\r\n}\r\n\r\n.form-title {\r\n  text-align: left;\r\n  padding-left: 30px;\r\n  margin: 20px auto 10px;\r\n  height: 44px;\r\n  background-color: #f2f2f2;\r\n  border-radius: 5px;\r\n  line-height: 44px;\r\n}\r\n\r\n/deep/ .el-form-item {\r\n  align-items: flex-start;\r\n}\r\n</style>\r\n"], "mappings": "AAgCA,SAASA,UAAU,QAAQ,gBAAgB;AAC3C,SACEC,eAAe,EACfC,QAAQ,EACRC,GAAG,EACHC,UAAU,EACVC,SAAS,EACTC,kBAAkB,QACb,KAAK;AACZ,OAAOC,eAAe,MAAM,oBAAoB;AAChD,SAASC,kBAAkB,QAAQ,eAAe;AAClD,SAASC,YAAY,QAAQ,cAAc;;;;;;;IAE3C,MAAMC,IAAI,GAAGP,GAAG,CAAC;MACfQ,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE;IACX,CAAC,CAAC;IACF,MAAMC,KAAK,GAAGC,OAAqB;IAEnC,MAAMC,OAAO,GAAGb,GAAG,CAAC,CAAC,CAAC,CAAC;IACvB,MAAM;MAAEc;IAAM,CAAC,GAAGX,kBAAkB,CAAC,CAAC;IACtCD,SAAS,CAAC,MAAM;MACdW,OAAO,CAACE,KAAK,CAAC,cAAc,CAAC,GAAGT,YAAY,CAAC,CAAC;MAC9CP,QAAQ,CAAC,MAAM;QACbQ,IAAI,CAACQ,KAAK,GAAGC,MAAM,CAACC,MAAM,CAACV,IAAI,CAACQ,KAAK,EAAEJ,KAAK,CAACO,IAAI,CAAC;QAClDX,IAAI,CAACQ,KAAK,CAACL,OAAO,GAAGL,kBAAkB,CAACM,KAAK,CAACO,IAAI,CAACR,OAAO,IAAI,EAAE,CAAC;MAEnE,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA,MAAMS,SAAS,GAAGlB,UAAU,CAAC,CAAC;;IAE9B;IACA,MAAMmB,SAAS,GAAGpB,GAAG,CAAC,cAAc,CAAC;IACrC,MAAMqB,IAAI,GAAGrB,GAAG,CAAC,SAAS,CAAC;IAE3B,MAAMsB,aAAa,GAAG,CAAC,CAAC;IACxB,MAAMC,YAAY,GAAG;MACnBC,WAAW,EAAE,UAAU;MACvBC,SAAS,EAAE;QACTC,WAAW,EAAE;UACXC,SAAS,EAAE,MAAM;UACjBC,WAAW,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;UAAE;UAC/BC,MAAM,EAAEf,KAAK,CAACgB,YAAY,GAAG,eAAe;UAC5CjB,OAAO,EAAE;YACP,cAAc,EAAEP,YAAY,CAAC;UAC/B,CAAC;UACDyB,YAAYA,CAACC,GAAG,EAAEC,QAAQ,EAAE;YAC1BC,OAAO,CAACC,GAAG,CAACH,GAAG,CAAC;YAChB,MAAMI,GAAG,GAAGtB,KAAK,CAACuB,YAAY,GAAGL,GAAG,CAACM,IAAI,CAACF,GAAG;YAC7C,MAAMG,GAAG,GAAGP,GAAG,CAACM,IAAI,CAACC,GAAG;YACxB,MAAMC,IAAI,GAAGR,GAAG,CAACM,IAAI,CAACE,IAAI;YAC1BP,QAAQ,CAACG,GAAG,EAAEG,GAAG,EAAEC,IAAI,CAAC;UAC1B,CAAC;UACDC,OAAOA,CAACC,IAAI,EAAEC,GAAG,EAAEX,GAAG,EAAE;YACtBE,OAAO,CAACC,GAAG,CAAC,GAAGO,IAAI,CAACE,IAAI,OAAO,EAAED,GAAG,EAAEX,GAAG,CAAC;UAC5C;QACF;MACF;IACF,CAAC;;IAED;IACAlC,eAAe,CAAC,MAAM;MACpB,MAAM+C,MAAM,GAAG1B,SAAS,CAACJ,KAAK;MAC9B,IAAI8B,MAAM,IAAI,IAAI,EAAE;MACpBA,MAAM,CAACC,OAAO,CAAC,CAAC;IAClB,CAAC,CAAC;IAEF,MAAMC,aAAa,GAAIF,MAAM,IAAK;MAChC1B,SAAS,CAACJ,KAAK,GAAG8B,MAAM,CAAC,CAAC;IAC5B,CAAC;IAEDG,QAAY,CAAC;MAAEzC;IAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}