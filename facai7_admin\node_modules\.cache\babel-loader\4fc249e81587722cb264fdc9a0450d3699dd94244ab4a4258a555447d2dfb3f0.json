{"ast": null, "code": "import createCompounder from './_createCompounder.js';\n\n/**\n * Converts `string`, as space separated words, to lower case.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to convert.\n * @returns {string} Returns the lower cased string.\n * @example\n *\n * _.lowerCase('--Foo-Bar--');\n * // => 'foo bar'\n *\n * _.lowerCase('fooBar');\n * // => 'foo bar'\n *\n * _.lowerCase('__FOO_BAR__');\n * // => 'foo bar'\n */\nvar lowerCase = createCompounder(function (result, word, index) {\n  return result + (index ? ' ' : '') + word.toLowerCase();\n});\nexport default lowerCase;", "map": {"version": 3, "names": ["createCompounder", "lowerCase", "result", "word", "index", "toLowerCase"], "sources": ["D:/WorkSpace/facai7/facai7_admin/node_modules/lodash-es/lowerCase.js"], "sourcesContent": ["import createCompounder from './_createCompounder.js';\n\n/**\n * Converts `string`, as space separated words, to lower case.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to convert.\n * @returns {string} Returns the lower cased string.\n * @example\n *\n * _.lowerCase('--Foo-Bar--');\n * // => 'foo bar'\n *\n * _.lowerCase('fooBar');\n * // => 'foo bar'\n *\n * _.lowerCase('__FOO_BAR__');\n * // => 'foo bar'\n */\nvar lowerCase = createCompounder(function(result, word, index) {\n  return result + (index ? ' ' : '') + word.toLowerCase();\n});\n\nexport default lowerCase;\n"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,wBAAwB;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,SAAS,GAAGD,gBAAgB,CAAC,UAASE,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE;EAC7D,OAAOF,MAAM,IAAIE,KAAK,GAAG,GAAG,GAAG,EAAE,CAAC,GAAGD,IAAI,CAACE,WAAW,CAAC,CAAC;AACzD,CAAC,CAAC;AAEF,eAAeJ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}