{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport store from \"@/store\";\nimport { ref, watch } from \"vue\";\nimport { useRoute, useRouter } from \"vue-router/dist/vue-router\";\nexport default {\n  __name: 'leftMenu',\n  props: [\"isCollapse\"],\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const menuList = ref([{\n      name: \"用户管理\",\n      index: \"1\",\n      path: \"/\",\n      icon: \"User\",\n      children: [{\n        name: \"用户列表\",\n        index: \"1-1\",\n        path: \"/userList\"\n      }, {\n        name: \"团队列表\",\n        index: \"1-41\",\n        path: \"/teamList\"\n      }, {\n        name: \"钱包管理\",\n        index: \"1-8\",\n        path: \"/userWallet\"\n      }, {\n        name: \"地址管理\",\n        index: \"1-9\",\n        path: \"/userAddress\"\n      }, {\n        name: \"实名审核\",\n        index: \"1-11\",\n        path: \"/realnameList\"\n      }, {\n        name: \"用户登录记录\",\n        index: \"13-13\",\n        path: \"/userLoginLog\"\n      }]\n    }, {\n      name: \"资金管理\",\n      index: \"11\",\n      path: \"/\",\n      icon: \"Coin\",\n      children: [{\n        name: \"普通充值\",\n        index: \"11-2\",\n        path: \"/chargeList\"\n      }, {\n        name: \"usdt充值\",\n        index: \"11-15\",\n        path: \"/UsdtChargeList\"\n      }, {\n        name: \"提款列表\",\n        index: \"11-3\",\n        path: \"/withdrawList\"\n      }, {\n        name: \"转账列表\",\n        index: \"11-42\",\n        path: \"/transferLogLists\"\n      }, {\n        name: \"流水记录\",\n        index: \"11-4\",\n        path: \"/flowList\"\n      }, {\n        name: \"余额宝记录\",\n        index: \"11-41\",\n        path: \"/yuebaoLogLists\"\n      }]\n    }, {\n      name: \"抽奖管理\",\n      index: \"12\",\n      path: \"/\",\n      icon: \"GoldMedal\",\n      children: [\n      // {\n      //   name: \"集字列表\",\n      //   index: \"12-8\",\n      //   path: \"/collectWords\",\n      // },\n      // {\n      //   name: \"集字记录\",\n      //   index: \"12-10\",\n      //   path: \"/wordsLogLists\",\n      // },\n      // {\n      //   name: \"大奖设置\",\n      //   index: \"12-9\",\n      //   path: \"/bigRaffleLists\",\n      // },\n      // {\n      //   name: \"大奖记录\",\n      //   index: \"12-11\",\n      //   path: \"/bigRaffleLogLists\",\n      // },\n      {\n        name: \"幸运抽奖\",\n        index: \"12-3\",\n        path: \"/lottory\"\n      }, {\n        name: \"抽奖记录\",\n        index: \"12-4\",\n        path: \"/lottoryDetails\"\n      }]\n    }, {\n      name: \"团队管理\",\n      index: \"13\",\n      path: \"/\",\n      icon: \"Baseball\",\n      children: [{\n        name: \"用户等级设置\",\n        index: \"13-5\",\n        path: \"/memberLevel\"\n      }, {\n        name: \"用户升级记录\",\n        index: \"13-12\",\n        path: \"/levelLogLists\"\n      }, {\n        name: \"团队等级设置\",\n        index: \"13-7\",\n        path: \"/teamLevel\"\n      }, {\n        name: \"团队升级记录\",\n        index: \"13-21\",\n        path: \"/teamLevelLogLists\"\n      }\n      // {\n      //   name: \"团队奖励记录\",\n      //   index: \"13-22\",\n      //   path: \"/teamRewardLogLists\",\n      // },\n      ]\n    }, {\n      name: \"签到管理\",\n      index: \"14\",\n      path: \"/\",\n      icon: \"Present\",\n      children: [{\n        name: \"签到记录\",\n        index: \"14-4\",\n        path: \"/signInLists\"\n      }, {\n        name: \"签到奖品领取记录\",\n        index: \"14-6\",\n        path: \"/giftsList\"\n      }, {\n        name: \"签到奖品设置\",\n        index: \"14-11\",\n        path: \"/signRewardTypes\"\n      }]\n    }, {\n      name: \"拼团管理\",\n      index: \"15\",\n      path: \"/\",\n      icon: \"Guide\",\n      children: [{\n        name: \"拼团类型\",\n        index: \"15-12\",\n        path: \"/pintuanType\"\n      }, {\n        name: \"拼团列表\",\n        index: \"15-13\",\n        path: \"/pintuanList\"\n      }, {\n        name: \"拼团记录\",\n        index: \"15-14\",\n        path: \"/pintuanRecords\"\n      }]\n    }, {\n      name: \"项目管理\",\n      index: \"2\",\n      path: \"/\",\n      icon: \"WindPower\",\n      children: [{\n        name: \"项目分类\",\n        index: \"2-1\",\n        path: \"/projectType\"\n      }, {\n        name: \"项目列表\",\n        index: \"2-2\",\n        path: \"/projectList\"\n      }, {\n        name: \"购买记录\",\n        index: \"2-6\",\n        path: \"/buyRecordList\"\n      }, {\n        name: \"结算记录\",\n        index: \"2-7\",\n        path: \"/projectLogList\"\n      }]\n    }, {\n      name: \"支付模块\",\n      index: \"8\",\n      path: \"/\",\n      icon: \"Wallet\",\n      children: [{\n        name: \"支付类型\",\n        index: \"8-3\",\n        path: \"/rechargeType\"\n      }, {\n        name: \"支付上游\",\n        index: \"8-5\",\n        path: \"/paymentUpper\"\n      }, {\n        name: \"支付账号\",\n        index: \"8-7\",\n        path: \"/paymentAccount\"\n      }, {\n        name: \"支付渠道\",\n        index: \"8-6\",\n        path: \"/paymentChannel\"\n      }, {\n        name: \"银行列表\",\n        index: \"8-8\",\n        path: \"/bankList\"\n      }]\n    }, {\n      name: \"商品管理\",\n      index: \"3\",\n      path: \"/\",\n      icon: \"Handbag\",\n      children: [{\n        name: \"商品分类\",\n        index: \"3-1\",\n        path: \"/goodsType\"\n      }, {\n        name: \"商品列表\",\n        index: \"3-2\",\n        path: \"/goodsList\"\n      }, {\n        name: \"购买记录\",\n        index: \"3-3\",\n        path: \"/goodsRecords\"\n      }]\n    }, {\n      name: \"运营管理\",\n      index: \"4\",\n      path: \"/\",\n      icon: \"Pointer\",\n      children: [{\n        name: \"优惠券列表\",\n        index: \"23-6\",\n        path: \"/discountList\"\n      }, {\n        name: \"轮播图\",\n        index: \"4-15\",\n        path: \"/bannerList\"\n      }, {\n        name: \"站内信\",\n        index: \"41-15\",\n        path: \"/messageLists\"\n      }, {\n        name: \"文章类型\",\n        index: \"4-10\",\n        path: \"/newsTypeList\"\n      }, {\n        name: \"文章资讯\",\n        index: \"4-1\",\n        path: \"/newsList\"\n      },\n      // {\n      //     name: '站内信',\n      //     index: '4-5',\n      //     path: '/letters'\n      // },\n\n      {\n        name: \"短信记录\",\n        index: \"4-8\",\n        path: \"/smsCodeLists\"\n      }, {\n        name: \"用户反馈\",\n        index: \"4-9\",\n        path: \"/userFeedback\"\n      }, {\n        name: \"问答列表\",\n        index: \"4-12\",\n        path: \"/questionList\"\n      }]\n    }, {\n      name: \"系统设置\",\n      index: \"5\",\n      path: \"/\",\n      icon: \"Setting\",\n      children: [{\n        name: \"参数设置\",\n        index: \"5-1\",\n        path: \"/paramsSetting\"\n      },\n      // {\n      //     name: '网站介绍',\n      //     index: '5-2',\n      //     path: '/siteDesc'\n      // },\n\n      {\n        name: \"谷歌密钥\",\n        index: \"5-4\",\n        path: \"/googleKeys\"\n      }]\n    },\n    // {\n    //     name: '视频管理',\n    //     index: '6',\n    //     path: '/',\n    //     icon: 'Film',\n    //     children: [\n    // {\n    //     name: '视频分类',\n    //     index: '6-1',\n    //     path: '/videoType'\n    // },{\n    //     name: '视频管理',\n    //     index: '6-2',\n    //     path: '/videoList'\n    // },\n    //         {\n    //             name: '宣传视频',\n    //             index: '6-3',\n    //             path: '/xuanchuanVideo'\n    //         }\n    //     ]\n    // },\n    {\n      name: \"后台管理\",\n      index: \"7\",\n      path: \"/\",\n      icon: \"Guide\",\n      children: [{\n        name: \"后台用户\",\n        index: \"7-1\",\n        path: \"/backendUser\"\n      }, {\n        name: \"登录日志\",\n        index: \"7-2\",\n        path: \"/loginLog\"\n      }]\n    }]);\n    const router = useRouter();\n    const selectItem = (item, child) => {\n      // const selectItem\n      let arr = [{\n        name: \"首页\",\n        index: \"1\",\n        path: \"/home\"\n      }, item, child];\n      store.commit(\"updateBreakCum\", arr);\n      router.push(child.path);\n    };\n    const activeKey = ref(\"1\");\n    const route = useRoute();\n    watch(() => route.path, (newPath, oldPath) => {\n      menuList.value.map(item => {\n        let res = item.children.filter(_t => _t.path === newPath);\n        if (res.length > 0) {\n          activeKey.value = res[0].index;\n        }\n      });\n    }, {\n      immediate: true\n    });\n    const gotohome = () => {\n      let arr = [{\n        name: \"首页\",\n        index: \"1\",\n        path: \"/home\"\n      }];\n      store.commit(\"updateBreakCum\", arr);\n      router.push(\"/home\");\n    };\n    const handleOpen = (key, keyPath) => {};\n    const handleClose = (key, keyPath) => {};\n    const props = __props;\n    const __returned__ = {\n      menuList,\n      router,\n      selectItem,\n      activeKey,\n      route,\n      gotohome,\n      handleOpen,\n      handleClose,\n      props,\n      get store() {\n        return store;\n      },\n      ref,\n      watch,\n      get useRoute() {\n        return useRoute;\n      },\n      get useRouter() {\n        return useRouter;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["store", "ref", "watch", "useRoute", "useRouter", "menuList", "name", "index", "path", "icon", "children", "router", "selectItem", "item", "child", "arr", "commit", "push", "active<PERSON><PERSON>", "route", "newPath", "old<PERSON><PERSON>", "value", "map", "res", "filter", "_t", "length", "immediate", "gotohome", "handleOpen", "key", "keyP<PERSON>", "handleClose", "props", "__props"], "sources": ["D:/WorkSpace/facai7/facai7_admin/src/layout/leftMenu.vue"], "sourcesContent": ["<template>\r\n  <el-menu\r\n    :default-active=\"activeKey\"\r\n    class=\"el-menu-vertical-demo layout-menu\"\r\n    :collapse=\"props.isCollapse\"\r\n    @open=\"handleOpen\"\r\n    @close=\"handleClose\"\r\n    :unique-opened=\"true\"\r\n  >\r\n    <el-menu-item index=\"1\" @click=\"gotohome\">\r\n      <el-icon><component is=\"menu\"></component></el-icon>\r\n      <template #title>首页</template>\r\n    </el-menu-item>\r\n    <el-sub-menu\r\n      :index=\"item.index\"\r\n      :key=\"item.index\"\r\n      v-for=\"(item, index) in menuList\"\r\n    >\r\n      <template #title>\r\n        <el-icon>\r\n          <component :is=\"item.icon\"></component>\r\n        </el-icon>\r\n        <span>{{ item.name }}</span>\r\n      </template>\r\n      <el-menu-item-group>\r\n        <el-menu-item\r\n          @click=\"selectItem(item, child)\"\r\n          :index=\"child.index\"\r\n          :key=\"child.path\"\r\n          v-for=\"(child, cIndex) in item.children\"\r\n          ><el-icon> <ArrowRight /> </el-icon>{{ child.name }}</el-menu-item\r\n        >\r\n      </el-menu-item-group>\r\n    </el-sub-menu>\r\n  </el-menu>\r\n</template>\r\n\r\n<script setup>\r\nimport store from \"@/store\";\r\nimport { ref, watch } from \"vue\";\r\nimport { useRoute, useRouter } from \"vue-router/dist/vue-router\";\r\nconst menuList = ref([\r\n  {\r\n    name: \"用户管理\",\r\n    index: \"1\",\r\n    path: \"/\",\r\n    icon: \"User\",\r\n    children: [\r\n      {\r\n        name: \"用户列表\",\r\n        index: \"1-1\",\r\n        path: \"/userList\",\r\n      },\r\n      {\r\n        name: \"团队列表\",\r\n        index: \"1-41\",\r\n        path: \"/teamList\",\r\n      },\r\n\r\n      {\r\n        name: \"钱包管理\",\r\n        index: \"1-8\",\r\n        path: \"/userWallet\",\r\n      },\r\n      {\r\n        name: \"地址管理\",\r\n        index: \"1-9\",\r\n        path: \"/userAddress\",\r\n      },\r\n      {\r\n        name: \"实名审核\",\r\n        index: \"1-11\",\r\n        path: \"/realnameList\",\r\n      },\r\n      {\r\n        name: \"用户登录记录\",\r\n        index: \"13-13\",\r\n        path: \"/userLoginLog\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"资金管理\",\r\n    index: \"11\",\r\n    path: \"/\",\r\n    icon: \"Coin\",\r\n    children: [\r\n      {\r\n        name: \"普通充值\",\r\n        index: \"11-2\",\r\n        path: \"/chargeList\",\r\n      },\r\n      {\r\n        name: \"usdt充值\",\r\n        index: \"11-15\",\r\n        path: \"/UsdtChargeList\",\r\n      },\r\n      {\r\n        name: \"提款列表\",\r\n        index: \"11-3\",\r\n        path: \"/withdrawList\",\r\n      },\r\n      {\r\n        name: \"转账列表\",\r\n        index: \"11-42\",\r\n        path: \"/transferLogLists\",\r\n      },\r\n      {\r\n        name: \"流水记录\",\r\n        index: \"11-4\",\r\n        path: \"/flowList\",\r\n      },\r\n      {\r\n        name: \"余额宝记录\",\r\n        index: \"11-41\",\r\n        path: \"/yuebaoLogLists\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"抽奖管理\",\r\n    index: \"12\",\r\n    path: \"/\",\r\n    icon: \"GoldMedal\",\r\n    children: [\r\n      // {\r\n      //   name: \"集字列表\",\r\n      //   index: \"12-8\",\r\n      //   path: \"/collectWords\",\r\n      // },\r\n      // {\r\n      //   name: \"集字记录\",\r\n      //   index: \"12-10\",\r\n      //   path: \"/wordsLogLists\",\r\n      // },\r\n      // {\r\n      //   name: \"大奖设置\",\r\n      //   index: \"12-9\",\r\n      //   path: \"/bigRaffleLists\",\r\n      // },\r\n      // {\r\n      //   name: \"大奖记录\",\r\n      //   index: \"12-11\",\r\n      //   path: \"/bigRaffleLogLists\",\r\n      // },\r\n      {\r\n        name: \"幸运抽奖\",\r\n        index: \"12-3\",\r\n        path: \"/lottory\",\r\n      },\r\n      {\r\n        name: \"抽奖记录\",\r\n        index: \"12-4\",\r\n        path: \"/lottoryDetails\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"团队管理\",\r\n    index: \"13\",\r\n    path: \"/\",\r\n    icon: \"Baseball\",\r\n    children: [\r\n      {\r\n        name: \"用户等级设置\",\r\n        index: \"13-5\",\r\n        path: \"/memberLevel\",\r\n      },\r\n      {\r\n        name: \"用户升级记录\",\r\n        index: \"13-12\",\r\n        path: \"/levelLogLists\",\r\n      },\r\n      {\r\n        name: \"团队等级设置\",\r\n        index: \"13-7\",\r\n        path: \"/teamLevel\",\r\n      },\r\n      {\r\n        name: \"团队升级记录\",\r\n        index: \"13-21\",\r\n        path: \"/teamLevelLogLists\",\r\n      },\r\n      // {\r\n      //   name: \"团队奖励记录\",\r\n      //   index: \"13-22\",\r\n      //   path: \"/teamRewardLogLists\",\r\n      // },\r\n    ],\r\n  },\r\n  {\r\n    name: \"签到管理\",\r\n    index: \"14\",\r\n    path: \"/\",\r\n    icon: \"Present\",\r\n    children: [\r\n      {\r\n        name: \"签到记录\",\r\n        index: \"14-4\",\r\n        path: \"/signInLists\",\r\n      },\r\n      {\r\n        name: \"签到奖品领取记录\",\r\n        index: \"14-6\",\r\n        path: \"/giftsList\",\r\n      },\r\n      {\r\n        name: \"签到奖品设置\",\r\n        index: \"14-11\",\r\n        path: \"/signRewardTypes\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"拼团管理\",\r\n    index: \"15\",\r\n    path: \"/\",\r\n    icon: \"Guide\",\r\n    children: [\r\n      {\r\n        name: \"拼团类型\",\r\n        index: \"15-12\",\r\n        path: \"/pintuanType\",\r\n      },\r\n      {\r\n        name: \"拼团列表\",\r\n        index: \"15-13\",\r\n        path: \"/pintuanList\",\r\n      },\r\n      {\r\n        name: \"拼团记录\",\r\n        index: \"15-14\",\r\n        path: \"/pintuanRecords\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"项目管理\",\r\n    index: \"2\",\r\n    path: \"/\",\r\n    icon: \"WindPower\",\r\n    children: [\r\n      {\r\n        name: \"项目分类\",\r\n        index: \"2-1\",\r\n        path: \"/projectType\",\r\n      },\r\n      {\r\n        name: \"项目列表\",\r\n        index: \"2-2\",\r\n        path: \"/projectList\",\r\n      },\r\n      {\r\n        name: \"购买记录\",\r\n        index: \"2-6\",\r\n        path: \"/buyRecordList\",\r\n      },\r\n      {\r\n        name: \"结算记录\",\r\n        index: \"2-7\",\r\n        path: \"/projectLogList\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"支付模块\",\r\n    index: \"8\",\r\n    path: \"/\",\r\n    icon: \"Wallet\",\r\n    children: [\r\n      {\r\n        name: \"支付类型\",\r\n        index: \"8-3\",\r\n        path: \"/rechargeType\",\r\n      },\r\n      {\r\n        name: \"支付上游\",\r\n        index: \"8-5\",\r\n        path: \"/paymentUpper\",\r\n      },\r\n      {\r\n        name: \"支付账号\",\r\n        index: \"8-7\",\r\n        path: \"/paymentAccount\",\r\n      },\r\n      {\r\n        name: \"支付渠道\",\r\n        index: \"8-6\",\r\n        path: \"/paymentChannel\",\r\n      },\r\n      {\r\n        name: \"银行列表\",\r\n        index: \"8-8\",\r\n        path: \"/bankList\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"商品管理\",\r\n    index: \"3\",\r\n    path: \"/\",\r\n    icon: \"Handbag\",\r\n    children: [\r\n      {\r\n        name: \"商品分类\",\r\n        index: \"3-1\",\r\n        path: \"/goodsType\",\r\n      },\r\n      {\r\n        name: \"商品列表\",\r\n        index: \"3-2\",\r\n        path: \"/goodsList\",\r\n      },\r\n\r\n      {\r\n        name: \"购买记录\",\r\n        index: \"3-3\",\r\n        path: \"/goodsRecords\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"运营管理\",\r\n    index: \"4\",\r\n    path: \"/\",\r\n    icon: \"Pointer\",\r\n    children: [\r\n      {\r\n        name: \"优惠券列表\",\r\n        index: \"23-6\",\r\n        path: \"/discountList\",\r\n      },\r\n      {\r\n        name: \"轮播图\",\r\n        index: \"4-15\",\r\n        path: \"/bannerList\",\r\n      },\r\n      {\r\n        name: \"站内信\",\r\n        index: \"41-15\",\r\n        path: \"/messageLists\",\r\n      },\r\n      {\r\n        name: \"文章类型\",\r\n        index: \"4-10\",\r\n        path: \"/newsTypeList\",\r\n      },\r\n      {\r\n        name: \"文章资讯\",\r\n        index: \"4-1\",\r\n        path: \"/newsList\",\r\n      },\r\n\r\n      // {\r\n      //     name: '站内信',\r\n      //     index: '4-5',\r\n      //     path: '/letters'\r\n      // },\r\n\r\n      {\r\n        name: \"短信记录\",\r\n        index: \"4-8\",\r\n        path: \"/smsCodeLists\",\r\n      },\r\n      {\r\n        name: \"用户反馈\",\r\n        index: \"4-9\",\r\n        path: \"/userFeedback\",\r\n      },\r\n      {\r\n        name: \"问答列表\",\r\n        index: \"4-12\",\r\n        path: \"/questionList\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"系统设置\",\r\n    index: \"5\",\r\n    path: \"/\",\r\n    icon: \"Setting\",\r\n    children: [\r\n      {\r\n        name: \"参数设置\",\r\n        index: \"5-1\",\r\n        path: \"/paramsSetting\",\r\n      },\r\n      // {\r\n      //     name: '网站介绍',\r\n      //     index: '5-2',\r\n      //     path: '/siteDesc'\r\n      // },\r\n\r\n      {\r\n        name: \"谷歌密钥\",\r\n        index: \"5-4\",\r\n        path: \"/googleKeys\",\r\n      },\r\n    ],\r\n  },\r\n  // {\r\n  //     name: '视频管理',\r\n  //     index: '6',\r\n  //     path: '/',\r\n  //     icon: 'Film',\r\n  //     children: [\r\n  // {\r\n  //     name: '视频分类',\r\n  //     index: '6-1',\r\n  //     path: '/videoType'\r\n  // },{\r\n  //     name: '视频管理',\r\n  //     index: '6-2',\r\n  //     path: '/videoList'\r\n  // },\r\n  //         {\r\n  //             name: '宣传视频',\r\n  //             index: '6-3',\r\n  //             path: '/xuanchuanVideo'\r\n  //         }\r\n  //     ]\r\n  // },\r\n  {\r\n    name: \"后台管理\",\r\n    index: \"7\",\r\n    path: \"/\",\r\n    icon: \"Guide\",\r\n    children: [\r\n      {\r\n        name: \"后台用户\",\r\n        index: \"7-1\",\r\n        path: \"/backendUser\",\r\n      },\r\n      {\r\n        name: \"登录日志\",\r\n        index: \"7-2\",\r\n        path: \"/loginLog\",\r\n      },\r\n    ],\r\n  },\r\n]);\r\n\r\nconst router = useRouter();\r\nconst selectItem = (item, child) => {\r\n  // const selectItem\r\n  let arr = [\r\n    {\r\n      name: \"首页\",\r\n      index: \"1\",\r\n      path: \"/home\",\r\n    },\r\n    item,\r\n    child,\r\n  ];\r\n  store.commit(\"updateBreakCum\", arr);\r\n  router.push(child.path);\r\n};\r\n\r\nconst activeKey = ref(\"1\");\r\nconst route = useRoute();\r\n\r\nwatch(\r\n  () => route.path,\r\n  (newPath, oldPath) => {\r\n    menuList.value.map((item) => {\r\n      let res = item.children.filter((_t) => _t.path === newPath);\r\n      if (res.length > 0) {\r\n        activeKey.value = res[0].index;\r\n      }\r\n    });\r\n  },\r\n  { immediate: true }\r\n);\r\n\r\nconst gotohome = () => {\r\n  let arr = [\r\n    {\r\n      name: \"首页\",\r\n      index: \"1\",\r\n      path: \"/home\",\r\n    },\r\n  ];\r\n  store.commit(\"updateBreakCum\", arr);\r\n  router.push(\"/home\");\r\n};\r\n\r\nconst handleOpen = (key, keyPath) => {};\r\nconst handleClose = (key, keyPath) => {};\r\n\r\nconst props = defineProps([\"isCollapse\"]);\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.layout-menu {\r\n  // width: 220px;\r\n  height: 100%;\r\n  background-color: #eee;\r\n  transition: all 0.25s;\r\n  overflow-y: auto;\r\n}\r\n</style>\r\n"], "mappings": ";;;;AAsCA,OAAOA,KAAK,MAAM,SAAS;AAC3B,SAASC,GAAG,EAAEC,KAAK,QAAQ,KAAK;AAChC,SAASC,QAAQ,EAAEC,SAAS,QAAQ,4BAA4B;;;;;;;;IAChE,MAAMC,QAAQ,GAAGJ,GAAG,CAAC,CACnB;MACEK,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,GAAG;MACVC,IAAI,EAAE,GAAG;MACTC,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE,CACR;QACEJ,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,KAAK;QACZC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE;MACR,CAAC,EAED;QACEF,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,KAAK;QACZC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,KAAK;QACZC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE;MACR,CAAC;IAEL,CAAC,EACD;MACEF,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE,GAAG;MACTC,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE,CACR;QACEJ,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE;MACR,CAAC;IAEL,CAAC,EACD;MACEF,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE,GAAG;MACTC,IAAI,EAAE,WAAW;MACjBC,QAAQ,EAAE;MACR;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACEJ,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE;MACR,CAAC;IAEL,CAAC,EACD;MACEF,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE,GAAG;MACTC,IAAI,EAAE,UAAU;MAChBC,QAAQ,EAAE,CACR;QACEJ,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE;MACR;MACA;MACA;MACA;MACA;MACA;MAAA;IAEJ,CAAC,EACD;MACEF,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE,GAAG;MACTC,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE,CACR;QACEJ,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,IAAI,EAAE,UAAU;QAChBC,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE;MACR,CAAC;IAEL,CAAC,EACD;MACEF,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE,GAAG;MACTC,IAAI,EAAE,OAAO;MACbC,QAAQ,EAAE,CACR;QACEJ,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE;MACR,CAAC;IAEL,CAAC,EACD;MACEF,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,GAAG;MACVC,IAAI,EAAE,GAAG;MACTC,IAAI,EAAE,WAAW;MACjBC,QAAQ,EAAE,CACR;QACEJ,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,KAAK;QACZC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,KAAK;QACZC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,KAAK;QACZC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,KAAK;QACZC,IAAI,EAAE;MACR,CAAC;IAEL,CAAC,EACD;MACEF,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,GAAG;MACVC,IAAI,EAAE,GAAG;MACTC,IAAI,EAAE,QAAQ;MACdC,QAAQ,EAAE,CACR;QACEJ,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,KAAK;QACZC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,KAAK;QACZC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,KAAK;QACZC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,KAAK;QACZC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,KAAK;QACZC,IAAI,EAAE;MACR,CAAC;IAEL,CAAC,EACD;MACEF,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,GAAG;MACVC,IAAI,EAAE,GAAG;MACTC,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE,CACR;QACEJ,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,KAAK;QACZC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,KAAK;QACZC,IAAI,EAAE;MACR,CAAC,EAED;QACEF,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,KAAK;QACZC,IAAI,EAAE;MACR,CAAC;IAEL,CAAC,EACD;MACEF,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,GAAG;MACVC,IAAI,EAAE,GAAG;MACTC,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE,CACR;QACEJ,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,IAAI,EAAE,KAAK;QACXC,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,IAAI,EAAE,KAAK;QACXC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,KAAK;QACZC,IAAI,EAAE;MACR,CAAC;MAED;MACA;MACA;MACA;MACA;;MAEA;QACEF,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,KAAK;QACZC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,KAAK;QACZC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE;MACR,CAAC;IAEL,CAAC,EACD;MACEF,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,GAAG;MACVC,IAAI,EAAE,GAAG;MACTC,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE,CACR;QACEJ,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,KAAK;QACZC,IAAI,EAAE;MACR,CAAC;MACD;MACA;MACA;MACA;MACA;;MAEA;QACEF,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,KAAK;QACZC,IAAI,EAAE;MACR,CAAC;IAEL,CAAC;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACEF,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,GAAG;MACVC,IAAI,EAAE,GAAG;MACTC,IAAI,EAAE,OAAO;MACbC,QAAQ,EAAE,CACR;QACEJ,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,KAAK;QACZC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,KAAK;QACZC,IAAI,EAAE;MACR,CAAC;IAEL,CAAC,CACF,CAAC;IAEF,MAAMG,MAAM,GAAGP,SAAS,CAAC,CAAC;IAC1B,MAAMQ,UAAU,GAAGA,CAACC,IAAI,EAAEC,KAAK,KAAK;MAClC;MACA,IAAIC,GAAG,GAAG,CACR;QACET,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,GAAG;QACVC,IAAI,EAAE;MACR,CAAC,EACDK,IAAI,EACJC,KAAK,CACN;MACDd,KAAK,CAACgB,MAAM,CAAC,gBAAgB,EAAED,GAAG,CAAC;MACnCJ,MAAM,CAACM,IAAI,CAACH,KAAK,CAACN,IAAI,CAAC;IACzB,CAAC;IAED,MAAMU,SAAS,GAAGjB,GAAG,CAAC,GAAG,CAAC;IAC1B,MAAMkB,KAAK,GAAGhB,QAAQ,CAAC,CAAC;IAExBD,KAAK,CACH,MAAMiB,KAAK,CAACX,IAAI,EAChB,CAACY,OAAO,EAAEC,OAAO,KAAK;MACpBhB,QAAQ,CAACiB,KAAK,CAACC,GAAG,CAAEV,IAAI,IAAK;QAC3B,IAAIW,GAAG,GAAGX,IAAI,CAACH,QAAQ,CAACe,MAAM,CAAEC,EAAE,IAAKA,EAAE,CAAClB,IAAI,KAAKY,OAAO,CAAC;QAC3D,IAAII,GAAG,CAACG,MAAM,GAAG,CAAC,EAAE;UAClBT,SAAS,CAACI,KAAK,GAAGE,GAAG,CAAC,CAAC,CAAC,CAACjB,KAAK;QAChC;MACF,CAAC,CAAC;IACJ,CAAC,EACD;MAAEqB,SAAS,EAAE;IAAK,CACpB,CAAC;IAED,MAAMC,QAAQ,GAAGA,CAAA,KAAM;MACrB,IAAId,GAAG,GAAG,CACR;QACET,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,GAAG;QACVC,IAAI,EAAE;MACR,CAAC,CACF;MACDR,KAAK,CAACgB,MAAM,CAAC,gBAAgB,EAAED,GAAG,CAAC;MACnCJ,MAAM,CAACM,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED,MAAMa,UAAU,GAAGA,CAACC,GAAG,EAAEC,OAAO,KAAK,CAAC,CAAC;IACvC,MAAMC,WAAW,GAAGA,CAACF,GAAG,EAAEC,OAAO,KAAK,CAAC,CAAC;IAExC,MAAME,KAAK,GAAGC,OAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}