{"ast": null, "code": "import { nextTick, onMounted, ref } from 'vue';\nimport { sfzStatusEnums } from '@/config/enums';\nexport default {\n  __name: 'editPop',\n  props: ['item'],\n  setup(__props, {\n    expose: __expose\n  }) {\n    const form = ref({\n      sfz_name: '',\n      sfz_number: '',\n      status: '',\n      username: ''\n    });\n    const props = __props;\n    onMounted(() => {\n      nextTick(() => {\n        form.value = Object.assign(form, props.item);\n      });\n    });\n    __expose({\n      form\n    });\n    const __returned__ = {\n      form,\n      props,\n      nextTick,\n      onMounted,\n      ref,\n      get sfzStatusEnums() {\n        return sfzStatusEnums;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["nextTick", "onMounted", "ref", "sfzStatusEnums", "form", "sfz_name", "sfz_number", "status", "username", "props", "__props", "value", "Object", "assign", "item", "__expose"], "sources": ["D:/WorkSpace/facai7/facai7_admin/src/views/userManage/components/realnameList/editPop.vue"], "sourcesContent": ["<template>\r\n    <el-form label-width=\"100px\" :inline=\"true\" :model=\"form\" class=\"demo-form-inline\">\r\n        <el-form-item label=\"身份证名称\" required >\r\n            <el-input v-model=\"form.sfz_name\" clearable  />\r\n        </el-form-item>\r\n        <el-form-item label=\"身份证号码\" required >\r\n            <el-input v-model=\"form.sfz_number\" clearable />\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"实名状态\" required>\r\n            <el-select v-model=\"form.status\" placeholder=\"\" clearable>\r\n                <el-option v-for=\"item in sfzStatusEnums\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"用户名称\" required >\r\n            <el-input v-model=\"form.username\" clearable />\r\n        </el-form-item>\r\n    </el-form>\r\n</template>\r\n\r\n<script setup>\r\nimport { nextTick, onMounted, ref } from 'vue'\r\nimport {sfzStatusEnums} from '@/config/enums'\r\n\r\nconst form = ref({\r\n    sfz_name: '',\r\n    sfz_number: '',\r\n    status: '',\r\n    username: '',\r\n})\r\nconst props = defineProps(['item'])\r\n\r\nonMounted(() => {\r\n    nextTick(()=> {\r\n        form.value = Object.assign(form, props.item)\r\n    })\r\n})\r\n\r\ndefineExpose({form})\r\n\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.demo-form-inline {\r\n    justify-content: flex-start;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n}\r\n.demo-form-inline .el-input {\r\n    --el-input-width: 220px;\r\n}\r\n\r\n.demo-form-inline .el-select {\r\n    --el-select-width: 220px;\r\n}\r\n/deep/ .el-radio-group {\r\n    width: 220px;\r\n}\r\n.form-title {\r\n text-align: left;\r\n padding-left: 30px;\r\n margin: 20px auto 10px;\r\n height: 44px;\r\n background-color: #f2f2f2;\r\n border-radius: 5px;\r\n line-height: 44px;\r\n}\r\n/deep/  .el-form-item {\r\n    align-items: flex-start;\r\n}\r\n</style>"], "mappings": "AAqBA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,GAAG,QAAQ,KAAK;AAC9C,SAAQC,cAAc,QAAO,gBAAgB;;;;;;;IAE7C,MAAMC,IAAI,GAAGF,GAAG,CAAC;MACbG,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,EAAE;MACdC,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE;IACd,CAAC,CAAC;IACF,MAAMC,KAAK,GAAGC,OAAqB;IAEnCT,SAAS,CAAC,MAAM;MACZD,QAAQ,CAAC,MAAK;QACVI,IAAI,CAACO,KAAK,GAAGC,MAAM,CAACC,MAAM,CAACT,IAAI,EAAEK,KAAK,CAACK,IAAI,CAAC;MAChD,CAAC,CAAC;IACN,CAAC,CAAC;IAEFC,QAAY,CAAC;MAACX;IAAI,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}