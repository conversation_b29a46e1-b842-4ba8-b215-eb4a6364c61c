{"ast": null, "code": "import { nextTick, onMounted, ref } from 'vue';\nexport default {\n  __name: 'editPop',\n  props: ['item'],\n  setup(__props, {\n    expose: __expose\n  }) {\n    const form = ref({\n      desc: '',\n      key: '',\n      val: ''\n    });\n    const props = __props;\n    onMounted(() => {\n      nextTick(() => {\n        form.value = Object.assign(form, props.item);\n      });\n    });\n    __expose({\n      form\n    });\n    const __returned__ = {\n      form,\n      props,\n      nextTick,\n      onMounted,\n      ref\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["nextTick", "onMounted", "ref", "form", "desc", "key", "val", "props", "__props", "value", "Object", "assign", "item", "__expose"], "sources": ["D:/WorkSpace/facai7/facai7_admin/src/views/sysSetting/components/paramsSetting/editPop.vue"], "sourcesContent": ["<template>\r\n    <el-form label-width=\"80px\" :inline=\"true\" :model=\"form\" class=\"demo-form-inline\">\r\n        <el-form-item label=\"名称\" required >\r\n            <el-input v-model=\"form.desc\"   />\r\n        </el-form-item>\r\n        <el-form-item label=\"key\" required >\r\n            <el-input v-model=\"form.key\"   />\r\n        </el-form-item>\r\n        <el-form-item label=\"值\" required >\r\n            <el-input v-model=\"form.val\"   />\r\n        </el-form-item>\r\n    </el-form>\r\n</template>\r\n\r\n<script setup>\r\nimport { nextTick, onMounted, ref } from 'vue'\r\n\r\nconst form = ref({\r\n    desc: '',\r\n    key: '',\r\n    val: ''\r\n})\r\nconst props = defineProps(['item'])\r\n\r\nonMounted(() => {\r\n    nextTick(()=> {\r\n        form.value = Object.assign(form, props.item)\r\n    })\r\n})\r\n\r\ndefineExpose({form})\r\n\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.demo-form-inline {\r\n    justify-content: flex-start;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n}\r\n.demo-form-inline .el-input {\r\n    --el-input-width: 220px;\r\n}\r\n\r\n.demo-form-inline .el-select {\r\n    --el-select-width: 220px;\r\n}\r\n/deep/ .el-radio-group {\r\n    width: 220px;\r\n}\r\n.form-title {\r\n text-align: left;\r\n padding-left: 30px;\r\n margin: 20px auto 10px;\r\n height: 44px;\r\n background-color: #f2f2f2;\r\n border-radius: 5px;\r\n line-height: 44px;\r\n}\r\n</style>"], "mappings": "AAeA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,GAAG,QAAQ,KAAK;;;;;;;IAE9C,MAAMC,IAAI,GAAGD,GAAG,CAAC;MACbE,IAAI,EAAE,EAAE;MACRC,GAAG,EAAE,EAAE;MACPC,GAAG,EAAE;IACT,CAAC,CAAC;IACF,MAAMC,KAAK,GAAGC,OAAqB;IAEnCP,SAAS,CAAC,MAAM;MACZD,QAAQ,CAAC,MAAK;QACVG,IAAI,CAACM,KAAK,GAAGC,MAAM,CAACC,MAAM,CAACR,IAAI,EAAEI,KAAK,CAACK,IAAI,CAAC;MAChD,CAAC,CAAC;IACN,CAAC,CAAC;IAEFC,QAAY,CAAC;MAACV;IAAI,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}