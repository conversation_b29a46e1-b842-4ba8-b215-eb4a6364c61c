{"ast": null, "code": "import { nextTick, onMounted, ref } from 'vue';\nimport { rolesEnums } from '@/config/enums';\nexport default {\n  __name: 'editPop',\n  props: ['item'],\n  setup(__props, {\n    expose: __expose\n  }) {\n    const form = ref({\n      username: '',\n      role: '',\n      role_id: '',\n      invite_code: '',\n      password: '',\n      email: '',\n      remarks: ''\n    });\n    const props = __props;\n    onMounted(() => {\n      nextTick(() => {\n        form.value = Object.assign(form, props.item);\n      });\n    });\n    __expose({\n      form\n    });\n    const __returned__ = {\n      form,\n      props,\n      nextTick,\n      onMounted,\n      ref,\n      get rolesEnums() {\n        return rolesEnums;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["nextTick", "onMounted", "ref", "rolesEnums", "form", "username", "role", "role_id", "invite_code", "password", "email", "remarks", "props", "__props", "value", "Object", "assign", "item", "__expose"], "sources": ["D:/WorkSpace/facai7/facai7_admin/src/views/backendManage/components/backendUser/editPop.vue"], "sourcesContent": ["<template>\r\n    <el-form label-width=\"100px\" :inline=\"true\" :model=\"form\" class=\"demo-form-inline\">\r\n        <el-form-item label=\"用户名\" required >\r\n            <el-input v-model=\"form.username\" clearable  />\r\n        </el-form-item>\r\n        \r\n        <!-- <el-form-item label=\"类型\" required>\r\n            <el-select v-model=\"form.role_id\" placeholder=\"\" clearable>\r\n                <el-option v-for=\"item in rolesEnums\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n        </el-form-item> -->\r\n        <!-- <el-form-item label=\"邀请码\" required >\r\n            <el-input v-model=\"form.invite_code\" clearable />\r\n        </el-form-item> -->\r\n        <el-form-item label=\"密码\" required >\r\n            <el-input v-model=\"form.password\" clearable />\r\n        </el-form-item>\r\n        <!-- <el-form-item label=\"联系方式\" required >\r\n            <el-input v-model=\"form.email\" clearable />\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" required >\r\n            <el-input v-model=\"form.remarks\" clearable />\r\n        </el-form-item> -->\r\n    </el-form>\r\n</template>\r\n\r\n<script setup>\r\nimport { nextTick, onMounted, ref } from 'vue'\r\nimport {rolesEnums} from '@/config/enums'\r\n\r\nconst form = ref({\r\n    username: '',\r\n    role: '',\r\n    role_id: '',\r\n    invite_code: '',\r\n    password: '',\r\n    email: '',\r\n    remarks: ''\r\n})\r\nconst props = defineProps(['item'])\r\n\r\nonMounted(() => {\r\n    nextTick(()=> {\r\n        form.value = Object.assign(form, props.item)\r\n    })\r\n})\r\n\r\ndefineExpose({form})\r\n\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.demo-form-inline {\r\n    justify-content: flex-start;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n}\r\n.demo-form-inline .el-input {\r\n    --el-input-width: 220px;\r\n}\r\n\r\n.demo-form-inline .el-select {\r\n    --el-select-width: 220px;\r\n}\r\n/deep/ .el-radio-group {\r\n    width: 220px;\r\n}\r\n.form-title {\r\n text-align: left;\r\n padding-left: 30px;\r\n margin: 20px auto 10px;\r\n height: 44px;\r\n background-color: #f2f2f2;\r\n border-radius: 5px;\r\n line-height: 44px;\r\n}\r\n/deep/  .el-form-item {\r\n    align-items: flex-start;\r\n}\r\n</style>"], "mappings": "AA2BA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,GAAG,QAAQ,KAAK;AAC9C,SAAQC,UAAU,QAAO,gBAAgB;;;;;;;IAEzC,MAAMC,IAAI,GAAGF,GAAG,CAAC;MACbG,QAAQ,EAAE,EAAE;MACZC,IAAI,EAAE,EAAE;MACRC,OAAO,EAAE,EAAE;MACXC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE;IACb,CAAC,CAAC;IACF,MAAMC,KAAK,GAAGC,OAAqB;IAEnCZ,SAAS,CAAC,MAAM;MACZD,QAAQ,CAAC,MAAK;QACVI,IAAI,CAACU,KAAK,GAAGC,MAAM,CAACC,MAAM,CAACZ,IAAI,EAAEQ,KAAK,CAACK,IAAI,CAAC;MAChD,CAAC,CAAC;IACN,CAAC,CAAC;IAEFC,QAAY,CAAC;MAACd;IAAI,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}