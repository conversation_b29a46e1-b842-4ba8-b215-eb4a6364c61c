# Facai7本地开发环境配置指南

## 快速配置步骤

### 1. 环境准备

#### 1.1 必需软件
- **PHP**: 7.2.5+ (推荐7.4)
- **MySQL**: 5.7+
- **Redis**: 5.0+
- **Node.js**: 14.0+
- **Composer**: 2.0+
- **Git**: 2.0+

#### 1.2 开发工具
- **IDE**: VSCode/PhpStorm
- **API测试**: Postman/Apifox
- **数据库管理**: Navicat/phpMyAdmin

### 2. 项目克隆和初始化

```bash
# 克隆项目
git clone [项目地址] facai7
cd facai7

# 初始化各模块
cd facai7_api && composer install
cd ../facai7_admin && npm install
cd ../facai7_app && npm install
```

### 3. 数据库配置

#### 3.1 本地连接远程开发数据库
创建或修改 `facai7_api/.env.local`:

```ini
APP_DEBUG = true
APP_ENV = local

[APP]
DEFAULT_TIMEZONE = Asia/Shanghai
DEFAULT_LANG = zh-cn
GOOGLE_AUTH = false

[DATABASE]
TYPE = mysql
HOSTNAME = [远程开发服务器IP]  # 替换为实际IP
DATABASE = facai7
USERNAME = facai7
PASSWORD = 5aRTTSwAeyiRWxFt
HOSTPORT = 3306
CHARSET = utf8
DEBUG = true

[CACHE]
HOST = [远程Redis服务器IP]    # 替换为实际IP
PORT = 6379
#PASSWORD = 

[QUEUE]
QUEUE = facai9
HOST = [远程Redis服务器IP]    # 替换为实际IP
PORT = 6379
#PASSWORD = 
```

#### 3.2 测试数据库连接
```bash
cd facai7_api
php think migrate:status
```

### 4. 前端代理配置

#### 4.1 管理后台配置
修改 `facai7_admin/.env.development`:

```env
NODE_ENV=development

# 接口地址
VUE_APP_BASE_URL=/facai16

# API地址 - 指向本地API服务
VUE_APP_BASE_API_URL=http://localhost:8099/admin.php/

# 图片预览地址
VUE_APP_IMG_BASE_URL=http://localhost:8099
```

#### 4.2 移动端配置
修改 `facai7_app/vite.config.js`:

```javascript
export default defineConfig({
    // ... 其他配置
    server: {
        proxy: {
            "/api/v1": {
                target: "http://127.0.0.1:8099",  # 本地API服务
                changeOrigin: true
            }
        }
    }
});
```

### 5. 启动开发服务

#### 5.1 启动API服务
```bash
cd facai7_api
# 使用PHP内置服务器
php think run -p 8099

# 或使用Apache/Nginx配置虚拟主机
```

#### 5.2 启动管理后台
```bash
cd facai7_admin
npm run serve
# 访问: http://localhost:8888
```

#### 5.3 启动移动端
```bash
cd facai7_app
npm run dev:h5
# 访问: http://localhost:3000
```

### 6. 代码同步配置

#### 6.1 Git Hooks配置
在远程服务器配置自动部署：

```bash
# 在远程服务器项目目录
nano .git/hooks/post-merge

# 添加内容:
#!/bin/bash
cd /path/to/your/project
composer install --no-dev
php think migrate
php think cache:clear
```

#### 6.2 本地开发流程
```bash
# 1. 本地开发
git add .
git commit -m "功能描述"

# 2. 推送到远程仓库
git push origin main

# 3. 远程服务器自动拉取（通过定时任务或Webhook）
```

### 7. 调试配置

#### 7.1 API调试
```bash
# 查看日志
tail -f facai7_api/runtime/log/202X-XX-XX.log

# 清除缓存
php think cache:clear

# 运行队列
php think queue:work
```

#### 7.2 前端调试
- **Vue DevTools**: 浏览器插件
- **控制台调试**: F12开发者工具
- **网络请求**: 查看API调用情况

### 8. 测试配置

#### 8.1 单元测试
```bash
cd facai7_api
# 运行所有测试
php think unit

# 运行特定测试
php think unit --filter TestClassName
```

#### 8.2 API测试
使用Postman导入API文档进行测试：
- **Base URL**: http://localhost:8099
- **认证方式**: JWT Token
- **测试数据**: 使用开发环境数据

### 9. 常见问题解决

#### 9.1 数据库连接问题
```bash
# 检查网络连接
telnet [远程IP] 3306

# 检查防火墙设置
# 确保远程服务器MySQL允许外部连接
```

#### 9.2 跨域问题
确保API服务器配置了正确的CORS头：
```php
// 在API中间件中添加
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
```

#### 9.3 缓存问题
```bash
# 清除所有缓存
php think cache:clear

# 清除特定缓存
php think cache:clear --tag user
```

### 10. 性能优化建议

#### 10.1 开发环境优化
- 关闭不必要的日志记录
- 使用本地Redis缓存
- 配置合适的PHP内存限制

#### 10.2 代码同步优化
- 使用rsync进行增量同步
- 配置.gitignore忽略不必要文件
- 使用Git LFS处理大文件

## 完整的开发工作流示例

```bash
# 1. 每日开发开始
cd facai7
git pull origin main

# 2. 启动所有服务
cd facai7_api && php think run -p 8099 &
cd ../facai7_admin && npm run serve &
cd ../facai7_app && npm run dev:h5 &

# 3. 开发完成后
git add .
git commit -m "feat: 添加新功能"
git push origin main

# 4. 验证远程部署
curl http://[远程服务器]/api/health
```

这样配置后，您就可以实现：
- ✅ 本地代码修改
- ✅ 连接远程开发数据库
- ✅ 自动代码同步
- ✅ 实时调试和测试
