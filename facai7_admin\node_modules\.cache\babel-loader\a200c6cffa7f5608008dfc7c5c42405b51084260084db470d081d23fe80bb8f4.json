{"ast": null, "code": "//! moment.js locale configuration\n//! locale : French (Switzerland) [fr-ch]\n//! author : <PERSON><PERSON> : https://github.com/gaspard\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var frCh = moment.defineLocale('fr-ch', {\n    months: 'janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre'.split('_'),\n    monthsShort: 'janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi'.split('_'),\n    weekdaysShort: 'dim._lun._mar._mer._jeu._ven._sam.'.split('_'),\n    weekdaysMin: 'di_lu_ma_me_je_ve_sa'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Aujourd’hui à] LT',\n      nextDay: '[Demain à] LT',\n      nextWeek: 'dddd [à] LT',\n      lastDay: '[Hier à] LT',\n      lastWeek: 'dddd [dernier à] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'dans %s',\n      past: 'il y a %s',\n      s: 'quelques secondes',\n      ss: '%d secondes',\n      m: 'une minute',\n      mm: '%d minutes',\n      h: 'une heure',\n      hh: '%d heures',\n      d: 'un jour',\n      dd: '%d jours',\n      M: 'un mois',\n      MM: '%d mois',\n      y: 'un an',\n      yy: '%d ans'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(er|e)/,\n    ordinal: function (number, period) {\n      switch (period) {\n        // Words with masculine grammatical gender: mois, trimestre, jour\n        default:\n        case 'M':\n        case 'Q':\n        case 'D':\n        case 'DDD':\n        case 'd':\n          return number + (number === 1 ? 'er' : 'e');\n\n        // Words with feminine grammatical gender: semaine\n        case 'w':\n        case 'W':\n          return number + (number === 1 ? 're' : 'e');\n      }\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return frCh;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "frCh", "defineLocale", "months", "split", "monthsShort", "monthsParseExact", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "number", "period", "week", "dow", "doy"], "sources": ["D:/WorkSpace/facai7/facai7_admin/node_modules/moment/locale/fr-ch.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : French (Switzerland) [fr-ch]\n//! author : <PERSON><PERSON> : https://github.com/gaspard\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var frCh = moment.defineLocale('fr-ch', {\n        months: 'janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre'.split(\n            '_'\n        ),\n        monthsShort:\n            'janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.'.split(\n                '_'\n            ),\n        monthsParseExact: true,\n        weekdays: 'dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi'.split('_'),\n        weekdaysShort: 'dim._lun._mar._mer._jeu._ven._sam.'.split('_'),\n        weekdaysMin: 'di_lu_ma_me_je_ve_sa'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd D MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[Aujourd’hui à] LT',\n            nextDay: '[Demain à] LT',\n            nextWeek: 'dddd [à] LT',\n            lastDay: '[Hier à] LT',\n            lastWeek: 'dddd [dernier à] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'dans %s',\n            past: 'il y a %s',\n            s: 'quelques secondes',\n            ss: '%d secondes',\n            m: 'une minute',\n            mm: '%d minutes',\n            h: 'une heure',\n            hh: '%d heures',\n            d: 'un jour',\n            dd: '%d jours',\n            M: 'un mois',\n            MM: '%d mois',\n            y: 'un an',\n            yy: '%d ans',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}(er|e)/,\n        ordinal: function (number, period) {\n            switch (period) {\n                // Words with masculine grammatical gender: mois, trimestre, jour\n                default:\n                case 'M':\n                case 'Q':\n                case 'D':\n                case 'DDD':\n                case 'd':\n                    return number + (number === 1 ? 'er' : 'e');\n\n                // Words with feminine grammatical gender: semaine\n                case 'w':\n                case 'W':\n                    return number + (number === 1 ? 're' : 'e');\n            }\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return frCh;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,IAAI,GAAGD,MAAM,CAACE,YAAY,CAAC,OAAO,EAAE;IACpCC,MAAM,EAAE,sFAAsF,CAACC,KAAK,CAChG,GACJ,CAAC;IACDC,WAAW,EACP,gEAAgE,CAACD,KAAK,CAClE,GACJ,CAAC;IACLE,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE,qDAAqD,CAACH,KAAK,CAAC,GAAG,CAAC;IAC1EI,aAAa,EAAE,oCAAoC,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC9DK,WAAW,EAAE,sBAAsB,CAACL,KAAK,CAAC,GAAG,CAAC;IAC9CM,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,oBAAoB;MAC7BC,OAAO,EAAE,eAAe;MACxBC,QAAQ,EAAE,aAAa;MACvBC,OAAO,EAAE,aAAa;MACtBC,QAAQ,EAAE,qBAAqB;MAC/BC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,SAAS;MACjBC,IAAI,EAAE,WAAW;MACjBC,CAAC,EAAE,mBAAmB;MACtBC,EAAE,EAAE,aAAa;MACjBC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,OAAO;MACVC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,eAAe;IACvCC,OAAO,EAAE,SAAAA,CAAUC,MAAM,EAAEC,MAAM,EAAE;MAC/B,QAAQA,MAAM;QACV;QACA;QACA,KAAK,GAAG;QACR,KAAK,GAAG;QACR,KAAK,GAAG;QACR,KAAK,KAAK;QACV,KAAK,GAAG;UACJ,OAAOD,MAAM,IAAIA,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;;QAE/C;QACA,KAAK,GAAG;QACR,KAAK,GAAG;UACJ,OAAOA,MAAM,IAAIA,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;MACnD;IACJ,CAAC;IACDE,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAO7C,IAAI;AAEf,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}