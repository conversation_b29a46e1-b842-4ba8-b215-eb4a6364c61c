{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createCommentVNode as _createCommentVNode, openBlock as _openBlock, createBlock as _createBlock } from \"vue\";\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createBlock(_component_el_form, {\n    \"label-width\": \"100px\",\n    inline: true,\n    model: $setup.form,\n    class: \"demo-form-inline\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_form_item, {\n      label: \"用户名\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.username,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.form.username = $event),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" <el-form-item label=\\\"类型\\\" required>\\r\\n            <el-select v-model=\\\"form.role_id\\\" placeholder=\\\"\\\" clearable>\\r\\n                <el-option v-for=\\\"item in rolesEnums\\\" :label=\\\"item.label\\\" :value=\\\"item.value\\\" />\\r\\n            </el-select>\\r\\n        </el-form-item> \"), _createCommentVNode(\" <el-form-item label=\\\"邀请码\\\" required >\\r\\n            <el-input v-model=\\\"form.invite_code\\\" clearable />\\r\\n        </el-form-item> \"), _createVNode(_component_el_form_item, {\n      label: \"密码\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.password,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.form.password = $event),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" <el-form-item label=\\\"联系方式\\\" required >\\r\\n            <el-input v-model=\\\"form.email\\\" clearable />\\r\\n        </el-form-item>\\r\\n        <el-form-item label=\\\"备注\\\" required >\\r\\n            <el-input v-model=\\\"form.remarks\\\" clearable />\\r\\n        </el-form-item> \")]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\"]);\n}", "map": {"version": 3, "names": ["_createBlock", "_component_el_form", "inline", "model", "$setup", "form", "class", "_createVNode", "_component_el_form_item", "label", "required", "_component_el_input", "username", "$event", "clearable", "_createCommentVNode", "password"], "sources": ["D:\\WorkSpace\\facai7\\facai7_admin\\src\\views\\userManage\\components\\moneyChangeList\\editPop.vue"], "sourcesContent": ["<template>\r\n    <el-form label-width=\"100px\" :inline=\"true\" :model=\"form\" class=\"demo-form-inline\">\r\n        <el-form-item label=\"用户名\" required >\r\n            <el-input v-model=\"form.username\" clearable  />\r\n        </el-form-item>\r\n        \r\n        <!-- <el-form-item label=\"类型\" required>\r\n            <el-select v-model=\"form.role_id\" placeholder=\"\" clearable>\r\n                <el-option v-for=\"item in rolesEnums\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n        </el-form-item> -->\r\n        <!-- <el-form-item label=\"邀请码\" required >\r\n            <el-input v-model=\"form.invite_code\" clearable />\r\n        </el-form-item> -->\r\n        <el-form-item label=\"密码\" required >\r\n            <el-input v-model=\"form.password\" clearable />\r\n        </el-form-item>\r\n        <!-- <el-form-item label=\"联系方式\" required >\r\n            <el-input v-model=\"form.email\" clearable />\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" required >\r\n            <el-input v-model=\"form.remarks\" clearable />\r\n        </el-form-item> -->\r\n    </el-form>\r\n</template>\r\n\r\n<script setup>\r\nimport { nextTick, onMounted, ref } from 'vue'\r\nimport {rolesEnums} from '@/config/enums'\r\n\r\nconst form = ref({\r\n    username: '',\r\n    role: '',\r\n    role_id: '',\r\n    invite_code: '',\r\n    password: '',\r\n    email: '',\r\n    remarks: ''\r\n})\r\nconst props = defineProps(['item'])\r\n\r\nonMounted(() => {\r\n    nextTick(()=> {\r\n        form.value = Object.assign(form, props.item)\r\n    })\r\n})\r\n\r\ndefineExpose({form})\r\n\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.demo-form-inline {\r\n    justify-content: flex-start;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n}\r\n.demo-form-inline .el-input {\r\n    --el-input-width: 220px;\r\n}\r\n\r\n.demo-form-inline .el-select {\r\n    --el-select-width: 220px;\r\n}\r\n/deep/ .el-radio-group {\r\n    width: 220px;\r\n}\r\n.form-title {\r\n text-align: left;\r\n padding-left: 30px;\r\n margin: 20px auto 10px;\r\n height: 44px;\r\n background-color: #f2f2f2;\r\n border-radius: 5px;\r\n line-height: 44px;\r\n}\r\n/deep/  .el-form-item {\r\n    align-items: flex-start;\r\n}\r\n</style>"], "mappings": ";;;;;uBACIA,YAAA,CAsBUC,kBAAA;IAtBD,aAAW,EAAC,OAAO;IAAEC,MAAM,EAAE,IAAI;IAAGC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IAAEC,KAAK,EAAC;;sBAC5D,MAEe,CAFfC,YAAA,CAEeC,uBAAA;MAFDC,KAAK,EAAC,KAAK;MAACC,QAAQ,EAAR;;wBACtB,MAA+C,CAA/CH,YAAA,CAA+CI,mBAAA;oBAA5BP,MAAA,CAAAC,IAAI,CAACO,QAAQ;mEAAbR,MAAA,CAAAC,IAAI,CAACO,QAAQ,GAAAC,MAAA;QAAEC,SAAS,EAAT;;;QAGtCC,mBAAA,0RAImB,EACnBA,mBAAA,0IAEmB,EACnBR,YAAA,CAEeC,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,QAAQ,EAAR;;wBACrB,MAA8C,CAA9CH,YAAA,CAA8CI,mBAAA;oBAA3BP,MAAA,CAAAC,IAAI,CAACW,QAAQ;mEAAbZ,MAAA,CAAAC,IAAI,CAACW,QAAQ,GAAAH,MAAA;QAAEC,SAAS,EAAT;;;QAEtCC,mBAAA,gRAKmB,C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}