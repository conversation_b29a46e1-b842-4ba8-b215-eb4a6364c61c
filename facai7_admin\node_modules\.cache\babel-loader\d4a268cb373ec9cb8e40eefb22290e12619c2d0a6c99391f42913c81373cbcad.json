{"ast": null, "code": "import { nextTick, onMounted, ref } from 'vue';\nexport default {\n  __name: 'editPop',\n  props: ['item'],\n  setup(__props, {\n    expose: __expose\n  }) {\n    const form = ref({\n      username: '',\n      money: '',\n      status: '',\n      money: '',\n      way: '',\n      huilv: '',\n      money2: '',\n      remark: ''\n    });\n    const props = __props;\n    onMounted(() => {\n      nextTick(() => {\n        form.value = Object.assign(form, props.item);\n      });\n    });\n    __expose({\n      form\n    });\n    const __returned__ = {\n      form,\n      props,\n      nextTick,\n      onMounted,\n      ref\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["nextTick", "onMounted", "ref", "form", "username", "money", "status", "way", "huilv", "money2", "remark", "props", "__props", "value", "Object", "assign", "item", "__expose"], "sources": ["D:/WorkSpace/facai7/facai7_admin/src/views/userManage/components/UsdtChargeList/editPop.vue"], "sourcesContent": ["<template>\r\n    <el-form label-width=\"80px\" :inline=\"true\" :model=\"form\" class=\"demo-form-inline\">\r\n        <el-form-item label=\"用户名\" required >\r\n            <el-input v-model=\"form.username\" disabled  />\r\n        </el-form-item>\r\n        <el-form-item label=\"余额\" required >\r\n            <el-input v-model=\"form.money\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"类型\" required>\r\n            <el-radio-group v-model=\"form.status\">\r\n                <el-radio value=\"1\">通过</el-radio>\r\n                <el-radio value=\"2\">拒绝</el-radio>\r\n            </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"充值方式\"  required>\r\n            <el-input v-model=\"form.way\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"充值金额\"  required>\r\n            <el-input v-model=\"form.money\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"汇率\"  required>\r\n            <el-input v-model=\"form.huilv\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"实际金额\"  required>\r\n            <el-input v-model=\"form.money2\"  disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" required>\r\n            <el-input v-model=\"form.remark\" clearable />\r\n        </el-form-item>\r\n    </el-form>\r\n</template>\r\n\r\n<script setup>\r\nimport { nextTick, onMounted, ref } from 'vue'\r\n\r\nconst form = ref({\r\n    username: '',\r\n    money: '',\r\n    status: '',\r\n    money: '',\r\n    way: '',\r\n    huilv: '',\r\n    money2: '',\r\n    remark: '',\r\n})\r\nconst props = defineProps(['item'])\r\n\r\nonMounted(() => {\r\n    nextTick(()=> {\r\n        form.value = Object.assign(form, props.item)\r\n    })\r\n})\r\n\r\ndefineExpose({form})\r\n\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.demo-form-inline {\r\n    justify-content: flex-start;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n}\r\n.demo-form-inline .el-input {\r\n    --el-input-width: 220px;\r\n}\r\n\r\n.demo-form-inline .el-select {\r\n    --el-select-width: 220px;\r\n}\r\n/deep/ .el-radio-group {\r\n    width: 220px;\r\n}\r\n.form-title {\r\n text-align: left;\r\n padding-left: 30px;\r\n margin: 20px auto 10px;\r\n height: 44px;\r\n background-color: #f2f2f2;\r\n border-radius: 5px;\r\n line-height: 44px;\r\n}\r\n</style>"], "mappings": "AAiCA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,GAAG,QAAQ,KAAK;;;;;;;IAE9C,MAAMC,IAAI,GAAGD,GAAG,CAAC;MACbE,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,EAAE;MACVD,KAAK,EAAE,EAAE;MACTE,GAAG,EAAE,EAAE;MACPC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE;IACZ,CAAC,CAAC;IACF,MAAMC,KAAK,GAAGC,OAAqB;IAEnCX,SAAS,CAAC,MAAM;MACZD,QAAQ,CAAC,MAAK;QACVG,IAAI,CAACU,KAAK,GAAGC,MAAM,CAACC,MAAM,CAACZ,IAAI,EAAEQ,KAAK,CAACK,IAAI,CAAC;MAChD,CAAC,CAAC;IACN,CAAC,CAAC;IAEFC,QAAY,CAAC;MAACd;IAAI,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}