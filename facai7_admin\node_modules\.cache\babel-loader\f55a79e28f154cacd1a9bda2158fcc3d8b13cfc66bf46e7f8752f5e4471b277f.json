{"ast": null, "code": "import { getCurrentInstance, ref } from \"vue\";\n\n// 已弃用\nconst fileUploadHoook = () => {\n  const {\n    proxy\n  } = getCurrentInstance();\n  const fileLoading = ref(false);\n  const fileUpload = async (file, files) => {\n    fileLoading.value = true;\n    const res = await proxy.$http({\n      method: \"post\",\n      url: \"index/upload\",\n      data: {\n        file: file\n      }\n    });\n    fileLoading.value = false;\n  };\n  const fileChange = async (file, files) => {\n    // fileUpload(file)\n    // return file;\n  };\n  return {\n    fileUpload,\n    fileChange\n  };\n};\nexport default fileUploadHoook;", "map": {"version": 3, "names": ["getCurrentInstance", "ref", "fileUploadHoook", "proxy", "fileLoading", "fileUpload", "file", "files", "value", "res", "$http", "method", "url", "data", "fileChange"], "sources": ["D:/WorkSpace/facai7/facai7_admin/src/hooks/fileUpload.js"], "sourcesContent": ["import { getCurrentInstance, ref } from \"vue\";\r\n\r\n\r\n// 已弃用\r\nconst fileUploadHoook = () => {\r\n\r\n  const { proxy } = getCurrentInstance();\r\n  const fileLoading = ref(false);\r\n\r\n  const fileUpload = async (file, files) => {\r\n    fileLoading.value = true;\r\n    const res = await proxy.$http({\r\n      method: \"post\",\r\n      url: \"index/upload\",\r\n      data: {\r\n        file: file,\r\n      },\r\n    });\r\n    fileLoading.value = false;\r\n    \r\n  };\r\n\r\n  const fileChange = async (file, files) => {\r\n    // fileUpload(file)\r\n    // return file;\r\n  };\r\n\r\n  return {\r\n    fileUpload,\r\n    fileChange,\r\n  };\r\n};\r\n\r\nexport default fileUploadHoook;\r\n"], "mappings": "AAAA,SAASA,kBAAkB,EAAEC,GAAG,QAAQ,KAAK;;AAG7C;AACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAE5B,MAAM;IAAEC;EAAM,CAAC,GAAGH,kBAAkB,CAAC,CAAC;EACtC,MAAMI,WAAW,GAAGH,GAAG,CAAC,KAAK,CAAC;EAE9B,MAAMI,UAAU,GAAG,MAAAA,CAAOC,IAAI,EAAEC,KAAK,KAAK;IACxCH,WAAW,CAACI,KAAK,GAAG,IAAI;IACxB,MAAMC,GAAG,GAAG,MAAMN,KAAK,CAACO,KAAK,CAAC;MAC5BC,MAAM,EAAE,MAAM;MACdC,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE;QACJP,IAAI,EAAEA;MACR;IACF,CAAC,CAAC;IACFF,WAAW,CAACI,KAAK,GAAG,KAAK;EAE3B,CAAC;EAED,MAAMM,UAAU,GAAG,MAAAA,CAAOR,IAAI,EAAEC,KAAK,KAAK;IACxC;IACA;EAAA,CACD;EAED,OAAO;IACLF,UAAU;IACVS;EACF,CAAC;AACH,CAAC;AAED,eAAeZ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}