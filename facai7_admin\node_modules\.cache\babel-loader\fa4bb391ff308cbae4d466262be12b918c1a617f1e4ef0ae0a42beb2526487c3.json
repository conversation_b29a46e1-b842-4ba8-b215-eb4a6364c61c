{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode } from \"vue\";\nconst _hoisted_1 = [\"src\"];\nconst _hoisted_2 = {\n  style: {\n    \"border\": \"1px solid #ccc\"\n  }\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_Plus = _resolveComponent(\"Plus\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_upload = _resolveComponent(\"el-upload\");\n  const _component_Toolbar = _resolveComponent(\"Toolbar\");\n  const _component_Editor = _resolveComponent(\"Editor\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createBlock(_component_el_form, {\n    \"label-width\": \"100px\",\n    inline: true,\n    model: $setup.form,\n    class: \"demo-form-inline\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_form_item, {\n      label: \"标题\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.title,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.form.title = $event),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"项目分类\",\n      prop: \"class_id\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_select, {\n        modelValue: $setup.form.class_id,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.form.class_id = $event),\n        placeholder: \"项目分类\",\n        clearable: \"\"\n      }, {\n        default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.typesEnum, item => {\n          return _openBlock(), _createBlock(_component_el_option, {\n            label: item.title,\n            key: item.title,\n            value: item.id\n          }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n        }), 128 /* KEYED_FRAGMENT */))]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"价格\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.price,\n        \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.form.price = $event),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"排序\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.sort,\n        \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.form.sort = $event),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"图片\",\n      prop: \"img\",\n      rules: [{\n        required: true,\n        message: '请上传图片'\n      }]\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_upload, {\n        class: \"upload-demo\",\n        style: {\n          \"width\": \"114px\"\n        },\n        \"show-file-list\": false,\n        drag: \"\",\n        headers: $setup.headers,\n        action: `${$setup.proxy.BASE_API_URL}index/upload`,\n        \"on-success\": $setup.successUpload,\n        \"on-error\": $setup.handleErr,\n        multiple: false\n      }, {\n        default: _withCtx(() => [$setup.form.img ? (_openBlock(), _createElementBlock(\"img\", {\n          key: 0,\n          src: $setup.proxy.IMG_BASE_URL + $setup.form.img,\n          width: \"100%\",\n          class: \"avatar\"\n        }, null, 8 /* PROPS */, _hoisted_1)) : (_openBlock(), _createBlock(_component_el_icon, {\n          key: 1,\n          class: \"avatar-uploader-icon\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_Plus)]),\n          _: 1 /* STABLE */\n        }))]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"headers\", \"action\"])]),\n      _: 1 /* STABLE */\n    }), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_Toolbar, {\n      style: {\n        \"border-bottom\": \"1px solid #ccc\"\n      },\n      editor: $setup.editorRef,\n      defaultConfig: $setup.toolbarConfig,\n      mode: $setup.mode\n    }, null, 8 /* PROPS */, [\"editor\", \"mode\"]), _createVNode(_component_Editor, {\n      style: {\n        \"height\": \"500px\",\n        \"overflow-y\": \"hidden\"\n      },\n      modelValue: $setup.form.content,\n      \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.form.content = $event),\n      defaultConfig: $setup.editorConfig,\n      mode: $setup.mode,\n      onOnCreated: $setup.handleCreated\n    }, null, 8 /* PROPS */, [\"modelValue\", \"mode\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\"]);\n}", "map": {"version": 3, "names": ["style", "_createBlock", "_component_el_form", "inline", "model", "$setup", "form", "class", "_createVNode", "_component_el_form_item", "label", "required", "_component_el_input", "title", "$event", "clearable", "prop", "_component_el_select", "class_id", "placeholder", "_createElementBlock", "_Fragment", "_renderList", "typesEnum", "item", "_component_el_option", "key", "value", "id", "price", "sort", "rules", "message", "_component_el_upload", "drag", "headers", "action", "proxy", "BASE_API_URL", "successUpload", "handleErr", "multiple", "img", "src", "IMG_BASE_URL", "width", "_component_el_icon", "_component_Plus", "_createElementVNode", "_hoisted_2", "_component_Toolbar", "editor", "editor<PERSON><PERSON>", "defaultConfig", "toolbarConfig", "mode", "_component_Editor", "content", "editorConfig", "onOnCreated", "handleCreated"], "sources": ["D:\\WorkSpace\\facai7\\facai7_admin\\src\\views\\goodsManage\\components\\goodsList\\editPop.vue"], "sourcesContent": ["<template>\r\n  <el-form\r\n    label-width=\"100px\"\r\n    :inline=\"true\"\r\n    :model=\"form\"\r\n    class=\"demo-form-inline\"\r\n  >\r\n    <el-form-item label=\"标题\" required>\r\n      <el-input v-model=\"form.title\" clearable />\r\n    </el-form-item>\r\n\r\n    <el-form-item\r\n        label=\"项目分类\"\r\n        prop=\"class_id\"\r\n      >\r\n        <el-select v-model=\"form.class_id\" placeholder=\"项目分类\" clearable>\r\n          <el-option\r\n            v-for=\"item in typesEnum\"\r\n            :label=\"item.title\"\r\n            :key=\"item.title\"\r\n            :value=\"item.id\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n    <el-form-item label=\"价格\" required>\r\n      <el-input v-model=\"form.price\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"排序\" required>\r\n      <el-input v-model=\"form.sort\" clearable />\r\n    </el-form-item>\r\n    <el-form-item\r\n      label=\"图片\"\r\n      prop=\"img\"\r\n      :rules=\"[{ required: true, message: '请上传图片' }]\"\r\n    >\r\n      <el-upload\r\n        class=\"upload-demo\"\r\n        style=\"width: 114px\"\r\n        :show-file-list=\"false\"\r\n        drag\r\n        :headers=\"headers\"\r\n        :action=\"`${proxy.BASE_API_URL}index/upload`\"\r\n        :on-success=\"successUpload\"\r\n        :on-error=\"handleErr\"\r\n        :multiple=\"false\"\r\n      >\r\n        <img v-if=\"form.img\" :src=\"proxy.IMG_BASE_URL + form.img\" width=\"100%\" class=\"avatar\" />\r\n        <el-icon v-else class=\"avatar-uploader-icon\"><Plus /></el-icon\r\n      ></el-upload>\r\n    </el-form-item>\r\n    <div style=\"border: 1px solid #ccc\">\r\n      <Toolbar\r\n        style=\"border-bottom: 1px solid #ccc\"\r\n        :editor=\"editorRef\"\r\n        :defaultConfig=\"toolbarConfig\"\r\n        :mode=\"mode\"\r\n      />\r\n      <Editor\r\n        style=\"height: 500px; overflow-y: hidden\"\r\n        v-model=\"form.content\"\r\n        :defaultConfig=\"editorConfig\"\r\n        :mode=\"mode\"\r\n        @onCreated=\"handleCreated\"\r\n      />\r\n    </div>\r\n  </el-form>\r\n</template>\r\n\r\n<script setup>\r\nimport { rolesEnums } from \"@/config/enums\";\r\nimport { htmlDecodeByRegExp } from '@/utils/utils'\r\nimport { onBeforeUnmount, nextTick, ref, shallowRef, onMounted, getCurrentInstance } from \"vue\";\r\nimport { getTokenAUTH } from \"@/utils/auth\";\r\n\r\nconst form = ref({\r\n  title: \"\",\r\n  price: \"\",\r\n  class_id: \"\",\r\n  sort: \"\",\r\n  content: \"\",\r\n  img: \"\",\r\n});\r\nconst props = defineProps([\"item\"]);\r\n\r\nconst headers = ref({})\r\nonMounted(() => {\r\n  headers.value['Accept-Token'] = getTokenAUTH()\r\n  nextTick(() => {\r\n    props.item.content = htmlDecodeByRegExp(props.item.content)\r\n    form.value = Object.assign(form.value, props.item);\r\n  });\r\n  getTYpesEnum()\r\n});\r\n\r\nconst { proxy } = getCurrentInstance()\r\n\r\nconst typesEnum = ref([])\r\n\r\nconst getTYpesEnum = async () => {\r\n    const res = await proxy.$http({\r\n        method: 'get',\r\n        url: '/Goods/getGoodsClassLists'\r\n    })\r\n    if (res.code == 0) {\r\n        typesEnum.value = res.data.data\r\n    }\r\n}\r\n\r\n\r\nconst successUpload = (res) => {\r\n  form.value.img = res.data.url;\r\n};\r\n\r\nconst handleErr = (err) => {\r\n  if (err.status == 320) {\r\n    form.value.img = JSON.parse(err.message).data.url;\r\n  }\r\n}\r\n\r\n// 编辑器实例，必须用 shallowRef\r\nconst editorRef = shallowRef();\r\n\r\n// 内容 HTML\r\nconst valueHtml = ref(\"<p>hello</p>\");\r\nconst mode = ref(\"default\");\r\n\r\nconst toolbarConfig = {};\r\nconst editorConfig =  {\r\n  placeholder: \"请输入内容...\",\r\n  MENU_CONF: {\r\n    uploadImage: {\r\n      fieldName: \"file\",\r\n      maxFileSize: 10 * 1024 * 1024, // 10M\r\n      server: proxy.BASE_API_URL + \"index/uploadX\",\r\n      headers: {\r\n        \"Accept-Token\": getTokenAUTH(),\r\n      },\r\n      customInsert(res, insertFn) {\r\n        const url = proxy.IMG_BASE_URL + res.data.url;\r\n        const alt = res.data.alt\r\n        const href = res.data.href\r\n        insertFn(url, alt, href);\r\n      },\r\n    },\r\n  },\r\n};\r\n\r\n// 组件销毁时，也及时销毁编辑器\r\nonBeforeUnmount(() => {\r\n  const editor = editorRef.value;\r\n  if (editor == null) return;\r\n  editor.destroy();\r\n});\r\n\r\nconst handleCreated = (editor) => {\r\n  editorRef.value = editor; // 记录 editor 实例，重要！\r\n};\r\n\r\ndefineExpose({ form });\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.demo-form-inline {\r\n  justify-content: flex-start;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.demo-form-inline .el-input {\r\n  --el-input-width: 220px;\r\n}\r\n\r\n.demo-form-inline .el-select {\r\n  --el-select-width: 220px;\r\n}\r\n\r\n/deep/ .el-radio-group {\r\n  width: 220px;\r\n}\r\n\r\n.form-title {\r\n  text-align: left;\r\n  padding-left: 30px;\r\n  margin: 20px auto 10px;\r\n  height: 44px;\r\n  background-color: #f2f2f2;\r\n  border-radius: 5px;\r\n  line-height: 44px;\r\n}\r\n\r\n/deep/ .el-form-item {\r\n  align-items: flex-start;\r\n}\r\n</style>\r\n"], "mappings": ";;;EAkDSA,KAA8B,EAA9B;IAAA;EAAA;AAA8B;;;;;;;;;;;;uBAjDrCC,YAAA,CAgEUC,kBAAA;IA/DR,aAAW,EAAC,OAAO;IAClBC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IACZC,KAAK,EAAC;;sBAEN,MAEe,CAFfC,YAAA,CAEeC,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,QAAQ,EAAR;;wBACvB,MAA2C,CAA3CH,YAAA,CAA2CI,mBAAA;oBAAxBP,MAAA,CAAAC,IAAI,CAACO,KAAK;mEAAVR,MAAA,CAAAC,IAAI,CAACO,KAAK,GAAAC,MAAA;QAAEC,SAAS,EAAT;;;QAGjCP,YAAA,CAYiBC,uBAAA;MAXbC,KAAK,EAAC,MAAM;MACZM,IAAI,EAAC;;wBAEL,MAOY,CAPZR,YAAA,CAOYS,oBAAA;oBAPQZ,MAAA,CAAAC,IAAI,CAACY,QAAQ;mEAAbb,MAAA,CAAAC,IAAI,CAACY,QAAQ,GAAAJ,MAAA;QAAEK,WAAW,EAAC,MAAM;QAACJ,SAAS,EAAT;;0BAElD,MAAyB,E,kBAD3BK,mBAAA,CAKEC,SAAA,QAAAC,WAAA,CAJejB,MAAA,CAAAkB,SAAS,EAAjBC,IAAI;+BADbvB,YAAA,CAKEwB,oBAAA;YAHCf,KAAK,EAAEc,IAAI,CAACX,KAAK;YACjBa,GAAG,EAAEF,IAAI,CAACX,KAAK;YACfc,KAAK,EAAEH,IAAI,CAACI;;;;;;QAIrBpB,YAAA,CAEeC,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,QAAQ,EAAR;;wBACvB,MAA2C,CAA3CH,YAAA,CAA2CI,mBAAA;oBAAxBP,MAAA,CAAAC,IAAI,CAACuB,KAAK;mEAAVxB,MAAA,CAAAC,IAAI,CAACuB,KAAK,GAAAf,MAAA;QAAEC,SAAS,EAAT;;;QAEjCP,YAAA,CAEeC,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,QAAQ,EAAR;;wBACvB,MAA0C,CAA1CH,YAAA,CAA0CI,mBAAA;oBAAvBP,MAAA,CAAAC,IAAI,CAACwB,IAAI;mEAATzB,MAAA,CAAAC,IAAI,CAACwB,IAAI,GAAAhB,MAAA;QAAEC,SAAS,EAAT;;;QAEhCP,YAAA,CAmBeC,uBAAA;MAlBbC,KAAK,EAAC,IAAI;MACVM,IAAI,EAAC,KAAK;MACTe,KAAK,EAAE;QAAApB,QAAA;QAAAqB,OAAA;MAAA;;wBAER,MAaa,CAbbxB,YAAA,CAaayB,oBAAA;QAZX1B,KAAK,EAAC,aAAa;QACnBP,KAAoB,EAApB;UAAA;QAAA,CAAoB;QACnB,gBAAc,EAAE,KAAK;QACtBkC,IAAI,EAAJ,EAAI;QACHC,OAAO,EAAE9B,MAAA,CAAA8B,OAAO;QAChBC,MAAM,KAAK/B,MAAA,CAAAgC,KAAK,CAACC,YAAY;QAC7B,YAAU,EAAEjC,MAAA,CAAAkC,aAAa;QACzB,UAAQ,EAAElC,MAAA,CAAAmC,SAAS;QACnBC,QAAQ,EAAE;;0BAXZ,MAKJ,CAQgBpC,MAAA,CAAAC,IAAI,CAACoC,GAAG,I,cAAnBtB,mBAAA,CAAwF;;UAAlEuB,GAAG,EAAEtC,MAAA,CAAAgC,KAAK,CAACO,YAAY,GAAGvC,MAAA,CAAAC,IAAI,CAACoC,GAAG;UAAEG,KAAK,EAAC,MAAM;UAACtC,KAAK,EAAC;8DAC7EN,YAAA,CAAuE6C,kBAAA;;UAAvDvC,KAAK,EAAC;;4BAAuB,MAAQ,CAARC,YAAA,CAAQuC,eAAA,E;;;;;;QAGzDC,mBAAA,CAcM,OAdNC,UAcM,GAbJzC,YAAA,CAKE0C,kBAAA;MAJAlD,KAAqC,EAArC;QAAA;MAAA,CAAqC;MACpCmD,MAAM,EAAE9C,MAAA,CAAA+C,SAAS;MACjBC,aAAa,EAAEhD,MAAA,CAAAiD,aAAa;MAC5BC,IAAI,EAAElD,MAAA,CAAAkD;iDAET/C,YAAA,CAMEgD,iBAAA;MALAxD,KAAyC,EAAzC;QAAA;QAAA;MAAA,CAAyC;kBAChCK,MAAA,CAAAC,IAAI,CAACmD,OAAO;iEAAZpD,MAAA,CAAAC,IAAI,CAACmD,OAAO,GAAA3C,MAAA;MACpBuC,aAAa,EAAEhD,MAAA,CAAAqD,YAAY;MAC3BH,IAAI,EAAElD,MAAA,CAAAkD,IAAI;MACVI,WAAS,EAAEtD,MAAA,CAAAuD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}