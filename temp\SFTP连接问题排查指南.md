# SFTP连接超时问题排查指南

## 🔍 问题分析

您遇到的错误：`Error: Timeout while connecting to server`

这通常由以下原因引起：
1. 网络连接不稳定
2. 服务器FTP服务未启动
3. 防火墙阻止连接
4. FTP配置参数不当

## 🔧 解决方案

### 1. 优化的SFTP配置

已为您添加了以下优化参数：
```json
{
    "connectTimeout": 30000,    // 连接超时30秒
    "keepalive": 10000,         // 保持连接10秒
    "passive": true             // 使用被动模式
}
```

### 2. 网络连通性测试

```powershell
# 测试服务器是否可达
ping 104.249.172.87

# 测试FTP端口
telnet 104.249.172.87 21
# 如果连接成功，会显示FTP服务器欢迎信息
```

### 3. 替代连接方案

#### 方案A：使用SFTP协议（推荐）
如果服务器支持SSH，修改配置：
```json
{
    "protocol": "sftp",
    "port": 22,
    "connectTimeout": 30000
}
```

#### 方案B：使用FTP被动模式
```json
{
    "protocol": "ftp",
    "passive": true,
    "connectTimeout": 30000
}
```

#### 方案C：手动FTP工具测试
使用FileZilla等FTP工具测试连接：
- 主机：104.249.172.87
- 用户名：facai7
- 密码：ZJNp72j6hh9R
- 端口：21

## 🧪 测试步骤

### 1. 基础网络测试
```powershell
# 1. 测试网络连通性
ping 104.249.172.87

# 2. 测试端口开放
telnet 104.249.172.87 21

# 3. 如果telnet连接成功，尝试FTP命令
ftp 104.249.172.87
# 输入用户名和密码
```

### 2. VSCode SFTP测试
```bash
# 在VSCode中
Ctrl+Shift+P → SFTP: Test Connection
```

### 3. 检查防火墙
```powershell
# Windows防火墙检查
netsh advfirewall show allprofiles

# 临时关闭防火墙测试（不推荐长期使用）
netsh advfirewall set allprofiles state off
```

## 🔄 备用同步方案

### 1. 使用Git同步（推荐）
如果SFTP持续有问题，可以考虑Git方式：
```bash
# 在远程服务器设置Git仓库
git clone [your-repo-url]

# 本地推送代码
git add .
git commit -m "update code"
git push origin main

# 远程服务器拉取
git pull origin main
```

### 2. 使用rsync（Linux/WSL）
```bash
# 如果有WSL环境
rsync -avz --exclude='vendor/' --exclude='runtime/' \
  ./facai7_api/ user@104.249.172.87:/path/to/remote/
```

### 3. 手动FTP工具
- **FileZilla**：图形界面FTP客户端
- **WinSCP**：Windows下的SFTP/FTP客户端

## ⚠️ 常见问题解决

### 问题1：连接被拒绝
- 检查服务器FTP服务是否运行
- 确认端口21是否开放
- 检查用户名密码是否正确

### 问题2：连接超时
- 增加connectTimeout值
- 尝试使用被动模式
- 检查网络稳定性

### 问题3：认证失败
- 确认用户名密码正确
- 检查用户是否有FTP权限
- 尝试重置FTP密码

## 🎯 推荐操作顺序

1. **先测试网络连通性**
   ```powershell
   ping 104.249.172.87
   telnet 104.249.172.87 21
   ```

2. **使用优化后的SFTP配置重试**
   ```bash
   VSCode: Ctrl+Shift+P → SFTP: Test Connection
   ```

3. **如果仍然失败，尝试手动FTP工具**
   - 下载FileZilla
   - 使用相同配置测试连接

4. **考虑备用同步方案**
   - Git方式同步代码
   - 或联系服务器管理员检查FTP服务

## 📞 联系服务器管理员

如果以上方法都无效，建议联系服务器管理员确认：
- FTP服务是否正常运行
- 防火墙是否允许FTP连接
- 用户权限是否正确配置
- 是否支持SFTP协议（更安全）

现在请先尝试网络连通性测试，然后使用优化后的配置重新连接！
