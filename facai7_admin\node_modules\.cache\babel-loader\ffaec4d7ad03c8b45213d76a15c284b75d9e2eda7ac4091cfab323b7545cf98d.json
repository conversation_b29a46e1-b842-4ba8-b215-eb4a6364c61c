{"ast": null, "code": "import { nextTick, onMounted, ref } from 'vue';\nimport { withdrawTypeEnums, getLabelByVal } from '@/config/enums';\nexport default {\n  __name: 'editPop',\n  props: ['item'],\n  setup(__props, {\n    expose: __expose\n  }) {\n    const form = ref({\n      title: \"\",\n      lv0: \"\",\n      lv1: \"\",\n      profit: \"\",\n      profit_rate: \"\"\n    });\n    const props = __props;\n    onMounted(() => {\n      nextTick(() => {\n        form.value = Object.assign(form, props.item);\n      });\n    });\n    __expose({\n      form\n    });\n    const __returned__ = {\n      form,\n      props,\n      nextTick,\n      onMounted,\n      ref,\n      get withdrawTypeEnums() {\n        return withdrawTypeEnums;\n      },\n      get getLabelByVal() {\n        return getLabelByVal;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["nextTick", "onMounted", "ref", "withdrawTypeEnums", "getLabelByVal", "form", "title", "lv0", "lv1", "profit", "profit_rate", "props", "__props", "value", "Object", "assign", "item", "__expose"], "sources": ["D:/WorkSpace/facai7/facai7_admin/src/views/userManage/components/teamLevel/editPop.vue"], "sourcesContent": ["<template>\r\n    <el-form label-width=\"180px\" :inline=\"true\" :model=\"form\" class=\"demo-form-inline\">\r\n        <el-form-item label=\"名称\" required>\r\n            <el-input v-model=\"form.title\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"全部人数\" required>\r\n            <el-input v-model=\"form.lv0\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"直推人数\" required>\r\n            <el-input v-model=\"form.lv1\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"团队盈利\" required>\r\n            <el-input v-model=\"form.profit\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"盈利奖励（%）\" required>\r\n            <el-input v-model=\"form.profit_rate\" />\r\n        </el-form-item>\r\n    </el-form>\r\n</template>\r\n\r\n<script setup>\r\nimport { nextTick, onMounted, ref } from 'vue'\r\nimport { withdrawTypeEnums, getLabelByVal } from '@/config/enums'\r\n\r\nconst form = ref({\r\n    title: \"\",\r\n    lv0: \"\",\r\n    lv1: \"\",\r\n    profit: \"\",\r\n    profit_rate: \"\",\r\n})\r\nconst props = defineProps(['item'])\r\n\r\nonMounted(() => {\r\n    nextTick(() => {\r\n        form.value = Object.assign(form, props.item)\r\n    })\r\n})\r\n\r\ndefineExpose({ form })\r\n\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.demo-form-inline {\r\n    justify-content: flex-start;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n}\r\n\r\n.demo-form-inline .el-input {\r\n    --el-input-width: 220px;\r\n}\r\n\r\n.demo-form-inline .el-select {\r\n    --el-select-width: 220px;\r\n}\r\n\r\n/deep/ .el-radio-group {\r\n    width: 220px;\r\n}\r\n\r\n.form-title {\r\n    text-align: left;\r\n    padding-left: 30px;\r\n    margin: 20px auto 10px;\r\n    height: 44px;\r\n    background-color: #f2f2f2;\r\n    border-radius: 5px;\r\n    line-height: 44px;\r\n    width: 100%;\r\n}\r\n\r\n/deep/ .el-form-item {\r\n    align-items: flex-start;\r\n}\r\n</style>"], "mappings": "AAqBA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,GAAG,QAAQ,KAAK;AAC9C,SAASC,iBAAiB,EAAEC,aAAa,QAAQ,gBAAgB;;;;;;;IAEjE,MAAMC,IAAI,GAAGH,GAAG,CAAC;MACbI,KAAK,EAAE,EAAE;MACTC,GAAG,EAAE,EAAE;MACPC,GAAG,EAAE,EAAE;MACPC,MAAM,EAAE,EAAE;MACVC,WAAW,EAAE;IACjB,CAAC,CAAC;IACF,MAAMC,KAAK,GAAGC,OAAqB;IAEnCX,SAAS,CAAC,MAAM;MACZD,QAAQ,CAAC,MAAM;QACXK,IAAI,CAACQ,KAAK,GAAGC,MAAM,CAACC,MAAM,CAACV,IAAI,EAAEM,KAAK,CAACK,IAAI,CAAC;MAChD,CAAC,CAAC;IACN,CAAC,CAAC;IAEFC,QAAY,CAAC;MAAEZ;IAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}