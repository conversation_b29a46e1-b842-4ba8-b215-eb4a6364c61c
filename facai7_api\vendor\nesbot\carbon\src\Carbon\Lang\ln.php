<?php

/**
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

/*
 * Authors: <AUTHORS>
 */
return array_replace_recursive(require __DIR__.'/en.php', [
    'formats' => [
        'LT' => 'HH:mm',
        'LTS' => 'HH:mm:ss',
        'L' => 'D/M/YYYY',
        'LL' => 'D MMM YYYY',
        'LLL' => 'D MMMM YYYY HH:mm',
        'LLLL' => 'dddd D MMMM YYYY HH:mm',
    ],
    'months' => ['sánzá ya yambo', 'sánzá ya míbalé', 'sánz<PERSON> ya mísáto', 'sánz<PERSON> ya mínei', 'sánz<PERSON> ya mítáno', 's<PERSON><PERSON><PERSON> ya motóbá', 's<PERSON><PERSON><PERSON> ya nsambo', 's<PERSON><PERSON><PERSON> ya mwambe', 's<PERSON>z<PERSON> ya libwa', 's<PERSON><PERSON><PERSON> ya zómi', 'sánz<PERSON> ya zómi na mɔ̌kɔ́', 's<PERSON><PERSON><PERSON> ya zómi na míbalé'],
    'months_short' => ['yan', 'fbl', 'msi', 'apl', 'mai', 'yun', 'yul', 'agt', 'stb', 'ɔtb', 'nvb', 'dsb'],
    'weekdays' => ['Lomíngo', 'Mosálá mɔ̌kɔ́', 'Misálá míbalé', 'Misálá mísáto', 'Misálá mínei', 'Misálá mítáno', 'Mpɔ́sɔ'],
    'weekdays_short' => ['m1.', 'm2.', 'm3.', 'm4.', 'm5.', 'm6.', 'm7.'],
    'weekdays_min' => ['m1.', 'm2.', 'm3.', 'm4.', 'm5.', 'm6.', 'm7.'],
    'first_day_of_week' => 1,
    'day_of_first_week_of_year' => 1,

    'year' => 'mbula :count',
    'y' => 'mbula :count',
    'a_year' => 'mbula :count',

    'month' => 'sánzá :count',
    'm' => 'sánzá :count',
    'a_month' => 'sánzá :count',

    'week' => 'mpɔ́sɔ :count',
    'w' => 'mpɔ́sɔ :count',
    'a_week' => 'mpɔ́sɔ :count',

    'day' => 'mokɔlɔ :count',
    'd' => 'mokɔlɔ :count',
    'a_day' => 'mokɔlɔ :count',

    'hour' => 'ngonga :count',
    'h' => 'ngonga :count',
    'a_hour' => 'ngonga :count',

    'minute' => 'miniti :count',
    'min' => 'miniti :count',
    'a_minute' => 'miniti :count',

    'second' => 'segɔnde :count',
    's' => 'segɔnde :count',
    'a_second' => 'segɔnde :count',
]);
