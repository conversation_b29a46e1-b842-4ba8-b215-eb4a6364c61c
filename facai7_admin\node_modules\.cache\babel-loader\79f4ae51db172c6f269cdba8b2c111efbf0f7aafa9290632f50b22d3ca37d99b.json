{"ast": null, "code": "import { computed } from 'vue';\nconst useYearRangeHeader = ({\n  unlinkPanels,\n  leftDate,\n  rightDate\n}) => {\n  const leftPrevYear = () => {\n    leftDate.value = leftDate.value.subtract(10, \"year\");\n    if (!unlinkPanels.value) {\n      rightDate.value = rightDate.value.subtract(10, \"year\");\n    }\n  };\n  const rightNextYear = () => {\n    if (!unlinkPanels.value) {\n      leftDate.value = leftDate.value.add(10, \"year\");\n    }\n    rightDate.value = rightDate.value.add(10, \"year\");\n  };\n  const leftNextYear = () => {\n    leftDate.value = leftDate.value.add(10, \"year\");\n  };\n  const rightPrevYear = () => {\n    rightDate.value = rightDate.value.subtract(10, \"year\");\n  };\n  const leftLabel = computed(() => {\n    const leftStartDate = Math.floor(leftDate.value.year() / 10) * 10;\n    return `${leftStartDate}-${leftStartDate + 9}`;\n  });\n  const rightLabel = computed(() => {\n    const rightStartDate = Math.floor(rightDate.value.year() / 10) * 10;\n    return `${rightStartDate}-${rightStartDate + 9}`;\n  });\n  const leftYear = computed(() => {\n    const leftEndDate = Math.floor(leftDate.value.year() / 10) * 10 + 9;\n    return leftEndDate;\n  });\n  const rightYear = computed(() => {\n    const rightStartDate = Math.floor(rightDate.value.year() / 10) * 10;\n    return rightStartDate;\n  });\n  return {\n    leftPrevYear,\n    rightNextYear,\n    leftNextYear,\n    rightPrevYear,\n    leftLabel,\n    rightLabel,\n    leftYear,\n    rightYear\n  };\n};\nexport { useYearRangeHeader };", "map": {"version": 3, "names": ["useYearRangeHeader", "unlinkPanels", "leftDate", "rightDate", "leftPrevYear", "value", "subtract", "rightNextYear", "add", "leftNextYear", "rightPrevYear", "leftLabel", "computed", "leftStartDate", "Math", "floor", "year", "<PERSON><PERSON><PERSON><PERSON>", "rightStartDate", "leftYear", "leftEndDate", "rightYear"], "sources": ["../../../../../../../packages/components/date-picker/src/composables/use-year-range-header.ts"], "sourcesContent": ["import { computed } from 'vue'\n\nimport type { Ref, ToRef } from 'vue'\nimport type { Dayjs } from 'dayjs'\n\nexport const useYearRangeHeader = ({\n  unlinkPanels,\n  leftDate,\n  rightDate,\n}: {\n  unlinkPanels: ToRef<boolean>\n  leftDate: Ref<Dayjs>\n  rightDate: Ref<Dayjs>\n}) => {\n  const leftPrevYear = () => {\n    leftDate.value = leftDate.value.subtract(10, 'year')\n    if (!unlinkPanels.value) {\n      rightDate.value = rightDate.value.subtract(10, 'year')\n    }\n  }\n\n  const rightNextYear = () => {\n    if (!unlinkPanels.value) {\n      leftDate.value = leftDate.value.add(10, 'year')\n    }\n    rightDate.value = rightDate.value.add(10, 'year')\n  }\n\n  const leftNextYear = () => {\n    leftDate.value = leftDate.value.add(10, 'year')\n  }\n\n  const rightPrevYear = () => {\n    rightDate.value = rightDate.value.subtract(10, 'year')\n  }\n\n  const leftLabel = computed(() => {\n    const leftStartDate = Math.floor(leftDate.value.year() / 10) * 10\n    return `${leftStartDate}-${leftStartDate + 9}`\n  })\n\n  const rightLabel = computed(() => {\n    const rightStartDate = Math.floor(rightDate.value.year() / 10) * 10\n    return `${rightStartDate}-${rightStartDate + 9}`\n  })\n\n  const leftYear = computed(() => {\n    const leftEndDate = Math.floor(leftDate.value.year() / 10) * 10 + 9\n    return leftEndDate\n  })\n\n  const rightYear = computed(() => {\n    const rightStartDate = Math.floor(rightDate.value.year() / 10) * 10\n    return rightStartDate\n  })\n\n  return {\n    leftPrevYear,\n    rightNextYear,\n    leftNextYear,\n    rightPrevYear,\n    leftLabel,\n    rightLabel,\n    leftYear,\n    rightYear,\n  }\n}\n"], "mappings": ";AACY,MAACA,kBAAkB,GAAGA,CAAC;EACjCC,YAAY;EACZC,QAAQ;EACRC;AACF,CAAC,KAAK;EACJ,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzBF,QAAQ,CAACG,KAAK,GAAGH,QAAQ,CAACG,KAAK,CAACC,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAC;IACpD,IAAI,CAACL,YAAY,CAACI,KAAK,EAAE;MACvBF,SAAS,CAACE,KAAK,GAAGF,SAAS,CAACE,KAAK,CAACC,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAC;IAC5D;EACA,CAAG;EACD,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAACN,YAAY,CAACI,KAAK,EAAE;MACvBH,QAAQ,CAACG,KAAK,GAAGH,QAAQ,CAACG,KAAK,CAACG,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC;IACrD;IACIL,SAAS,CAACE,KAAK,GAAGF,SAAS,CAACE,KAAK,CAACG,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC;EACrD,CAAG;EACD,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzBP,QAAQ,CAACG,KAAK,GAAGH,QAAQ,CAACG,KAAK,CAACG,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC;EACnD,CAAG;EACD,MAAME,aAAa,GAAGA,CAAA,KAAM;IAC1BP,SAAS,CAACE,KAAK,GAAGF,SAAS,CAACE,KAAK,CAACC,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAC;EAC1D,CAAG;EACD,MAAMK,SAAS,GAAGC,QAAQ,CAAC,MAAM;IAC/B,MAAMC,aAAa,GAAGC,IAAI,CAACC,KAAK,CAACb,QAAQ,CAACG,KAAK,CAACW,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE;IACjE,OAAO,GAAGH,aAAa,IAAIA,aAAa,GAAG,CAAC,EAAE;EAClD,CAAG,CAAC;EACF,MAAMI,UAAU,GAAGL,QAAQ,CAAC,MAAM;IAChC,MAAMM,cAAc,GAAGJ,IAAI,CAACC,KAAK,CAACZ,SAAS,CAACE,KAAK,CAACW,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE;IACnE,OAAO,GAAGE,cAAc,IAAIA,cAAc,GAAG,CAAC,EAAE;EACpD,CAAG,CAAC;EACF,MAAMC,QAAQ,GAAGP,QAAQ,CAAC,MAAM;IAC9B,MAAMQ,WAAW,GAAGN,IAAI,CAACC,KAAK,CAACb,QAAQ,CAACG,KAAK,CAACW,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;IACnE,OAAOI,WAAW;EACtB,CAAG,CAAC;EACF,MAAMC,SAAS,GAAGT,QAAQ,CAAC,MAAM;IAC/B,MAAMM,cAAc,GAAGJ,IAAI,CAACC,KAAK,CAACZ,SAAS,CAACE,KAAK,CAACW,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE;IACnE,OAAOE,cAAc;EACzB,CAAG,CAAC;EACF,OAAO;IACLd,YAAY;IACZG,aAAa;IACbE,YAAY;IACZC,aAAa;IACbC,SAAS;IACTM,UAAU;IACVE,QAAQ;IACRE;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}