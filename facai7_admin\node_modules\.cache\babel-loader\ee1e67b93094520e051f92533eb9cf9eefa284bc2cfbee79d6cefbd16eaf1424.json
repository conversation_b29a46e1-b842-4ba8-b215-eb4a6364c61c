{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { ref, getCurrentInstance, computed, watch, unref } from 'vue';\nimport { getRowIdentity, walkTreeNode } from '../util.mjs';\nimport { isArray } from '@vue/shared';\nimport { isUndefined } from '../../../../utils/types.mjs';\nfunction useTree(watcherData) {\n  const expandRowKeys = ref([]);\n  const treeData = ref({});\n  const indent = ref(16);\n  const lazy = ref(false);\n  const lazyTreeNodeMap = ref({});\n  const lazyColumnIdentifier = ref(\"hasChildren\");\n  const childrenColumnName = ref(\"children\");\n  const checkStrictly = ref(false);\n  const instance = getCurrentInstance();\n  const normalizedData = computed(() => {\n    if (!watcherData.rowKey.value) return {};\n    const data = watcherData.data.value || [];\n    return normalize(data);\n  });\n  const normalizedLazyNode = computed(() => {\n    const rowKey = watcherData.rowKey.value;\n    const keys = Object.keys(lazyTreeNodeMap.value);\n    const res = {};\n    if (!keys.length) return res;\n    keys.forEach(key => {\n      if (lazyTreeNodeMap.value[key].length) {\n        const item = {\n          children: []\n        };\n        lazyTreeNodeMap.value[key].forEach(row => {\n          const currentRowKey = getRowIdentity(row, rowKey);\n          item.children.push(currentRowKey);\n          if (row[lazyColumnIdentifier.value] && !res[currentRowKey]) {\n            res[currentRowKey] = {\n              children: []\n            };\n          }\n        });\n        res[key] = item;\n      }\n    });\n    return res;\n  });\n  const normalize = data => {\n    const rowKey = watcherData.rowKey.value;\n    const res = /* @__PURE__ */new Map();\n    walkTreeNode(data, (parent, children, level) => {\n      const parentId = getRowIdentity(parent, rowKey, true);\n      if (isArray(children)) {\n        res.set(parentId, {\n          children: children.map(row => row[rowKey]),\n          level\n        });\n      } else if (lazy.value) {\n        res.set(parentId, {\n          children: [],\n          lazy: true,\n          level\n        });\n      }\n    }, childrenColumnName.value, lazyColumnIdentifier.value, lazy.value);\n    return res;\n  };\n  const updateTreeData = (ifChangeExpandRowKeys = false, ifExpandAll) => {\n    var _a, _b;\n    ifExpandAll || (ifExpandAll = (_a = instance.store) == null ? void 0 : _a.states.defaultExpandAll.value);\n    const nested = normalizedData.value;\n    const normalizedLazyNode_ = normalizedLazyNode.value;\n    const newTreeData = {};\n    if (nested instanceof Map && nested.size) {\n      const oldTreeData = unref(treeData);\n      const rootLazyRowKeys = [];\n      const getExpanded = (oldValue, key) => {\n        if (ifChangeExpandRowKeys) {\n          if (expandRowKeys.value) {\n            return ifExpandAll || expandRowKeys.value.includes(key);\n          } else {\n            return !!(ifExpandAll || (oldValue == null ? void 0 : oldValue.expanded));\n          }\n        } else {\n          const included = ifExpandAll || expandRowKeys.value && expandRowKeys.value.includes(key);\n          return !!((oldValue == null ? void 0 : oldValue.expanded) || included);\n        }\n      };\n      nested.forEach((_, key) => {\n        const oldValue = oldTreeData[key];\n        const newValue = {\n          ...nested.get(key)\n        };\n        newValue.expanded = getExpanded(oldValue, key);\n        if (newValue.lazy) {\n          const {\n            loaded = false,\n            loading = false\n          } = oldValue || {};\n          newValue.loaded = !!loaded;\n          newValue.loading = !!loading;\n          rootLazyRowKeys.push(key);\n        }\n        newTreeData[key] = newValue;\n      });\n      const lazyKeys = Object.keys(normalizedLazyNode_);\n      if (lazy.value && lazyKeys.length && rootLazyRowKeys.length) {\n        lazyKeys.forEach(key => {\n          var _a2;\n          const oldValue = oldTreeData[key];\n          const lazyNodeChildren = normalizedLazyNode_[key].children;\n          if (rootLazyRowKeys.includes(key)) {\n            if (((_a2 = newTreeData[key].children) == null ? void 0 : _a2.length) !== 0) {\n              throw new Error(\"[ElTable]children must be an empty array.\");\n            }\n            newTreeData[key].children = lazyNodeChildren;\n          } else {\n            const {\n              loaded = false,\n              loading = false\n            } = oldValue || {};\n            newTreeData[key] = {\n              lazy: true,\n              loaded: !!loaded,\n              loading: !!loading,\n              expanded: getExpanded(oldValue, key),\n              children: lazyNodeChildren,\n              level: void 0\n            };\n          }\n        });\n      }\n    }\n    treeData.value = newTreeData;\n    (_b = instance.store) == null ? void 0 : _b.updateTableScrollY();\n  };\n  watch(() => expandRowKeys.value, () => {\n    updateTreeData(true);\n  });\n  watch(() => normalizedData.value, () => {\n    updateTreeData();\n  });\n  watch(() => normalizedLazyNode.value, () => {\n    updateTreeData();\n  });\n  const updateTreeExpandKeys = value => {\n    expandRowKeys.value = value;\n    updateTreeData();\n  };\n  const isUseLazy = data => {\n    return lazy.value && data && \"loaded\" in data && !data.loaded;\n  };\n  const toggleTreeExpansion = (row, expanded) => {\n    instance.store.assertRowKey();\n    const rowKey = watcherData.rowKey.value;\n    const id = getRowIdentity(row, rowKey);\n    const data = id && treeData.value[id];\n    if (id && data && \"expanded\" in data) {\n      const oldExpanded = data.expanded;\n      expanded = isUndefined(expanded) ? !data.expanded : expanded;\n      treeData.value[id].expanded = expanded;\n      if (oldExpanded !== expanded) {\n        instance.emit(\"expand-change\", row, expanded);\n      }\n      isUseLazy(data) && loadData(row, id, data);\n      instance.store.updateTableScrollY();\n    }\n  };\n  const loadOrToggle = row => {\n    instance.store.assertRowKey();\n    const rowKey = watcherData.rowKey.value;\n    const id = getRowIdentity(row, rowKey);\n    const data = treeData.value[id];\n    if (isUseLazy(data)) {\n      loadData(row, id, data);\n    } else {\n      toggleTreeExpansion(row, void 0);\n    }\n  };\n  const loadData = (row, key, treeNode) => {\n    const {\n      load\n    } = instance.props;\n    if (load && !treeData.value[key].loaded) {\n      treeData.value[key].loading = true;\n      load(row, treeNode, data => {\n        if (!isArray(data)) {\n          throw new TypeError(\"[ElTable] data must be an array\");\n        }\n        treeData.value[key].loading = false;\n        treeData.value[key].loaded = true;\n        treeData.value[key].expanded = true;\n        if (data.length) {\n          lazyTreeNodeMap.value[key] = data;\n        }\n        instance.emit(\"expand-change\", row, true);\n      });\n    }\n  };\n  const updateKeyChildren = (key, data) => {\n    const {\n      lazy: lazy2,\n      rowKey\n    } = instance.props;\n    if (!lazy2) return;\n    if (!rowKey) throw new Error(\"[Table] rowKey is required in updateKeyChild\");\n    if (lazyTreeNodeMap.value[key]) {\n      lazyTreeNodeMap.value[key] = data;\n    }\n  };\n  return {\n    loadData,\n    loadOrToggle,\n    toggleTreeExpansion,\n    updateTreeExpandKeys,\n    updateTreeData,\n    updateKeyChildren,\n    normalize,\n    states: {\n      expandRowKeys,\n      treeData,\n      indent,\n      lazy,\n      lazyTreeNodeMap,\n      lazyColumnIdentifier,\n      childrenColumnName,\n      checkStrictly\n    }\n  };\n}\nexport { useTree as default };", "map": {"version": 3, "names": ["useTree", "watcherData", "expandRowKeys", "ref", "treeData", "indent", "lazy", "lazyTreeNodeMap", "lazyColumnIdentifier", "childrenColumnName", "checkStrictly", "instance", "getCurrentInstance", "normalizedData", "computed", "<PERSON><PERSON><PERSON>", "value", "data", "normalize", "normalizedLazyNode", "keys", "Object", "res", "length", "for<PERSON>ach", "key", "item", "children", "row", "currentRowKey", "getRowIdentity", "push", "Map", "walkTreeNode", "parent", "level", "parentId", "isArray", "set", "map", "updateTreeData", "ifChangeExpandRowKeys", "ifExpandAll", "_a", "_b", "store", "states", "defaultExpandAll", "nested", "normalizedLazyNode_", "newTreeData", "size", "oldTreeData", "unref", "rootLazyRowKeys", "getExpanded", "oldValue", "includes", "expanded", "included", "_", "newValue", "get", "loaded", "loading", "lazy<PERSON>eys", "_a2", "lazy<PERSON><PERSON><PERSON><PERSON><PERSON>n", "Error", "updateTableScrollY", "watch", "updateTreeExpandKeys", "isUseLazy", "toggleTreeExpansion", "assertRowKey", "id", "oldExpanded", "isUndefined", "emit", "loadData", "loadOrToggle", "treeNode", "load", "props", "TypeError", "update<PERSON>ey<PERSON><PERSON><PERSON>n", "lazy2"], "sources": ["../../../../../../../packages/components/table/src/store/tree.ts"], "sourcesContent": ["import { computed, getCurrentInstance, ref, unref, watch } from 'vue'\nimport { isArray, isUndefined } from '@element-plus/utils'\nimport { getRowIdentity, walkTreeNode } from '../util'\n\nimport type { WatcherPropsData } from '.'\nimport type { DefaultRow, Table, TableProps, TreeNode } from '../table/defaults'\n\nexport interface TreeData extends TreeNode {\n  children?: string[]\n  lazy?: boolean\n  loaded?: boolean\n}\n\nfunction useTree<T extends DefaultRow>(watcherData: WatcherPropsData<T>) {\n  const expandRowKeys = ref<Array<string | number>>([])\n  const treeData = ref<Record<string, TreeData>>({})\n  const indent = ref(16)\n  const lazy = ref(false)\n  const lazyTreeNodeMap = ref<Record<string, T[]>>({})\n  const lazyColumnIdentifier = ref('hasChildren')\n  const childrenColumnName = ref('children')\n  const checkStrictly = ref(false)\n  const instance = getCurrentInstance() as Table<T>\n  const normalizedData = computed(() => {\n    if (!watcherData.rowKey.value) return {}\n    const data = watcherData.data.value || []\n    return normalize(data)\n  })\n  const normalizedLazyNode = computed(() => {\n    const rowKey = watcherData.rowKey.value\n    const keys = Object.keys(lazyTreeNodeMap.value)\n    const res: Record<string, { children: string[] }> = {}\n    if (!keys.length) return res\n    keys.forEach((key) => {\n      if (lazyTreeNodeMap.value[key].length) {\n        const item: typeof res[number] = { children: [] }\n        lazyTreeNodeMap.value[key].forEach((row) => {\n          const currentRowKey = getRowIdentity(row, rowKey)\n          item.children.push(currentRowKey)\n          if (row[lazyColumnIdentifier.value] && !res[currentRowKey]) {\n            res[currentRowKey] = { children: [] }\n          }\n        })\n        res[key] = item\n      }\n    })\n    return res\n  })\n\n  const normalize = (data: T[]) => {\n    const rowKey = watcherData.rowKey.value\n    const res = new Map<string | number, TreeData>() // 使用 Map 替代 Object，解决 number key 的问题\n    walkTreeNode(\n      data,\n      (parent, children, level) => {\n        const parentId = getRowIdentity(parent, rowKey, true)\n        if (isArray(children)) {\n          res.set(parentId, {\n            children: children.map((row) => row[rowKey!]),\n            level,\n          })\n        } else if (lazy.value) {\n          // 当 children 不存在且 lazy 为 true，该节点即为懒加载的节点\n          res.set(parentId, {\n            children: [],\n            lazy: true,\n            level,\n          })\n        }\n      },\n      childrenColumnName.value,\n      lazyColumnIdentifier.value,\n      lazy.value\n    )\n    return res\n  }\n\n  const updateTreeData = (\n    ifChangeExpandRowKeys = false,\n    ifExpandAll?: boolean\n  ) => {\n    ifExpandAll ||= instance.store?.states.defaultExpandAll.value\n    const nested = normalizedData.value\n    const normalizedLazyNode_ = normalizedLazyNode.value\n    const newTreeData: Record<string, TreeData> = {}\n    if (nested instanceof Map && nested.size) {\n      const oldTreeData = unref(treeData)\n      const rootLazyRowKeys: string[] = []\n      const getExpanded = (oldValue: TreeData, key: string) => {\n        if (ifChangeExpandRowKeys) {\n          if (expandRowKeys.value) {\n            return ifExpandAll || expandRowKeys.value.includes(key)\n          } else {\n            return !!(ifExpandAll || oldValue?.expanded)\n          }\n        } else {\n          const included =\n            ifExpandAll ||\n            (expandRowKeys.value && expandRowKeys.value.includes(key))\n          return !!(oldValue?.expanded || included)\n        }\n      }\n      // 合并 expanded 与 display，确保数据刷新后，状态不变\n      nested.forEach((_, key) => {\n        const oldValue = oldTreeData[key]\n        const newValue = { ...nested.get(key) }\n        newValue.expanded = getExpanded(oldValue, key)\n        if (newValue.lazy) {\n          const { loaded = false, loading = false } = oldValue || {}\n          newValue.loaded = !!loaded\n          newValue.loading = !!loading\n          rootLazyRowKeys.push(key)\n        }\n        newTreeData[key] = newValue\n      })\n      // 根据懒加载数据更新 treeData\n      const lazyKeys = Object.keys(normalizedLazyNode_)\n      if (lazy.value && lazyKeys.length && rootLazyRowKeys.length) {\n        lazyKeys.forEach((key) => {\n          const oldValue = oldTreeData[key]\n          const lazyNodeChildren = normalizedLazyNode_[key].children\n          if (rootLazyRowKeys.includes(key)) {\n            // 懒加载的 root 节点，更新一下原有的数据，原来的 children 一定是空数组\n            if (newTreeData[key].children?.length !== 0) {\n              throw new Error('[ElTable]children must be an empty array.')\n            }\n            newTreeData[key].children = lazyNodeChildren\n          } else {\n            const { loaded = false, loading = false } = oldValue || {}\n            newTreeData[key] = {\n              lazy: true,\n              loaded: !!loaded,\n              loading: !!loading,\n              expanded: getExpanded(oldValue, key),\n              children: lazyNodeChildren,\n              level: undefined,\n            }\n          }\n        })\n      }\n    }\n    treeData.value = newTreeData\n    instance.store?.updateTableScrollY()\n  }\n\n  watch(\n    () => expandRowKeys.value,\n    () => {\n      updateTreeData(true)\n    }\n  )\n\n  watch(\n    () => normalizedData.value,\n    () => {\n      updateTreeData()\n    }\n  )\n  watch(\n    () => normalizedLazyNode.value,\n    () => {\n      updateTreeData()\n    }\n  )\n\n  const updateTreeExpandKeys = (value: (string | number)[]) => {\n    expandRowKeys.value = value\n    updateTreeData()\n  }\n  const isUseLazy = (data: TreeData) => {\n    return lazy.value && data && 'loaded' in data && !data.loaded\n  }\n  const toggleTreeExpansion = (row: T, expanded?: boolean) => {\n    instance.store.assertRowKey()\n\n    const rowKey = watcherData.rowKey.value\n    const id = getRowIdentity(row, rowKey)\n    const data = id && treeData.value[id]\n    if (id && data && 'expanded' in data) {\n      const oldExpanded = data.expanded\n      expanded = isUndefined(expanded) ? !data.expanded : expanded\n      treeData.value[id].expanded = expanded\n      if (oldExpanded !== expanded) {\n        instance.emit('expand-change', row, expanded)\n      }\n      isUseLazy(data) && loadData(row, id, data)\n      instance.store.updateTableScrollY()\n    }\n  }\n\n  const loadOrToggle = (row: T) => {\n    instance.store.assertRowKey()\n    const rowKey = watcherData.rowKey.value\n    const id = getRowIdentity(row, rowKey)\n    const data = treeData.value[id]\n    if (isUseLazy(data)) {\n      loadData(row, id, data)\n    } else {\n      toggleTreeExpansion(row, undefined)\n    }\n  }\n\n  const loadData = (row: T, key: string, treeNode: TreeNode) => {\n    const { load } = instance.props as unknown as TableProps<T>\n    if (load && !treeData.value[key].loaded) {\n      treeData.value[key].loading = true\n      load(row, treeNode, (data) => {\n        if (!isArray(data)) {\n          throw new TypeError('[ElTable] data must be an array')\n        }\n        treeData.value[key].loading = false\n        treeData.value[key].loaded = true\n        treeData.value[key].expanded = true\n        if (data.length) {\n          lazyTreeNodeMap.value[key] = data\n        }\n        instance.emit('expand-change', row, true)\n      })\n    }\n  }\n\n  const updateKeyChildren = (key: string, data: T[]) => {\n    const { lazy, rowKey } = instance.props as unknown as TableProps<T>\n    if (!lazy) return\n    if (!rowKey) throw new Error('[Table] rowKey is required in updateKeyChild')\n\n    if (lazyTreeNodeMap.value[key]) {\n      lazyTreeNodeMap.value[key] = data\n    }\n  }\n\n  return {\n    loadData,\n    loadOrToggle,\n    toggleTreeExpansion,\n    updateTreeExpandKeys,\n    updateTreeData,\n    updateKeyChildren,\n    normalize,\n    states: {\n      expandRowKeys,\n      treeData,\n      indent,\n      lazy,\n      lazyTreeNodeMap,\n      lazyColumnIdentifier,\n      childrenColumnName,\n      checkStrictly,\n    },\n  }\n}\n\nexport default useTree\n"], "mappings": ";;;;;;;;AAGA,SAASA,OAAOA,CAACC,WAAW,EAAE;EAC5B,MAAMC,aAAa,GAAGC,GAAG,CAAC,EAAE,CAAC;EAC7B,MAAMC,QAAQ,GAAGD,GAAG,CAAC,EAAE,CAAC;EACxB,MAAME,MAAM,GAAGF,GAAG,CAAC,EAAE,CAAC;EACtB,MAAMG,IAAI,GAAGH,GAAG,CAAC,KAAK,CAAC;EACvB,MAAMI,eAAe,GAAGJ,GAAG,CAAC,EAAE,CAAC;EAC/B,MAAMK,oBAAoB,GAAGL,GAAG,CAAC,aAAa,CAAC;EAC/C,MAAMM,kBAAkB,GAAGN,GAAG,CAAC,UAAU,CAAC;EAC1C,MAAMO,aAAa,GAAGP,GAAG,CAAC,KAAK,CAAC;EAChC,MAAMQ,QAAQ,GAAGC,kBAAkB,EAAE;EACrC,MAAMC,cAAc,GAAGC,QAAQ,CAAC,MAAM;IACpC,IAAI,CAACb,WAAW,CAACc,MAAM,CAACC,KAAK,EAC3B,OAAO,EAAE;IACX,MAAMC,IAAI,GAAGhB,WAAW,CAACgB,IAAI,CAACD,KAAK,IAAI,EAAE;IACzC,OAAOE,SAAS,CAACD,IAAI,CAAC;EAC1B,CAAG,CAAC;EACF,MAAME,kBAAkB,GAAGL,QAAQ,CAAC,MAAM;IACxC,MAAMC,MAAM,GAAGd,WAAW,CAACc,MAAM,CAACC,KAAK;IACvC,MAAMI,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACb,eAAe,CAACS,KAAK,CAAC;IAC/C,MAAMM,GAAG,GAAG,EAAE;IACd,IAAI,CAACF,IAAI,CAACG,MAAM,EACd,OAAOD,GAAG;IACZF,IAAI,CAACI,OAAO,CAAEC,GAAG,IAAK;MACpB,IAAIlB,eAAe,CAACS,KAAK,CAACS,GAAG,CAAC,CAACF,MAAM,EAAE;QACrC,MAAMG,IAAI,GAAG;UAAEC,QAAQ,EAAE;QAAE,CAAE;QAC7BpB,eAAe,CAACS,KAAK,CAACS,GAAG,CAAC,CAACD,OAAO,CAAEI,GAAG,IAAK;UAC1C,MAAMC,aAAa,GAAGC,cAAc,CAACF,GAAG,EAAEb,MAAM,CAAC;UACjDW,IAAI,CAACC,QAAQ,CAACI,IAAI,CAACF,aAAa,CAAC;UACjC,IAAID,GAAG,CAACpB,oBAAoB,CAACQ,KAAK,CAAC,IAAI,CAACM,GAAG,CAACO,aAAa,CAAC,EAAE;YAC1DP,GAAG,CAACO,aAAa,CAAC,GAAG;cAAEF,QAAQ,EAAE;YAAE,CAAE;UACjD;QACA,CAAS,CAAC;QACFL,GAAG,CAACG,GAAG,CAAC,GAAGC,IAAI;MACvB;IACA,CAAK,CAAC;IACF,OAAOJ,GAAG;EACd,CAAG,CAAC;EACF,MAAMJ,SAAS,GAAID,IAAI,IAAK;IAC1B,MAAMF,MAAM,GAAGd,WAAW,CAACc,MAAM,CAACC,KAAK;IACvC,MAAMM,GAAG,kBAAmB,IAAIU,GAAG,EAAE;IACrCC,YAAY,CAAChB,IAAI,EAAE,CAACiB,MAAM,EAAEP,QAAQ,EAAEQ,KAAK,KAAK;MAC9C,MAAMC,QAAQ,GAAGN,cAAc,CAACI,MAAM,EAAEnB,MAAM,EAAE,IAAI,CAAC;MACrD,IAAIsB,OAAO,CAACV,QAAQ,CAAC,EAAE;QACrBL,GAAG,CAACgB,GAAG,CAACF,QAAQ,EAAE;UAChBT,QAAQ,EAAEA,QAAQ,CAACY,GAAG,CAAEX,GAAG,IAAKA,GAAG,CAACb,MAAM,CAAC,CAAC;UAC5CoB;QACV,CAAS,CAAC;MACV,CAAO,MAAM,IAAI7B,IAAI,CAACU,KAAK,EAAE;QACrBM,GAAG,CAACgB,GAAG,CAACF,QAAQ,EAAE;UAChBT,QAAQ,EAAE,EAAE;UACZrB,IAAI,EAAE,IAAI;UACV6B;QACV,CAAS,CAAC;MACV;IACA,CAAK,EAAE1B,kBAAkB,CAACO,KAAK,EAAER,oBAAoB,CAACQ,KAAK,EAAEV,IAAI,CAACU,KAAK,CAAC;IACpE,OAAOM,GAAG;EACd,CAAG;EACD,MAAMkB,cAAc,GAAGA,CAACC,qBAAqB,GAAG,KAAK,EAAEC,WAAW,KAAK;IACrE,IAAIC,EAAE,EAAEC,EAAE;IACVF,WAAW,KAAKA,WAAW,GAAG,CAACC,EAAE,GAAGhC,QAAQ,CAACkC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,EAAE,CAACG,MAAM,CAACC,gBAAgB,CAAC/B,KAAK,CAAC;IACxG,MAAMgC,MAAM,GAAGnC,cAAc,CAACG,KAAK;IACnC,MAAMiC,mBAAmB,GAAG9B,kBAAkB,CAACH,KAAK;IACpD,MAAMkC,WAAW,GAAG,EAAE;IACtB,IAAIF,MAAM,YAAYhB,GAAG,IAAIgB,MAAM,CAACG,IAAI,EAAE;MACxC,MAAMC,WAAW,GAAGC,KAAK,CAACjD,QAAQ,CAAC;MACnC,MAAMkD,eAAe,GAAG,EAAE;MAC1B,MAAMC,WAAW,GAAGA,CAACC,QAAQ,EAAE/B,GAAG,KAAK;QACrC,IAAIgB,qBAAqB,EAAE;UACzB,IAAIvC,aAAa,CAACc,KAAK,EAAE;YACvB,OAAO0B,WAAW,IAAIxC,aAAa,CAACc,KAAK,CAACyC,QAAQ,CAAChC,GAAG,CAAC;UACnE,CAAW,MAAM;YACL,OAAO,CAAC,EAAEiB,WAAW,KAAKc,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACE,QAAQ,CAAC,CAAC;UACrF;QACA,CAAS,MAAM;UACL,MAAMC,QAAQ,GAAGjB,WAAW,IAAIxC,aAAa,CAACc,KAAK,IAAId,aAAa,CAACc,KAAK,CAACyC,QAAQ,CAAChC,GAAG,CAAC;UACxF,OAAO,CAAC,EAAE,CAAC+B,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACE,QAAQ,KAAKC,QAAQ,CAAC;QAChF;MACA,CAAO;MACDX,MAAM,CAACxB,OAAO,CAAC,CAACoC,CAAC,EAAEnC,GAAG,KAAK;QACzB,MAAM+B,QAAQ,GAAGJ,WAAW,CAAC3B,GAAG,CAAC;QACjC,MAAMoC,QAAQ,GAAG;UAAE,GAAGb,MAAM,CAACc,GAAG,CAACrC,GAAG;QAAC,CAAE;QACvCoC,QAAQ,CAACH,QAAQ,GAAGH,WAAW,CAACC,QAAQ,EAAE/B,GAAG,CAAC;QAC9C,IAAIoC,QAAQ,CAACvD,IAAI,EAAE;UACjB,MAAM;YAAEyD,MAAM,GAAG,KAAK;YAAEC,OAAO,GAAG;UAAK,CAAE,GAAGR,QAAQ,IAAI,EAAE;UAC1DK,QAAQ,CAACE,MAAM,GAAG,CAAC,CAACA,MAAM;UAC1BF,QAAQ,CAACG,OAAO,GAAG,CAAC,CAACA,OAAO;UAC5BV,eAAe,CAACvB,IAAI,CAACN,GAAG,CAAC;QACnC;QACQyB,WAAW,CAACzB,GAAG,CAAC,GAAGoC,QAAQ;MACnC,CAAO,CAAC;MACF,MAAMI,QAAQ,GAAG5C,MAAM,CAACD,IAAI,CAAC6B,mBAAmB,CAAC;MACjD,IAAI3C,IAAI,CAACU,KAAK,IAAIiD,QAAQ,CAAC1C,MAAM,IAAI+B,eAAe,CAAC/B,MAAM,EAAE;QAC3D0C,QAAQ,CAACzC,OAAO,CAAEC,GAAG,IAAK;UACxB,IAAIyC,GAAG;UACP,MAAMV,QAAQ,GAAGJ,WAAW,CAAC3B,GAAG,CAAC;UACjC,MAAM0C,gBAAgB,GAAGlB,mBAAmB,CAACxB,GAAG,CAAC,CAACE,QAAQ;UAC1D,IAAI2B,eAAe,CAACG,QAAQ,CAAChC,GAAG,CAAC,EAAE;YACjC,IAAI,CAAC,CAACyC,GAAG,GAAGhB,WAAW,CAACzB,GAAG,CAAC,CAACE,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGuC,GAAG,CAAC3C,MAAM,MAAM,CAAC,EAAE;cAC3E,MAAM,IAAI6C,KAAK,CAAC,2CAA2C,CAAC;YAC1E;YACYlB,WAAW,CAACzB,GAAG,CAAC,CAACE,QAAQ,GAAGwC,gBAAgB;UACxD,CAAW,MAAM;YACL,MAAM;cAAEJ,MAAM,GAAG,KAAK;cAAEC,OAAO,GAAG;YAAK,CAAE,GAAGR,QAAQ,IAAI,EAAE;YAC1DN,WAAW,CAACzB,GAAG,CAAC,GAAG;cACjBnB,IAAI,EAAE,IAAI;cACVyD,MAAM,EAAE,CAAC,CAACA,MAAM;cAChBC,OAAO,EAAE,CAAC,CAACA,OAAO;cAClBN,QAAQ,EAAEH,WAAW,CAACC,QAAQ,EAAE/B,GAAG,CAAC;cACpCE,QAAQ,EAAEwC,gBAAgB;cAC1BhC,KAAK,EAAE,KAAK;YAC1B,CAAa;UACb;QACA,CAAS,CAAC;MACV;IACA;IACI/B,QAAQ,CAACY,KAAK,GAAGkC,WAAW;IAC5B,CAACN,EAAE,GAAGjC,QAAQ,CAACkC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,EAAE,CAACyB,kBAAkB,EAAE;EACpE,CAAG;EACDC,KAAK,CAAC,MAAMpE,aAAa,CAACc,KAAK,EAAE,MAAM;IACrCwB,cAAc,CAAC,IAAI,CAAC;EACxB,CAAG,CAAC;EACF8B,KAAK,CAAC,MAAMzD,cAAc,CAACG,KAAK,EAAE,MAAM;IACtCwB,cAAc,EAAE;EACpB,CAAG,CAAC;EACF8B,KAAK,CAAC,MAAMnD,kBAAkB,CAACH,KAAK,EAAE,MAAM;IAC1CwB,cAAc,EAAE;EACpB,CAAG,CAAC;EACF,MAAM+B,oBAAoB,GAAIvD,KAAK,IAAK;IACtCd,aAAa,CAACc,KAAK,GAAGA,KAAK;IAC3BwB,cAAc,EAAE;EACpB,CAAG;EACD,MAAMgC,SAAS,GAAIvD,IAAI,IAAK;IAC1B,OAAOX,IAAI,CAACU,KAAK,IAAIC,IAAI,IAAI,QAAQ,IAAIA,IAAI,IAAI,CAACA,IAAI,CAAC8C,MAAM;EACjE,CAAG;EACD,MAAMU,mBAAmB,GAAGA,CAAC7C,GAAG,EAAE8B,QAAQ,KAAK;IAC7C/C,QAAQ,CAACkC,KAAK,CAAC6B,YAAY,EAAE;IAC7B,MAAM3D,MAAM,GAAGd,WAAW,CAACc,MAAM,CAACC,KAAK;IACvC,MAAM2D,EAAE,GAAG7C,cAAc,CAACF,GAAG,EAAEb,MAAM,CAAC;IACtC,MAAME,IAAI,GAAG0D,EAAE,IAAIvE,QAAQ,CAACY,KAAK,CAAC2D,EAAE,CAAC;IACrC,IAAIA,EAAE,IAAI1D,IAAI,IAAI,UAAU,IAAIA,IAAI,EAAE;MACpC,MAAM2D,WAAW,GAAG3D,IAAI,CAACyC,QAAQ;MACjCA,QAAQ,GAAGmB,WAAW,CAACnB,QAAQ,CAAC,GAAG,CAACzC,IAAI,CAACyC,QAAQ,GAAGA,QAAQ;MAC5DtD,QAAQ,CAACY,KAAK,CAAC2D,EAAE,CAAC,CAACjB,QAAQ,GAAGA,QAAQ;MACtC,IAAIkB,WAAW,KAAKlB,QAAQ,EAAE;QAC5B/C,QAAQ,CAACmE,IAAI,CAAC,eAAe,EAAElD,GAAG,EAAE8B,QAAQ,CAAC;MACrD;MACMc,SAAS,CAACvD,IAAI,CAAC,IAAI8D,QAAQ,CAACnD,GAAG,EAAE+C,EAAE,EAAE1D,IAAI,CAAC;MAC1CN,QAAQ,CAACkC,KAAK,CAACwB,kBAAkB,EAAE;IACzC;EACA,CAAG;EACD,MAAMW,YAAY,GAAIpD,GAAG,IAAK;IAC5BjB,QAAQ,CAACkC,KAAK,CAAC6B,YAAY,EAAE;IAC7B,MAAM3D,MAAM,GAAGd,WAAW,CAACc,MAAM,CAACC,KAAK;IACvC,MAAM2D,EAAE,GAAG7C,cAAc,CAACF,GAAG,EAAEb,MAAM,CAAC;IACtC,MAAME,IAAI,GAAGb,QAAQ,CAACY,KAAK,CAAC2D,EAAE,CAAC;IAC/B,IAAIH,SAAS,CAACvD,IAAI,CAAC,EAAE;MACnB8D,QAAQ,CAACnD,GAAG,EAAE+C,EAAE,EAAE1D,IAAI,CAAC;IAC7B,CAAK,MAAM;MACLwD,mBAAmB,CAAC7C,GAAG,EAAE,KAAK,CAAC,CAAC;IACtC;EACA,CAAG;EACD,MAAMmD,QAAQ,GAAGA,CAACnD,GAAG,EAAEH,GAAG,EAAEwD,QAAQ,KAAK;IACvC,MAAM;MAAEC;IAAI,CAAE,GAAGvE,QAAQ,CAACwE,KAAK;IAC/B,IAAID,IAAI,IAAI,CAAC9E,QAAQ,CAACY,KAAK,CAACS,GAAG,CAAC,CAACsC,MAAM,EAAE;MACvC3D,QAAQ,CAACY,KAAK,CAACS,GAAG,CAAC,CAACuC,OAAO,GAAG,IAAI;MAClCkB,IAAI,CAACtD,GAAG,EAAEqD,QAAQ,EAAGhE,IAAI,IAAK;QAC5B,IAAI,CAACoB,OAAO,CAACpB,IAAI,CAAC,EAAE;UAClB,MAAM,IAAImE,SAAS,CAAC,iCAAiC,CAAC;QAChE;QACQhF,QAAQ,CAACY,KAAK,CAACS,GAAG,CAAC,CAACuC,OAAO,GAAG,KAAK;QACnC5D,QAAQ,CAACY,KAAK,CAACS,GAAG,CAAC,CAACsC,MAAM,GAAG,IAAI;QACjC3D,QAAQ,CAACY,KAAK,CAACS,GAAG,CAAC,CAACiC,QAAQ,GAAG,IAAI;QACnC,IAAIzC,IAAI,CAACM,MAAM,EAAE;UACfhB,eAAe,CAACS,KAAK,CAACS,GAAG,CAAC,GAAGR,IAAI;QAC3C;QACQN,QAAQ,CAACmE,IAAI,CAAC,eAAe,EAAElD,GAAG,EAAE,IAAI,CAAC;MACjD,CAAO,CAAC;IACR;EACA,CAAG;EACD,MAAMyD,iBAAiB,GAAGA,CAAC5D,GAAG,EAAER,IAAI,KAAK;IACvC,MAAM;MAAEX,IAAI,EAAEgF,KAAK;MAAEvE;IAAM,CAAE,GAAGJ,QAAQ,CAACwE,KAAK;IAC9C,IAAI,CAACG,KAAK,EACR;IACF,IAAI,CAACvE,MAAM,EACT,MAAM,IAAIqD,KAAK,CAAC,8CAA8C,CAAC;IACjE,IAAI7D,eAAe,CAACS,KAAK,CAACS,GAAG,CAAC,EAAE;MAC9BlB,eAAe,CAACS,KAAK,CAACS,GAAG,CAAC,GAAGR,IAAI;IACvC;EACA,CAAG;EACD,OAAO;IACL8D,QAAQ;IACRC,YAAY;IACZP,mBAAmB;IACnBF,oBAAoB;IACpB/B,cAAc;IACd6C,iBAAiB;IACjBnE,SAAS;IACT4B,MAAM,EAAE;MACN5C,aAAa;MACbE,QAAQ;MACRC,MAAM;MACNC,IAAI;MACJC,eAAe;MACfC,oBAAoB;MACpBC,kBAAkB;MAClBC;IACN;EACA,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}