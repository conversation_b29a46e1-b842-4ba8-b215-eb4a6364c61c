<?php

/**
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

/*
 * Authors: <AUTHORS>
 */
return array_replace_recursive(require __DIR__.'/en.php', [
    'formats' => [
        'L' => 'DD. MM. YY',
    ],
    'months' => ['Ghenn<PERSON>rg<PERSON>', 'Freàrgiu', '<PERSON><PERSON>', '<PERSON><PERSON>le', 'Maju', 'Làmpadas', 'Argiolas//Trìulas', 'Austu', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>//Ladàmine', 'Onniasantu//Santandria', 'Nadale//Idas'],
    'months_short' => ['Ghe', 'Fre', 'Mar', 'Abr', 'Maj', 'Làm', 'Arg', 'Aus', '<PERSON><PERSON>', 'Lad', 'Onn', 'Nad'],
    'weekdays' => ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'],
    'weekdays_short' => ['<PERSON>', 'Lun', '<PERSON>', '<PERSON>èr', 'Giò', 'Che', 'Sàb'],
    'weekdays_min' => ['Dom', 'Lun', 'Mar', '<PERSON>èr', 'Giò', 'Che', 'Sàb'],
    'first_day_of_week' => 1,
    'day_of_first_week_of_year' => 4,

    'minute' => ':count mementu', // less reliable
    'min' => ':count mementu', // less reliable
    'a_minute' => ':count mementu', // less reliable

    'year' => ':count annu',
    'y' => ':count annu',
    'a_year' => ':count annu',

    'month' => ':count mese',
    'm' => ':count mese',
    'a_month' => ':count mese',

    'week' => ':count chida',
    'w' => ':count chida',
    'a_week' => ':count chida',

    'day' => ':count dí',
    'd' => ':count dí',
    'a_day' => ':count dí',

    'hour' => ':count ora',
    'h' => ':count ora',
    'a_hour' => ':count ora',

    'second' => ':count secundu',
    's' => ':count secundu',
    'a_second' => ':count secundu',
]);
