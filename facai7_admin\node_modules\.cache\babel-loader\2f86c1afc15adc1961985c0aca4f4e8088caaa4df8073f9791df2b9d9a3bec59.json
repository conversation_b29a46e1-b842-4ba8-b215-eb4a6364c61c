{"ast": null, "code": "import { rolesEnums } from \"@/config/enums\";\nimport { htmlDecodeByRegExp } from '@/utils/utils';\nimport { onBeforeUnmount, nextTick, ref, shallowRef, onMounted, getCurrentInstance } from \"vue\";\nimport { getTokenAUTH } from \"@/utils/auth\";\nexport default {\n  __name: 'editPop',\n  props: [\"item\"],\n  setup(__props, {\n    expose: __expose\n  }) {\n    const form = ref({\n      title: \"\",\n      price: \"\",\n      class_id: \"\",\n      sort: \"\",\n      content: \"\",\n      img: \"\"\n    });\n    const props = __props;\n    const headers = ref({});\n    onMounted(() => {\n      headers.value['Accept-Token'] = getTokenAUTH();\n      nextTick(() => {\n        props.item.content = htmlDecodeByRegExp(props.item.content);\n        form.value = Object.assign(form.value, props.item);\n      });\n      getTYpesEnum();\n    });\n    const {\n      proxy\n    } = getCurrentInstance();\n    const typesEnum = ref([]);\n    const getTYpesEnum = async () => {\n      const res = await proxy.$http({\n        method: 'get',\n        url: '/Goods/getGoodsClassLists'\n      });\n      if (res.code == 0) {\n        typesEnum.value = res.data.data;\n      }\n    };\n    const successUpload = res => {\n      form.value.img = res.data.url;\n    };\n    const handleErr = err => {\n      if (err.status == 320) {\n        form.value.img = JSON.parse(err.message).data.url;\n      }\n    };\n\n    // 编辑器实例，必须用 shallowRef\n    const editorRef = shallowRef();\n\n    // 内容 HTML\n    const valueHtml = ref(\"<p>hello</p>\");\n    const mode = ref(\"default\");\n    const toolbarConfig = {};\n    const editorConfig = {\n      placeholder: \"请输入内容...\",\n      MENU_CONF: {\n        uploadImage: {\n          fieldName: \"file\",\n          maxFileSize: 10 * 1024 * 1024,\n          // 10M\n          server: proxy.BASE_API_URL + \"index/uploadX\",\n          headers: {\n            \"Accept-Token\": getTokenAUTH()\n          },\n          customInsert(res, insertFn) {\n            const url = proxy.IMG_BASE_URL + res.data.url;\n            const alt = res.data.alt;\n            const href = res.data.href;\n            insertFn(url, alt, href);\n          }\n        }\n      }\n    };\n\n    // 组件销毁时，也及时销毁编辑器\n    onBeforeUnmount(() => {\n      const editor = editorRef.value;\n      if (editor == null) return;\n      editor.destroy();\n    });\n    const handleCreated = editor => {\n      editorRef.value = editor; // 记录 editor 实例，重要！\n    };\n    __expose({\n      form\n    });\n    const __returned__ = {\n      form,\n      props,\n      headers,\n      proxy,\n      typesEnum,\n      getTYpesEnum,\n      successUpload,\n      handleErr,\n      editorRef,\n      valueHtml,\n      mode,\n      toolbarConfig,\n      editorConfig,\n      handleCreated,\n      get rolesEnums() {\n        return rolesEnums;\n      },\n      get htmlDecodeByRegExp() {\n        return htmlDecodeByRegExp;\n      },\n      onBeforeUnmount,\n      nextTick,\n      ref,\n      shallowRef,\n      onMounted,\n      getCurrentInstance,\n      get getTokenAUTH() {\n        return getTokenAUTH;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["rolesEnums", "htmlDecodeByRegExp", "onBeforeUnmount", "nextTick", "ref", "shallowRef", "onMounted", "getCurrentInstance", "getTokenAUTH", "form", "title", "price", "class_id", "sort", "content", "img", "props", "__props", "headers", "value", "item", "Object", "assign", "getTYpesEnum", "proxy", "typesEnum", "res", "$http", "method", "url", "code", "data", "successUpload", "handleErr", "err", "status", "JSON", "parse", "message", "editor<PERSON><PERSON>", "valueHtml", "mode", "toolbarConfig", "editorConfig", "placeholder", "MENU_CONF", "uploadImage", "fieldName", "maxFileSize", "server", "BASE_API_URL", "customInsert", "insertFn", "IMG_BASE_URL", "alt", "href", "editor", "destroy", "handleCreated", "__expose"], "sources": ["D:/WorkSpace/facai7/facai7_admin/src/views/goodsManage/components/goodsList/editPop.vue"], "sourcesContent": ["<template>\r\n  <el-form\r\n    label-width=\"100px\"\r\n    :inline=\"true\"\r\n    :model=\"form\"\r\n    class=\"demo-form-inline\"\r\n  >\r\n    <el-form-item label=\"标题\" required>\r\n      <el-input v-model=\"form.title\" clearable />\r\n    </el-form-item>\r\n\r\n    <el-form-item\r\n        label=\"项目分类\"\r\n        prop=\"class_id\"\r\n      >\r\n        <el-select v-model=\"form.class_id\" placeholder=\"项目分类\" clearable>\r\n          <el-option\r\n            v-for=\"item in typesEnum\"\r\n            :label=\"item.title\"\r\n            :key=\"item.title\"\r\n            :value=\"item.id\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n    <el-form-item label=\"价格\" required>\r\n      <el-input v-model=\"form.price\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"排序\" required>\r\n      <el-input v-model=\"form.sort\" clearable />\r\n    </el-form-item>\r\n    <el-form-item\r\n      label=\"图片\"\r\n      prop=\"img\"\r\n      :rules=\"[{ required: true, message: '请上传图片' }]\"\r\n    >\r\n      <el-upload\r\n        class=\"upload-demo\"\r\n        style=\"width: 114px\"\r\n        :show-file-list=\"false\"\r\n        drag\r\n        :headers=\"headers\"\r\n        :action=\"`${proxy.BASE_API_URL}index/upload`\"\r\n        :on-success=\"successUpload\"\r\n        :on-error=\"handleErr\"\r\n        :multiple=\"false\"\r\n      >\r\n        <img v-if=\"form.img\" :src=\"proxy.IMG_BASE_URL + form.img\" width=\"100%\" class=\"avatar\" />\r\n        <el-icon v-else class=\"avatar-uploader-icon\"><Plus /></el-icon\r\n      ></el-upload>\r\n    </el-form-item>\r\n    <div style=\"border: 1px solid #ccc\">\r\n      <Toolbar\r\n        style=\"border-bottom: 1px solid #ccc\"\r\n        :editor=\"editorRef\"\r\n        :defaultConfig=\"toolbarConfig\"\r\n        :mode=\"mode\"\r\n      />\r\n      <Editor\r\n        style=\"height: 500px; overflow-y: hidden\"\r\n        v-model=\"form.content\"\r\n        :defaultConfig=\"editorConfig\"\r\n        :mode=\"mode\"\r\n        @onCreated=\"handleCreated\"\r\n      />\r\n    </div>\r\n  </el-form>\r\n</template>\r\n\r\n<script setup>\r\nimport { rolesEnums } from \"@/config/enums\";\r\nimport { htmlDecodeByRegExp } from '@/utils/utils'\r\nimport { onBeforeUnmount, nextTick, ref, shallowRef, onMounted, getCurrentInstance } from \"vue\";\r\nimport { getTokenAUTH } from \"@/utils/auth\";\r\n\r\nconst form = ref({\r\n  title: \"\",\r\n  price: \"\",\r\n  class_id: \"\",\r\n  sort: \"\",\r\n  content: \"\",\r\n  img: \"\",\r\n});\r\nconst props = defineProps([\"item\"]);\r\n\r\nconst headers = ref({})\r\nonMounted(() => {\r\n  headers.value['Accept-Token'] = getTokenAUTH()\r\n  nextTick(() => {\r\n    props.item.content = htmlDecodeByRegExp(props.item.content)\r\n    form.value = Object.assign(form.value, props.item);\r\n  });\r\n  getTYpesEnum()\r\n});\r\n\r\nconst { proxy } = getCurrentInstance()\r\n\r\nconst typesEnum = ref([])\r\n\r\nconst getTYpesEnum = async () => {\r\n    const res = await proxy.$http({\r\n        method: 'get',\r\n        url: '/Goods/getGoodsClassLists'\r\n    })\r\n    if (res.code == 0) {\r\n        typesEnum.value = res.data.data\r\n    }\r\n}\r\n\r\n\r\nconst successUpload = (res) => {\r\n  form.value.img = res.data.url;\r\n};\r\n\r\nconst handleErr = (err) => {\r\n  if (err.status == 320) {\r\n    form.value.img = JSON.parse(err.message).data.url;\r\n  }\r\n}\r\n\r\n// 编辑器实例，必须用 shallowRef\r\nconst editorRef = shallowRef();\r\n\r\n// 内容 HTML\r\nconst valueHtml = ref(\"<p>hello</p>\");\r\nconst mode = ref(\"default\");\r\n\r\nconst toolbarConfig = {};\r\nconst editorConfig =  {\r\n  placeholder: \"请输入内容...\",\r\n  MENU_CONF: {\r\n    uploadImage: {\r\n      fieldName: \"file\",\r\n      maxFileSize: 10 * 1024 * 1024, // 10M\r\n      server: proxy.BASE_API_URL + \"index/uploadX\",\r\n      headers: {\r\n        \"Accept-Token\": getTokenAUTH(),\r\n      },\r\n      customInsert(res, insertFn) {\r\n        const url = proxy.IMG_BASE_URL + res.data.url;\r\n        const alt = res.data.alt\r\n        const href = res.data.href\r\n        insertFn(url, alt, href);\r\n      },\r\n    },\r\n  },\r\n};\r\n\r\n// 组件销毁时，也及时销毁编辑器\r\nonBeforeUnmount(() => {\r\n  const editor = editorRef.value;\r\n  if (editor == null) return;\r\n  editor.destroy();\r\n});\r\n\r\nconst handleCreated = (editor) => {\r\n  editorRef.value = editor; // 记录 editor 实例，重要！\r\n};\r\n\r\ndefineExpose({ form });\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.demo-form-inline {\r\n  justify-content: flex-start;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.demo-form-inline .el-input {\r\n  --el-input-width: 220px;\r\n}\r\n\r\n.demo-form-inline .el-select {\r\n  --el-select-width: 220px;\r\n}\r\n\r\n/deep/ .el-radio-group {\r\n  width: 220px;\r\n}\r\n\r\n.form-title {\r\n  text-align: left;\r\n  padding-left: 30px;\r\n  margin: 20px auto 10px;\r\n  height: 44px;\r\n  background-color: #f2f2f2;\r\n  border-radius: 5px;\r\n  line-height: 44px;\r\n}\r\n\r\n/deep/ .el-form-item {\r\n  align-items: flex-start;\r\n}\r\n</style>\r\n"], "mappings": "AAqEA,SAASA,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,kBAAkB,QAAQ,eAAe;AAClD,SAASC,eAAe,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAEC,kBAAkB,QAAQ,KAAK;AAC/F,SAASC,YAAY,QAAQ,cAAc;;;;;;;IAE3C,MAAMC,IAAI,GAAGL,GAAG,CAAC;MACfM,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,IAAI,EAAE,EAAE;MACRC,OAAO,EAAE,EAAE;MACXC,GAAG,EAAE;IACP,CAAC,CAAC;IACF,MAAMC,KAAK,GAAGC,OAAqB;IAEnC,MAAMC,OAAO,GAAGd,GAAG,CAAC,CAAC,CAAC,CAAC;IACvBE,SAAS,CAAC,MAAM;MACdY,OAAO,CAACC,KAAK,CAAC,cAAc,CAAC,GAAGX,YAAY,CAAC,CAAC;MAC9CL,QAAQ,CAAC,MAAM;QACba,KAAK,CAACI,IAAI,CAACN,OAAO,GAAGb,kBAAkB,CAACe,KAAK,CAACI,IAAI,CAACN,OAAO,CAAC;QAC3DL,IAAI,CAACU,KAAK,GAAGE,MAAM,CAACC,MAAM,CAACb,IAAI,CAACU,KAAK,EAAEH,KAAK,CAACI,IAAI,CAAC;MACpD,CAAC,CAAC;MACFG,YAAY,CAAC,CAAC;IAChB,CAAC,CAAC;IAEF,MAAM;MAAEC;IAAM,CAAC,GAAGjB,kBAAkB,CAAC,CAAC;IAEtC,MAAMkB,SAAS,GAAGrB,GAAG,CAAC,EAAE,CAAC;IAEzB,MAAMmB,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC7B,MAAMG,GAAG,GAAG,MAAMF,KAAK,CAACG,KAAK,CAAC;QAC1BC,MAAM,EAAE,KAAK;QACbC,GAAG,EAAE;MACT,CAAC,CAAC;MACF,IAAIH,GAAG,CAACI,IAAI,IAAI,CAAC,EAAE;QACfL,SAAS,CAACN,KAAK,GAAGO,GAAG,CAACK,IAAI,CAACA,IAAI;MACnC;IACJ,CAAC;IAGD,MAAMC,aAAa,GAAIN,GAAG,IAAK;MAC7BjB,IAAI,CAACU,KAAK,CAACJ,GAAG,GAAGW,GAAG,CAACK,IAAI,CAACF,GAAG;IAC/B,CAAC;IAED,MAAMI,SAAS,GAAIC,GAAG,IAAK;MACzB,IAAIA,GAAG,CAACC,MAAM,IAAI,GAAG,EAAE;QACrB1B,IAAI,CAACU,KAAK,CAACJ,GAAG,GAAGqB,IAAI,CAACC,KAAK,CAACH,GAAG,CAACI,OAAO,CAAC,CAACP,IAAI,CAACF,GAAG;MACnD;IACF,CAAC;;IAED;IACA,MAAMU,SAAS,GAAGlC,UAAU,CAAC,CAAC;;IAE9B;IACA,MAAMmC,SAAS,GAAGpC,GAAG,CAAC,cAAc,CAAC;IACrC,MAAMqC,IAAI,GAAGrC,GAAG,CAAC,SAAS,CAAC;IAE3B,MAAMsC,aAAa,GAAG,CAAC,CAAC;IACxB,MAAMC,YAAY,GAAI;MACpBC,WAAW,EAAE,UAAU;MACvBC,SAAS,EAAE;QACTC,WAAW,EAAE;UACXC,SAAS,EAAE,MAAM;UACjBC,WAAW,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;UAAE;UAC/BC,MAAM,EAAEzB,KAAK,CAAC0B,YAAY,GAAG,eAAe;UAC5ChC,OAAO,EAAE;YACP,cAAc,EAAEV,YAAY,CAAC;UAC/B,CAAC;UACD2C,YAAYA,CAACzB,GAAG,EAAE0B,QAAQ,EAAE;YAC1B,MAAMvB,GAAG,GAAGL,KAAK,CAAC6B,YAAY,GAAG3B,GAAG,CAACK,IAAI,CAACF,GAAG;YAC7C,MAAMyB,GAAG,GAAG5B,GAAG,CAACK,IAAI,CAACuB,GAAG;YACxB,MAAMC,IAAI,GAAG7B,GAAG,CAACK,IAAI,CAACwB,IAAI;YAC1BH,QAAQ,CAACvB,GAAG,EAAEyB,GAAG,EAAEC,IAAI,CAAC;UAC1B;QACF;MACF;IACF,CAAC;;IAED;IACArD,eAAe,CAAC,MAAM;MACpB,MAAMsD,MAAM,GAAGjB,SAAS,CAACpB,KAAK;MAC9B,IAAIqC,MAAM,IAAI,IAAI,EAAE;MACpBA,MAAM,CAACC,OAAO,CAAC,CAAC;IAClB,CAAC,CAAC;IAEF,MAAMC,aAAa,GAAIF,MAAM,IAAK;MAChCjB,SAAS,CAACpB,KAAK,GAAGqC,MAAM,CAAC,CAAC;IAC5B,CAAC;IAEDG,QAAY,CAAC;MAAElD;IAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}