<?php
namespace app\admin\service;

use app\admin\service\util\RelationTrait;
use app\common\cache\OnlineUser;
use app\common\jobs\UpdateUserStateJob;
use app\common\model\MoneyClass;
use app\common\repository\ItemOrderRepository;
use app\common\repository\LevelLogRepository;
use app\common\repository\LevelRepository;
use app\common\repository\MoneyLogRepository;
use app\common\repository\MoneyRepository;
use app\common\repository\TeamRepository;
use app\common\repository\UserInfoRepository;
use app\common\repository\UserLoginRepository;
use app\common\repository\UserRelationRepository;
use app\common\repository\UserRepository;
use app\common\repository\UserStateRepository;
use app\common\utils\Arrays;
use app\common\utils\IpAddress;
use app\common\utils\Plan;
use app\common\utils\Record;
use app\common\utils\Result;
use think\facade\Db;
use think\facade\Request;

/**
 * 用户模块
 */
class UserService
{

    use RelationTrait;

    /**
     * 用户列表
     * @param $params
     * @return array
     */
    public function getUserLists($params): array
    {
        $UserRepo           = new UserRepository();
        $OnlineUser         = new OnlineUser();
        $UserRelationRepo   = new UserRelationRepository();

        $where      = [];
        $order      = [];

        if (!empty($params['starttime']))
        {
            $where[] = ['ur.create_time', '>=', strtotime($params['starttime'])];
        }

        if (!empty($params['endtime']))
        {
            $where[] = ['ur.create_time', '<', strtotime($params['endtime'])];
        }

        if (!empty($params['username']))
        {
            $where[] = ['ur.username', '=', $params['username']];
        }

        if (isset($params['is_test']) && is_numeric($params['is_test']))
        {
            $where[] = ['ur.is_test', '=', $params['is_test']];
        }

        if (!empty($params['phone']))
        {
            $where[] = ['ur.phone', 'like',  "%{$params['phone']}%"];
        }

        if (!empty($params['invite']))
        {
            $where[] = ['ur.invite', '=', $params['invite']];
        }

        if (isset($params['level']) && is_numeric($params['level']))
        {
            $where[] = ['ur.level', '=', $params['level']];
        }

        if (isset($params['sfz_status']) && is_numeric($params['sfz_status']))
        {
            $where[] = ['st.sfz_status', '=', $params['sfz_status']];
        }

        if (isset($params['level_team']) && is_numeric($params['level_team']))
        {
            $where[] = ['ur.level_team', '=', $params['level_team']];
        }

        if (!empty($params['sfz_name']))
        {
            $where[] = ['ur.sfz_name', '=', $params['sfz_name']];
        }


        if (!empty($params['online_time']))
        {
            $where[] = ['ur.online_time', '>=', strtotime(' -3 day')];
        }

        if (!empty($params['is_online']))
        {
            $data       = $OnlineUser->list(0,100000);
            $online     = empty($data) ? [] :$data;

            $where[]    = ['ur.id', 'in', $online];
        }

        if (isset($params['is_signin']) && is_numeric($params['is_signin']))
        {
            $where[] = ['ur.signin_time', '>=', strtotime(date('Y-m-d 00:00:00'))];
        }


        if (isset($params['sort_point']) && is_numeric($params['sort_point']))
        {
            $order['fo.user_points'] = $params['sort_point'] == 0 ? 'asc' : 'desc';
        }

        if (isset($params['sort_yuebao']) && is_numeric($params['sort_yuebao']))
        {
            $order['fo.yuebao_money'] = $params['sort_yuebao'] == 0 ? 'asc' : 'desc';
        }

        if (isset($params['sort_money']) && is_numeric($params['sort_money']))
        {
            $order['mo.money'] = $params['sort_money'] == 0 ? 'asc' : 'desc';
        }

        if (isset($params['sort_frozen_money']) && is_numeric($params['sort_frozen_money']))
        {
            $order['mo.frozen_money'] = $params['sort_frozen_money'] == 0 ? 'asc' : 'desc';
        }

        if (isset($params['sort_coupon_num']) && is_numeric($params['sort_coupon_num']))
        {
            $order['fo.coupon_num'] = $params['sort_coupon_num'] == 0 ? 'asc' : 'desc';
        }

        if (isset($params['blow']) && is_numeric($params['blow']))
        {

            $id               = $UserRepo->valueByCondition(['phone' => $params['phone']],'id');


            $where2            = [];
            $where2[]          = ['top_id', '=', $id];
            $where2[]          = ['level', '=', $params['blow']];

            $UserRelationRepo = new UserRelationRepository();
            $members          = $UserRelationRepo->selectByCondition($where2,'uid');
            $members          = array_column($members, 'uid');
            $where            = [];
            $where[]          = ['id', 'in', $members];

        }

        if (empty($order))
        {
            $order = ['ur.id' => 'desc',];
        }

        // 加上一天的秒数，即可获得今日截止时间的时间戳
        $data = $UserRepo->alias('ur')->join('user_info fo', 'ur.id = fo.uid')
            ->join('user_state st', 'ur.id = st.uid')
            ->join('money mo', 'ur.id = mo.uid')
            ->where($where)
            ->order($order)
            ->paginate($params['limit'])->toArray();

        foreach ($data['data'] as &$user)
        {
            $user['online']            = $OnlineUser->isOnline($user['id']);
            $user['login_time']        = date('Y-m-d H:i:s', $user['login_time']);
            $user['item_not_finish']   = 0;

            for ($i = 1; $i <= 5; $i++)
            {
                $where    = [];
                $where[]  = ['top_id', '=', $user['id']];
                $where[]  = ['level', '=', $i];

                $user['team'.$i]            = $UserRelationRepo->countByCondition($where);
                $user['item_not_finish']    = 0;
            }
        }

        return Result::success($data);
    }


    /**
     * 用户信息
     * @param $id
     * @return array
     */
    public function getUserInfo($id): array
    {
        $UserRepo      = new UserRepository();
        $user          = $UserRepo->findById($id);

        $UserStateRepo = new UserStateRepository();
        $state         = $UserStateRepo->findByCondition(['uid' => $user['id']]);

        $UserInfoRepo  = new UserInfoRepository();
        $info          = $UserInfoRepo->findByCondition(['uid' => $user['id']]);

        $data          = array_merge($user,$state,$info);

        return Result::success($data);
    }

    /**
     * 用户信息
     * @param $id
     * @return array
     */
    public function getUser($id): array
    {
        $UserRepo      = new UserRepository();
        $user          = $UserRepo->findById($id);

        return Result::success($user);
    }


    /**
     * 用户统计信息
     * @param $id
     * @return array
     */
    public function getUserInfos($id): array
    {
        $UserInfoRepo  = new UserInfoRepository();
        $data          = $UserInfoRepo->findByCondition(['uid' => $id]);

        return Result::success($data);
    }

    /**
     * 用户状态信息
     * @param $id
     * @return array
     */
    public function getUserState($id): array
    {
        $UserStateRepo = new UserStateRepository();
        $data          = $UserStateRepo->findByCondition(['uid' => $id]);

        return Result::success($data);
    }

    /**
     * 更新用户状态
     * @param $params
     * @return array
     */
    public function updateUserInfo($params): array
    {
        $params['update_time']  = time();
        $UserInfoRepo           = new UserInfoRepository();
        $res                    = $UserInfoRepo->updateById($params['id'], Arrays::withOut($params,['id','username']));

        if (!$res)
        {
            return Result::fail('保存失败');
        }

        return Result::success();
    }

    /**
     * 更新用户状态
     * @param $params
     * @return array
     */
    public function updateUserState($params): array
    {

        $update = [
            'ban_buy'           => $params['ban_buy'],
            'ban_sigin'         => $params['ban_sigin'],
            'ban_raffle'        => $params['ban_raffle'],
            'ban_login'         => $params['ban_login'],
            'ban_invite'        => $params['ban_invite'],
            'ban_recharge'      => $params['ban_recharge'],
            'ban_withdraw'      => $params['ban_withdraw'],
            'ban_exchange'      => $params['ban_exchange'],
            'sfz_status'        => $params['sfz_status'],
            'kick_out'          => $params['kick_out'],
            'is_test'           => $params['is_test'],
            'is_valid_user'     => $params['is_valid_user'],
            'update_time'       => Request::time(),
        ];

        $UserStateRepo = new UserStateRepository();
        $res           = $UserStateRepo->updateByCondition(['uid' => $params['uid']], $update);

        if (!$res)
        {
            return Result::fail('保存失败');
        }

        $UserRepo = new UserRepository();
        $res      = $UserRepo->updateByCondition(['id' => $params['uid']], ['is_test' => $params['is_test'],'update_time' => time()]);

        if (!$res)
        {
            return Result::fail('保存失败');
        }

        if ($params['sfz_status'] == 0)
        {
            $Plan = new Plan();
            $res  = $Plan->gift($params['uid']);

            if ($res['code'])
            {
                return  Result::fail('福利等级修改失败');
            }
        }


        queue(UpdateUserStateJob::class,['uid' => $params['uid']]);

        return Result::success();
    }

    /**
     * 添加用户
     * @param $username
     * @param $password
     * @param $phone
     * @return array
     */
    public function addUser($username, $password, $phone): array
    {
        $UserRepo  = new UserRepository();


        Db::startTrans();

        try {

            $LevelRepo = new LevelRepository();
            $level     = $LevelRepo->findByCondition([],'id,title', ['id' => 'asc']);

            $insert = [
                'username'      => $username,
                'nickname'      => $username,
                'phone'         => $phone,
                'invite'        => $UserRepo->createCode(),
                'level'         => $level['id'] ?? 0,
                'level_name'    => $level['title'] ?? '',
                'password'      => $password,
                'pin'           => 123456,
                'register_ip'   => IpAddress::realIP(),
                'create_time'   => Request::time(),
                'update_time'   => Request::time(),
            ];

            $uid      = $UserRepo->insertsGetId($insert);

            if (!$uid)
            {
                Db::rollback();
                return Result::fail('保存失败');
            }

            $insert = [
                'uid'           =>  $uid,
                'create_time'   => Request::time(),
                'update_time'   => Request::time(),
            ];

            $UserInfoRepo = new UserInfoRepository();
            $res          = $UserInfoRepo->inserts($insert);

            if (!$res)
            {
                Db::rollback();
                return Result::fail('保存失败');
            }

            $UserStateRepo = new UserStateRepository();

            $res =  $UserStateRepo->inserts($insert);

            if (!$res)
            {
                Db::rollback();
                return Result::fail('保存失败');
            }

            $MoneyRepo = new MoneyRepository();

            $res = $MoneyRepo->inserts($insert);

            if (!$res)
            {
                Db::rollback();
                return Result::fail('保存失败');
            }

            // 提交事务
            Db::commit();

            //queue(LevelUpJob::class,['uid'=> $uid]);

        } catch (\Exception $exception)
        {

            Record::exception('admin', $exception,'LoginService->register');

            Db::rollback();

            //注册失败
            return Result::fail('注册失败');
        }

        return Result::success();
    }

    /**
     * 更新用户
     * @param $params
     * @return array
     */
    public function updateUser($params): array
    {
        $UserRepo      = new UserRepository();
        $LevelLogRepo  = new LevelLogRepository();
        $TeamRepo      = new TeamRepository();
        $UserStateRepo = new UserStateRepository();
        $LevelRepo     = new LevelRepository();
        $user          = $UserRepo->findById($params['id']);
        $state         = $UserStateRepo->findById($user['id']);

        if (!$user)
        {
            return Result::fail('用户不存在');
        }


        $update = [
            'nickname'    => $params['nickname'],
            'password'    => $params['password'],
            'pin'         => $params['pin'],
            'email'       => $params['email'],
            'sfz_name'    => $params['sfz_name'],
            'username'    => $params['sfz_name'],
            'sfz_number'  => $params['sfz_number'],
            'avatar'      => $params['avatar'],
            'update_time' => Request::time(),
        ];

        if (isset($params['level']))
        {

            $update['level']       = $params['level'];
            $update['level_name']  = $LevelRepo->valueByCondition(['id' => $params['level']], 'title');

            if ($update['level'] == 0)
            {
                $update['level_name']  = '';
            }

            if ($params['level'] > $user['level'])
            {
                $insert  = [
                    'uid'           => $user['id'],
                    'username'      => $user['username'],
                    'phone'         => $user['phone'],
                    'is_test'       => $state['is_test'],
                    'lv_id'         => $update['level'],
                    'lv_name'       => $update['level_name'],
                    'desc'          => '从' . $user['level_name'] . '升级到' . $update['level_name'],
                    'create_time'   => time(),
                    'update_time'   => time()
                ];

                $res = $LevelLogRepo->inserts($insert);
            }

            if ($params['level'] < $user['level'])
            {

                $insert  = [
                    'uid'           => $user['id'],
                    'username'      => $user['username'],
                    'phone'         => $user['phone'],
                    'is_test'       => $state['is_test'],
                    'lv_id'         => $update['level'],
                    'lv_name'       => $update['level_name'],
                    'desc'          => '从' . $user['level_name'] . '降级到' . $update['level_name'],
                    'create_time'   => time(),
                    'update_time'   => time()
                ];

                $res = $LevelLogRepo->inserts($insert);
            }

            $Plan = new Plan();
            $res  = $Plan->level($user['id']);

            if ($res['code'])
            {
                return  Result::fail('福利等级修改失败');
            }
        }

        if (isset($params['level_team']))
        {
            $update['level_team']       = $params['level_team'];
            $update['level_team_name']  = $TeamRepo->valueByCondition(['id' => $params['level_team']], 'title');

            if ($update['level_team'] == 0)
            {
                $update['level_team_name']  = '';
            }
        }


        $res       = $UserRepo->updateById($params['id'], $update);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    /**
     * 删除用户
     * @param $id
     * @return array
     */
    public function deleteUser($id): array
    {
        $UserRepo  = new UserRepository();

        $res       = $UserRepo->deleteById($id);

        if (!$res)
        {
            return Result::fail();
        }

        return Result::success();
    }


    /**
     * 用户迁移
     * @return void
     */
    public function userRemove($formUser, $toUser, $lv): array
    {
        $UserRepo = new UserRepository();

        $formInfo = $UserRepo->findByCondition(['phone' => $formUser]);

        if (empty($formInfo))
        {
            return Result::fail('用户信息不存在');
        }

        $toInfo   = $UserRepo->findByCondition(['invite' => $toUser]);

        if (empty($toInfo))
        {
            return Result::fail('邀请码不存在');
        }

        if ($formInfo['id'] == $toInfo['id'])
        {
            return Result::fail('不能转移到自己');
        }

        $this->relationshipTransfer($formInfo['phone'], $toInfo['phone']);


        return Result::success();
    }

    /**
     * 登入列表
     * @param $params
     * @return array
     */
    public function getLoginLists($params): array
    {

        $where = [];

        if (!empty($params['starttime']))
        {
            $where[] = ['create_time', '>=', strtotime($params['starttime'])];
        }

        if (!empty($params['endtime']))
        {
            $where[] = ['create_time', '<', strtotime($params['endtime'])];
        }


        if (!empty($params['phone']))
        {
            $where[] = ['phone', '=', $params['phone']];
        }

        $UserLoginRepo = new UserLoginRepository();
        $data          = $UserLoginRepo->paginates($where);
        return Result::success($data);
    }

    /**
     * 团队列表
     * @param $params
     * @return array
     */
    public function getTeamLists($params): array
    {
        $UserRepo           = new UserRepository();
        $OnlineUser         = new OnlineUser();
        $UserRelationRepo   = new UserRelationRepository();

        $id                 = $UserRepo->valueByCondition(['phone' => $params['phone']],'id');
        $condition          = [];
        $condition[]        = ['level', '<=', $params['level']];

        if ($params['blow'])
        {
            $condition[]       = ['uid', '=', $id];
            $members           = $UserRelationRepo->selectByCondition($condition,'top_id');
            $members           = array_column($members, 'top_id');
        }
        else
        {
            $condition[]       = ['top_id', '=', $id];
            $members           = $UserRelationRepo->selectByCondition($condition,'uid');
            $members           = array_column($members, 'uid');
        }


        $where            = [];
        $where[]          = ['id', 'in', $members];

        $order            = ['ur.id' => 'desc',];

        if (!empty($params['starttime']))
        {
            $where[] = ['ur.create_time', '>=', strtotime($params['starttime'])];
        }

        if (!empty($params['endtime']))
        {
            $where[] = ['ur.create_time', '<', strtotime($params['endtime'])];
        }


        // 加上一天的秒数，即可获得今日截止时间的时间戳
        $data = $UserRepo->alias('ur')->join('user_info fo', 'ur.id = fo.uid')
            ->join('user_state st', 'ur.id = st.uid')
            ->join('money mo', 'ur.id = mo.uid')
            ->where($where)
            ->order($order)
            ->paginate($params['limit'])->toArray();

        foreach ($data['data'] as &$user)
        {
            $user['online']            = $OnlineUser->isOnline($user['id']);
            $user['login_time']        = date('Y-m-d H:i:s', $user['login_time']);
            $user['item_not_finish']   = 0;

            for ($i = 1; $i <= 5; $i++)
            {
                $where    = [];
                $where[]  = ['top_id', '=', $user['id']];
                $where[]  = ['level', '=', $i];

                $user['team'.$i]            = $UserRelationRepo->countByCondition($where);
                $user['item_not_finish']    = 0;
            }
        }

        $MoneyLogRepo     = new MoneyLogRepository();
        $history          = strtotime(date('Y-m-d 00:00:00', 1));
        $tomorrow         = strtotime(date('Y-m-d 00:00:00', strtotime('+1 day', Request::time())));

        $UserInfoRepo     = new UserInfoRepository();
        $UserStateRepo    = new UserStateRepository();

        //13总充值
        $team['total_chongzhi']             = empty($members) ? 0 : $MoneyLogRepo->statistics($history, $tomorrow, [MoneyClass::RECHARGE], 0, $members);
        //14总提现
        $team['total_tixian']               = empty($members) ? 0 : $MoneyLogRepo->statistics($history, $tomorrow, [MoneyClass::WITHDRAW], 0, $members);
        //19总订阅金额
        $team['total_dingyue_jiner']        = empty($members) ? 0 : $UserInfoRepo->totalItemMoney($history,$tomorrow,0, $members);
        //18总充值人数
        $team['total_chongzhi_reshu']       = empty($members) ? 0 : $UserInfoRepo->totalRechargeUsers($history,$tomorrow,0, $members);
        //21总订阅人数
        $team['total_dingyue_ershu']        = empty($members) ? 0 : $UserStateRepo->registerUsers($history,$tomorrow,0,$members);

        $data['team']                       = $team;

        return Result::success($data);
    }

}
