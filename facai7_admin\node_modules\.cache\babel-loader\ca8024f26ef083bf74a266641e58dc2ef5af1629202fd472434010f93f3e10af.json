{"ast": null, "code": "import cloneArrayBuffer from './_cloneArrayBuffer.js';\n\n/**\n * Creates a clone of `typedArray`.\n *\n * @private\n * @param {Object} typedArray The typed array to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the cloned typed array.\n */\nfunction cloneTypedArray(typedArray, isDeep) {\n  var buffer = isDeep ? cloneArrayBuffer(typedArray.buffer) : typedArray.buffer;\n  return new typedArray.constructor(buffer, typedArray.byteOffset, typedArray.length);\n}\nexport default cloneTypedArray;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cloneTypedArray", "typedArray", "isDeep", "buffer", "constructor", "byteOffset", "length"], "sources": ["D:/WorkSpace/facai7/facai7_admin/node_modules/lodash-es/_cloneTypedArray.js"], "sourcesContent": ["import cloneArrayBuffer from './_cloneArrayBuffer.js';\n\n/**\n * Creates a clone of `typedArray`.\n *\n * @private\n * @param {Object} typedArray The typed array to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the cloned typed array.\n */\nfunction cloneTypedArray(typedArray, isDeep) {\n  var buffer = isDeep ? cloneArrayBuffer(typedArray.buffer) : typedArray.buffer;\n  return new typedArray.constructor(buffer, typedArray.byteOffset, typedArray.length);\n}\n\nexport default cloneTypedArray;\n"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,wBAAwB;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,UAAU,EAAEC,MAAM,EAAE;EAC3C,IAAIC,MAAM,GAAGD,MAAM,GAAGH,gBAAgB,CAACE,UAAU,CAACE,MAAM,CAAC,GAAGF,UAAU,CAACE,MAAM;EAC7E,OAAO,IAAIF,UAAU,CAACG,WAAW,CAACD,MAAM,EAAEF,UAAU,CAACI,UAAU,EAAEJ,UAAU,CAACK,MAAM,CAAC;AACrF;AAEA,eAAeN,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}