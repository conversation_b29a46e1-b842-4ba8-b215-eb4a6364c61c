{"ast": null, "code": "import { lottoryTypeBEnums, rolesEnums } from \"@/config/enums\";\nimport { onBeforeUnmount, nextTick, ref, shallowRef, onMounted, getCurrentInstance } from \"vue\";\nimport { getTokenAUTH } from \"@/utils/auth\";\nexport default {\n  __name: 'editPop',\n  props: [\"item\"],\n  setup(__props, {\n    expose: __expose\n  }) {\n    const form = ref({\n      title: \"\",\n      money: \"\",\n      // words: \"\",\n      type: \"\",\n      chance: \"\",\n      img: \"\"\n    });\n    const props = __props;\n    const successUpload = res => {\n      form.value.img = res.data.url;\n    };\n    const handleErr = err => {\n      if (err.status == 320) {\n        form.value.img = JSON.parse(err.message).data.url;\n      }\n    };\n    const headers = ref({});\n    onMounted(() => {\n      headers.value['Accept-Token'] = getTokenAUTH();\n      nextTick(() => {\n        form.value = Object.assign(form.value, props.item);\n      });\n    });\n    const {\n      proxy\n    } = getCurrentInstance();\n\n    // 编辑器实例，必须用 shallowRef\n    const editorRef = shallowRef();\n\n    // 内容 HTML\n    const valueHtml = ref(\"<p>hello</p>\");\n    const mode = ref(\"default\");\n    const toolbarConfig = {};\n    const editorConfig = {\n      placeholder: \"请输入内容...\",\n      MENU_CONF: {\n        uploadImage: {\n          fieldName: \"file\",\n          maxFileSize: 10 * 1024 * 1024,\n          // 10M\n          server: proxy.BASE_API_URL + \"index/uploadX\",\n          headers: {\n            \"Accept-Token\": getTokenAUTH()\n          },\n          customInsert(res, insertFn) {\n            const url = proxy.IMG_BASE_URL + res.data.url;\n            const alt = res.data.alt;\n            const href = res.data.href;\n            insertFn(url, alt, href);\n          }\n        }\n      }\n    };\n\n    // 组件销毁时，也及时销毁编辑器\n    onBeforeUnmount(() => {\n      const editor = editorRef.value;\n      if (editor == null) return;\n      editor.destroy();\n    });\n    const handleCreated = editor => {\n      editorRef.value = editor; // 记录 editor 实例，重要！\n    };\n    __expose({\n      form\n    });\n    const __returned__ = {\n      form,\n      props,\n      successUpload,\n      handleErr,\n      headers,\n      proxy,\n      editorRef,\n      valueHtml,\n      mode,\n      toolbarConfig,\n      editorConfig,\n      handleCreated,\n      get lottoryTypeBEnums() {\n        return lottoryTypeBEnums;\n      },\n      get rolesEnums() {\n        return rolesEnums;\n      },\n      onBeforeUnmount,\n      nextTick,\n      ref,\n      shallowRef,\n      onMounted,\n      getCurrentInstance,\n      get getTokenAUTH() {\n        return getTokenAUTH;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["lottoryTypeBEnums", "rolesEnums", "onBeforeUnmount", "nextTick", "ref", "shallowRef", "onMounted", "getCurrentInstance", "getTokenAUTH", "form", "title", "money", "type", "chance", "img", "props", "__props", "successUpload", "res", "value", "data", "url", "handleErr", "err", "status", "JSON", "parse", "message", "headers", "Object", "assign", "item", "proxy", "editor<PERSON><PERSON>", "valueHtml", "mode", "toolbarConfig", "editorConfig", "placeholder", "MENU_CONF", "uploadImage", "fieldName", "maxFileSize", "server", "BASE_API_URL", "customInsert", "insertFn", "IMG_BASE_URL", "alt", "href", "editor", "destroy", "handleCreated", "__expose"], "sources": ["D:/WorkSpace/facai7/facai7_admin/src/views/operationManage/components/lottory/editPop.vue"], "sourcesContent": ["<template>\r\n  <el-form\r\n    label-width=\"100px\"\r\n    :inline=\"true\"\r\n    :model=\"form\"\r\n    class=\"demo-form-inline\"\r\n  >\r\n    <el-form-item label=\"标题\" required>\r\n      <el-input v-model=\"form.title\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"金额\" required>\r\n      <el-input v-model=\"form.money\" clearable />\r\n    </el-form-item>\r\n    <!-- <el-form-item label=\"集字\" required>\r\n      <el-input v-model=\"form.words\" clearable />\r\n    </el-form-item> -->\r\n    <el-form-item\r\n      label=\"奖品类型\"\r\n      prop=\"type\"\r\n      :rules=\"[\r\n        {\r\n          required: true,\r\n          message: '请选择奖品类型',\r\n          trigger: ['change'],\r\n        },\r\n      ]\"\r\n    >\r\n      <el-select v-model=\"form.type\" placeholder=\"奖品类型\" clearable>\r\n        <el-option\r\n          v-for=\"item in lottoryTypeBEnums\"\r\n          :label=\"item.label\"\r\n          :key=\"item.label\"\r\n          :value=\"item.value\"\r\n        />\r\n      </el-select>\r\n    </el-form-item>\r\n    <el-form-item label=\"概率\" required>\r\n      <el-input v-model=\"form.chance\" clearable />\r\n    </el-form-item>\r\n\r\n    <el-form-item\r\n      label=\"图片\"\r\n      prop=\"img\"\r\n      :rules=\"[{ required: true, message: '请上传图片' }]\"\r\n    >\r\n      <el-upload\r\n        class=\"upload-demo\"\r\n        style=\"width: 114px\"\r\n        :show-file-list=\"false\"\r\n        drag\r\n        :headers=\"headers\"\r\n        :action=\"`${proxy.BASE_API_URL}index/upload`\"\r\n        :on-success=\"successUpload\"\r\n        :on-error=\"handleErr\"\r\n        :multiple=\"false\"\r\n      >\r\n        <img v-if=\"form.img\" :src=\"proxy.IMG_BASE_URL + form.img\" width=\"100%\" class=\"avatar\" />\r\n        <el-icon v-else class=\"avatar-uploader-icon\"><Plus /></el-icon\r\n      ></el-upload>\r\n    </el-form-item>\r\n  </el-form>\r\n</template>\r\n\r\n<script setup>\r\nimport { lottoryTypeBEnums, rolesEnums } from \"@/config/enums\";\r\nimport { onBeforeUnmount, nextTick, ref, shallowRef, onMounted, getCurrentInstance } from \"vue\";\r\nimport { getTokenAUTH } from \"@/utils/auth\";\r\n\r\nconst form = ref({\r\n  title: \"\",\r\n  money: \"\",\r\n  // words: \"\",\r\n  type: \"\",\r\n  chance: \"\",\r\n  img: \"\",\r\n});\r\nconst props = defineProps([\"item\"]);\r\n\r\nconst successUpload = (res) => {\r\n  form.value.img = res.data.url;\r\n};\r\n\r\nconst handleErr = (err) => {\r\n  if (err.status == 320) {\r\n    form.value.img = JSON.parse(err.message).data.url;\r\n  }\r\n}\r\n\r\nconst headers = ref({})\r\nonMounted(() => {\r\n  headers.value['Accept-Token'] = getTokenAUTH()\r\n  nextTick(() => {\r\n    form.value = Object.assign(form.value, props.item);\r\n  });\r\n});\r\n\r\nconst { proxy } = getCurrentInstance()\r\n\r\n// 编辑器实例，必须用 shallowRef\r\nconst editorRef = shallowRef();\r\n\r\n// 内容 HTML\r\nconst valueHtml = ref(\"<p>hello</p>\");\r\nconst mode = ref(\"default\");\r\n\r\nconst toolbarConfig = {};\r\nconst editorConfig =  {\r\n  placeholder: \"请输入内容...\",\r\n  MENU_CONF: {\r\n    uploadImage: {\r\n      fieldName: \"file\",\r\n      maxFileSize: 10 * 1024 * 1024, // 10M\r\n      server: proxy.BASE_API_URL + \"index/uploadX\",\r\n      headers: {\r\n        \"Accept-Token\": getTokenAUTH(),\r\n      },\r\n      customInsert(res, insertFn) {\r\n        const url = proxy.IMG_BASE_URL + res.data.url;\r\n        const alt = res.data.alt\r\n        const href = res.data.href\r\n        insertFn(url, alt, href);\r\n      },\r\n    },\r\n  },\r\n};\r\n\r\n// 组件销毁时，也及时销毁编辑器\r\nonBeforeUnmount(() => {\r\n  const editor = editorRef.value;\r\n  if (editor == null) return;\r\n  editor.destroy();\r\n});\r\n\r\nconst handleCreated = (editor) => {\r\n  editorRef.value = editor; // 记录 editor 实例，重要！\r\n};\r\n\r\ndefineExpose({ form });\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.demo-form-inline {\r\n  justify-content: flex-start;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.demo-form-inline .el-input {\r\n  --el-input-width: 220px;\r\n}\r\n\r\n.demo-form-inline .el-select {\r\n  --el-select-width: 220px;\r\n}\r\n\r\n/deep/ .el-radio-group {\r\n  width: 220px;\r\n}\r\n\r\n.form-title {\r\n  text-align: left;\r\n  padding-left: 30px;\r\n  margin: 20px auto 10px;\r\n  height: 44px;\r\n  background-color: #f2f2f2;\r\n  border-radius: 5px;\r\n  line-height: 44px;\r\n}\r\n\r\n/deep/ .el-form-item {\r\n  align-items: flex-start;\r\n}\r\n</style>\r\n"], "mappings": "AAgEA,SAASA,iBAAiB,EAAEC,UAAU,QAAQ,gBAAgB;AAC9D,SAASC,eAAe,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAEC,kBAAkB,QAAQ,KAAK;AAC/F,SAASC,YAAY,QAAQ,cAAc;;;;;;;IAE3C,MAAMC,IAAI,GAAGL,GAAG,CAAC;MACfM,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACT;MACAC,IAAI,EAAE,EAAE;MACRC,MAAM,EAAE,EAAE;MACVC,GAAG,EAAE;IACP,CAAC,CAAC;IACF,MAAMC,KAAK,GAAGC,OAAqB;IAEnC,MAAMC,aAAa,GAAIC,GAAG,IAAK;MAC7BT,IAAI,CAACU,KAAK,CAACL,GAAG,GAAGI,GAAG,CAACE,IAAI,CAACC,GAAG;IAC/B,CAAC;IAED,MAAMC,SAAS,GAAIC,GAAG,IAAK;MACzB,IAAIA,GAAG,CAACC,MAAM,IAAI,GAAG,EAAE;QACrBf,IAAI,CAACU,KAAK,CAACL,GAAG,GAAGW,IAAI,CAACC,KAAK,CAACH,GAAG,CAACI,OAAO,CAAC,CAACP,IAAI,CAACC,GAAG;MACnD;IACF,CAAC;IAED,MAAMO,OAAO,GAAGxB,GAAG,CAAC,CAAC,CAAC,CAAC;IACvBE,SAAS,CAAC,MAAM;MACdsB,OAAO,CAACT,KAAK,CAAC,cAAc,CAAC,GAAGX,YAAY,CAAC,CAAC;MAC9CL,QAAQ,CAAC,MAAM;QACbM,IAAI,CAACU,KAAK,GAAGU,MAAM,CAACC,MAAM,CAACrB,IAAI,CAACU,KAAK,EAAEJ,KAAK,CAACgB,IAAI,CAAC;MACpD,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,MAAM;MAAEC;IAAM,CAAC,GAAGzB,kBAAkB,CAAC,CAAC;;IAEtC;IACA,MAAM0B,SAAS,GAAG5B,UAAU,CAAC,CAAC;;IAE9B;IACA,MAAM6B,SAAS,GAAG9B,GAAG,CAAC,cAAc,CAAC;IACrC,MAAM+B,IAAI,GAAG/B,GAAG,CAAC,SAAS,CAAC;IAE3B,MAAMgC,aAAa,GAAG,CAAC,CAAC;IACxB,MAAMC,YAAY,GAAI;MACpBC,WAAW,EAAE,UAAU;MACvBC,SAAS,EAAE;QACTC,WAAW,EAAE;UACXC,SAAS,EAAE,MAAM;UACjBC,WAAW,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;UAAE;UAC/BC,MAAM,EAAEX,KAAK,CAACY,YAAY,GAAG,eAAe;UAC5ChB,OAAO,EAAE;YACP,cAAc,EAAEpB,YAAY,CAAC;UAC/B,CAAC;UACDqC,YAAYA,CAAC3B,GAAG,EAAE4B,QAAQ,EAAE;YAC1B,MAAMzB,GAAG,GAAGW,KAAK,CAACe,YAAY,GAAG7B,GAAG,CAACE,IAAI,CAACC,GAAG;YAC7C,MAAM2B,GAAG,GAAG9B,GAAG,CAACE,IAAI,CAAC4B,GAAG;YACxB,MAAMC,IAAI,GAAG/B,GAAG,CAACE,IAAI,CAAC6B,IAAI;YAC1BH,QAAQ,CAACzB,GAAG,EAAE2B,GAAG,EAAEC,IAAI,CAAC;UAC1B;QACF;MACF;IACF,CAAC;;IAED;IACA/C,eAAe,CAAC,MAAM;MACpB,MAAMgD,MAAM,GAAGjB,SAAS,CAACd,KAAK;MAC9B,IAAI+B,MAAM,IAAI,IAAI,EAAE;MACpBA,MAAM,CAACC,OAAO,CAAC,CAAC;IAClB,CAAC,CAAC;IAEF,MAAMC,aAAa,GAAIF,MAAM,IAAK;MAChCjB,SAAS,CAACd,KAAK,GAAG+B,MAAM,CAAC,CAAC;IAC5B,CAAC;IAEDG,QAAY,CAAC;MAAE5C;IAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}