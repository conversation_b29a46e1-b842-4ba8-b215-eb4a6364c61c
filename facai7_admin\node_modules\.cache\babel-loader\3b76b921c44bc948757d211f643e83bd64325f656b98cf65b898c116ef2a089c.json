{"ast": null, "code": "import { rolesEnums } from \"@/config/enums\";\nimport { onBeforeUnmount, nextTick, ref, shallowRef, onMounted, getCurrentInstance } from \"vue\";\nimport fileUploadHoook from \"@/hooks/fileUpload\";\nimport { htmlDecodeByRegExp } from \"@/utils/utils\";\nimport { getTokenAUTH } from \"@/utils/auth\";\nexport default {\n  __name: 'editPop',\n  props: [\"item\"],\n  setup(__props, {\n    expose: __expose\n  }) {\n    const form = ref({\n      title: \"\",\n      content: \"\",\n      class_id: \"\",\n      release_time: \"\",\n      code: \"\",\n      desc: \"\",\n      img: \"\"\n    });\n    const props = __props;\n    const headers = ref({});\n    onMounted(() => {\n      headers.value['Accept-Token'] = getTokenAUTH();\n      nextTick(() => {\n        props.item.content = htmlDecodeByRegExp(props.item.content);\n        form.value = Object.assign(form.value, props.item);\n      });\n      getTYpesEnum();\n    });\n    const {\n      proxy\n    } = getCurrentInstance();\n    const typesEnum = ref([]);\n    const getTYpesEnum = async () => {\n      const res = await proxy.$http({\n        method: \"get\",\n        url: \"/Article/getArticleClassLists\"\n      });\n      if (res.code == 0) {\n        typesEnum.value = res.data.data;\n      }\n    };\n    const successUpload = res => {\n      form.value.img = res.data.url;\n    };\n    const handleErr = err => {\n      if (err.status == 320) {\n        form.value.img = JSON.parse(err.message).data.url;\n      }\n    };\n\n    // 编辑器实例，必须用 shallowRef\n    const editorRef = shallowRef();\n\n    // 内容 HTML\n    const valueHtml = ref(\"<p>hello</p>\");\n    const mode = ref(\"default\");\n    const toolbarConfig = {};\n    const editorConfig = {\n      placeholder: \"请输入内容...\",\n      MENU_CONF: {\n        uploadImage: {\n          fieldName: \"file\",\n          maxFileSize: 10 * 1024 * 1024,\n          // 10M\n          server: proxy.BASE_API_URL + \"index/uploadX\",\n          headers: {\n            \"Accept-Token\": getTokenAUTH()\n          },\n          customInsert(res, insertFn) {\n            console.log(res);\n            const url = proxy.IMG_BASE_URL + res.data.url;\n            const alt = res.data.alt;\n            const href = res.data.href;\n            insertFn(url, alt, href);\n          },\n          onError(file, err, res) {\n            console.log(`${file.name} 上传出错`, err, res);\n          }\n        }\n      }\n    };\n\n    // 组件销毁时，也及时销毁编辑器\n    onBeforeUnmount(() => {\n      const editor = editorRef.value;\n      if (editor == null) return;\n      editor.destroy();\n    });\n    const handleCreated = editor => {\n      editorRef.value = editor; // 记录 editor 实例，重要！\n    };\n    __expose({\n      form\n    });\n    const __returned__ = {\n      form,\n      props,\n      headers,\n      proxy,\n      typesEnum,\n      getTYpesEnum,\n      successUpload,\n      handleErr,\n      editorRef,\n      valueHtml,\n      mode,\n      toolbarConfig,\n      editorConfig,\n      handleCreated,\n      get rolesEnums() {\n        return rolesEnums;\n      },\n      onBeforeUnmount,\n      nextTick,\n      ref,\n      shallowRef,\n      onMounted,\n      getCurrentInstance,\n      get fileUploadHoook() {\n        return fileUploadHoook;\n      },\n      get htmlDecodeByRegExp() {\n        return htmlDecodeByRegExp;\n      },\n      get getTokenAUTH() {\n        return getTokenAUTH;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["rolesEnums", "onBeforeUnmount", "nextTick", "ref", "shallowRef", "onMounted", "getCurrentInstance", "fileUploadHoook", "htmlDecodeByRegExp", "getTokenAUTH", "form", "title", "content", "class_id", "release_time", "code", "desc", "img", "props", "__props", "headers", "value", "item", "Object", "assign", "getTYpesEnum", "proxy", "typesEnum", "res", "$http", "method", "url", "data", "successUpload", "handleErr", "err", "status", "JSON", "parse", "message", "editor<PERSON><PERSON>", "valueHtml", "mode", "toolbarConfig", "editorConfig", "placeholder", "MENU_CONF", "uploadImage", "fieldName", "maxFileSize", "server", "BASE_API_URL", "customInsert", "insertFn", "console", "log", "IMG_BASE_URL", "alt", "href", "onError", "file", "name", "editor", "destroy", "handleCreated", "__expose"], "sources": ["D:/WorkSpace/facai7/facai7_admin/src/views/operationManage/components/newsList/editPop.vue"], "sourcesContent": ["<template>\r\n  <el-form\r\n    label-width=\"100px\"\r\n    :inline=\"true\"\r\n    :model=\"form\"\r\n    class=\"demo-form-inline\"\r\n  >\r\n    <el-form-item label=\"标题\" required>\r\n      <el-input v-model=\"form.title\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"描述\" required>\r\n      <el-input v-model=\"form.desc\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"文章唯一编码\" required>\r\n      <el-input v-model=\"form.code\" clearable />\r\n    </el-form-item>\r\n\r\n    <el-form-item\r\n      label=\"文章类型\"\r\n      prop=\"class_id\"\r\n      :rules=\"[\r\n        {\r\n          required: true,\r\n          message: '请选择文章类型',\r\n          trigger: ['change'],\r\n        },\r\n      ]\"\r\n    >\r\n      <el-select v-model=\"form.class_id\" placeholder=\"文章类型\" clearable>\r\n        <el-option\r\n          v-for=\"item in typesEnum\"\r\n          :label=\"item.title\"\r\n          :key=\"item.title\"\r\n          :value=\"item.id\"\r\n        />\r\n      </el-select>\r\n    </el-form-item>\r\n    <el-form-item label=\"发布时间\" required>\r\n      <el-date-picker\r\n        v-model=\"form.release_time\"\r\n        type=\"datetime\"\r\n        value-format=\"YYYY-MM-DD HH:mm:ss\"\r\n        placeholder=\"发布时间\"\r\n        style=\"width: 100%\"\r\n      />\r\n    </el-form-item>\r\n    <el-form-item\r\n      label=\"图片\"\r\n      prop=\"img\"\r\n      :rules=\"[{ required: true, message: '请上传图片' }]\"\r\n    >\r\n      <el-upload\r\n        class=\"upload-demo\"\r\n        style=\"width: 114px\"\r\n        :show-file-list=\"false\"\r\n        drag\r\n        :headers=\"headers\"\r\n        :action=\"`${proxy.BASE_API_URL}index/upload`\"\r\n        :on-success=\"successUpload\"\r\n        :on-error=\"handleErr\"\r\n        :multiple=\"false\"\r\n      >\r\n        <img\r\n          v-if=\"form.img\"\r\n          :src=\"proxy.IMG_BASE_URL + form.img\"\r\n          width=\"100%\"\r\n          class=\"avatar\" />\r\n        <el-icon v-else class=\"avatar-uploader-icon\"><Plus /></el-icon\r\n      ></el-upload>\r\n    </el-form-item>\r\n    <div style=\"border: 1px solid #ccc\">\r\n      <Toolbar\r\n        style=\"border-bottom: 1px solid #ccc\"\r\n        :editor=\"editorRef\"\r\n        :defaultConfig=\"toolbarConfig\"\r\n        :mode=\"mode\"\r\n      />\r\n      <Editor\r\n        style=\"height: 500px; overflow-y: hidden\"\r\n        v-model=\"form.content\"\r\n        :defaultConfig=\"editorConfig\"\r\n        :mode=\"mode\"\r\n        @onCreated=\"handleCreated\"\r\n      />\r\n    </div>\r\n  </el-form>\r\n</template>\r\n\r\n<script setup>\r\nimport { rolesEnums } from \"@/config/enums\";\r\nimport {\r\n  onBeforeUnmount,\r\n  nextTick,\r\n  ref,\r\n  shallowRef,\r\n  onMounted,\r\n  getCurrentInstance,\r\n} from \"vue\";\r\nimport fileUploadHoook from \"@/hooks/fileUpload\";\r\nimport { htmlDecodeByRegExp } from \"@/utils/utils\";\r\nimport { getTokenAUTH } from \"@/utils/auth\";\r\n\r\nconst form = ref({\r\n  title: \"\",\r\n  content: \"\",\r\n  class_id: \"\",\r\n  release_time: \"\",\r\n  code: \"\",\r\n  desc: \"\",\r\n  img: \"\",\r\n});\r\nconst props = defineProps([\"item\"]);\r\n\r\nconst headers = ref({})\r\nonMounted(() => {\r\n  headers.value['Accept-Token'] = getTokenAUTH()\r\n  nextTick(() => {\r\n    props.item.content = htmlDecodeByRegExp(props.item.content);\r\n    form.value = Object.assign(form.value, props.item);\r\n\r\n  });\r\n  getTYpesEnum();\r\n});\r\n\r\nconst { proxy } = getCurrentInstance();\r\nconst typesEnum = ref([]);\r\n\r\nconst getTYpesEnum = async () => {\r\n  const res = await proxy.$http({\r\n    method: \"get\",\r\n    url: \"/Article/getArticleClassLists\",\r\n  });\r\n  if (res.code == 0) {\r\n    typesEnum.value = res.data.data;\r\n  }\r\n};\r\n\r\nconst successUpload = (res) => {\r\n  form.value.img = res.data.url;\r\n};\r\n\r\nconst handleErr = (err) => {\r\n  if (err.status == 320) {\r\n    form.value.img = JSON.parse(err.message).data.url;\r\n  }\r\n}\r\n\r\n// 编辑器实例，必须用 shallowRef\r\nconst editorRef = shallowRef();\r\n\r\n// 内容 HTML\r\nconst valueHtml = ref(\"<p>hello</p>\");\r\nconst mode = ref(\"default\");\r\n\r\nconst toolbarConfig = {};\r\nconst editorConfig = {\r\n  placeholder: \"请输入内容...\",\r\n  MENU_CONF: {\r\n    uploadImage: {\r\n      fieldName: \"file\",\r\n      maxFileSize: 10 * 1024 * 1024, // 10M\r\n      server: proxy.BASE_API_URL + \"index/uploadX\",\r\n      headers: {\r\n        \"Accept-Token\": getTokenAUTH(),\r\n      },\r\n      customInsert(res, insertFn) {\r\n        console.log(res)\r\n        const url = proxy.IMG_BASE_URL + res.data.url;\r\n        const alt = res.data.alt\r\n        const href = res.data.href\r\n        insertFn(url, alt, href);\r\n      },\r\n      onError(file, err, res) {\r\n        console.log(`${file.name} 上传出错`, err, res)\r\n      }\r\n    },\r\n  },\r\n};\r\n\r\n// 组件销毁时，也及时销毁编辑器\r\nonBeforeUnmount(() => {\r\n  const editor = editorRef.value;\r\n  if (editor == null) return;\r\n  editor.destroy();\r\n});\r\n\r\nconst handleCreated = (editor) => {\r\n  editorRef.value = editor; // 记录 editor 实例，重要！\r\n};\r\n\r\ndefineExpose({ form });\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.demo-form-inline {\r\n  justify-content: flex-start;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.demo-form-inline .el-input {\r\n  --el-input-width: 220px;\r\n}\r\n\r\n.demo-form-inline .el-select {\r\n  --el-select-width: 220px;\r\n}\r\n\r\n/deep/ .el-radio-group {\r\n  width: 220px;\r\n}\r\n\r\n.form-title {\r\n  text-align: left;\r\n  padding-left: 30px;\r\n  margin: 20px auto 10px;\r\n  height: 44px;\r\n  background-color: #f2f2f2;\r\n  border-radius: 5px;\r\n  line-height: 44px;\r\n}\r\n\r\n/deep/ .el-form-item {\r\n  align-items: flex-start;\r\n}\r\n</style>\r\n"], "mappings": "AAyFA,SAASA,UAAU,QAAQ,gBAAgB;AAC3C,SACEC,eAAe,EACfC,QAAQ,EACRC,GAAG,EACHC,UAAU,EACVC,SAAS,EACTC,kBAAkB,QACb,KAAK;AACZ,OAAOC,eAAe,MAAM,oBAAoB;AAChD,SAASC,kBAAkB,QAAQ,eAAe;AAClD,SAASC,YAAY,QAAQ,cAAc;;;;;;;IAE3C,MAAMC,IAAI,GAAGP,GAAG,CAAC;MACfQ,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,QAAQ,EAAE,EAAE;MACZC,YAAY,EAAE,EAAE;MAChBC,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,EAAE;MACRC,GAAG,EAAE;IACP,CAAC,CAAC;IACF,MAAMC,KAAK,GAAGC,OAAqB;IAEnC,MAAMC,OAAO,GAAGjB,GAAG,CAAC,CAAC,CAAC,CAAC;IACvBE,SAAS,CAAC,MAAM;MACde,OAAO,CAACC,KAAK,CAAC,cAAc,CAAC,GAAGZ,YAAY,CAAC,CAAC;MAC9CP,QAAQ,CAAC,MAAM;QACbgB,KAAK,CAACI,IAAI,CAACV,OAAO,GAAGJ,kBAAkB,CAACU,KAAK,CAACI,IAAI,CAACV,OAAO,CAAC;QAC3DF,IAAI,CAACW,KAAK,GAAGE,MAAM,CAACC,MAAM,CAACd,IAAI,CAACW,KAAK,EAAEH,KAAK,CAACI,IAAI,CAAC;MAEpD,CAAC,CAAC;MACFG,YAAY,CAAC,CAAC;IAChB,CAAC,CAAC;IAEF,MAAM;MAAEC;IAAM,CAAC,GAAGpB,kBAAkB,CAAC,CAAC;IACtC,MAAMqB,SAAS,GAAGxB,GAAG,CAAC,EAAE,CAAC;IAEzB,MAAMsB,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,MAAMG,GAAG,GAAG,MAAMF,KAAK,CAACG,KAAK,CAAC;QAC5BC,MAAM,EAAE,KAAK;QACbC,GAAG,EAAE;MACP,CAAC,CAAC;MACF,IAAIH,GAAG,CAACb,IAAI,IAAI,CAAC,EAAE;QACjBY,SAAS,CAACN,KAAK,GAAGO,GAAG,CAACI,IAAI,CAACA,IAAI;MACjC;IACF,CAAC;IAED,MAAMC,aAAa,GAAIL,GAAG,IAAK;MAC7BlB,IAAI,CAACW,KAAK,CAACJ,GAAG,GAAGW,GAAG,CAACI,IAAI,CAACD,GAAG;IAC/B,CAAC;IAED,MAAMG,SAAS,GAAIC,GAAG,IAAK;MACzB,IAAIA,GAAG,CAACC,MAAM,IAAI,GAAG,EAAE;QACrB1B,IAAI,CAACW,KAAK,CAACJ,GAAG,GAAGoB,IAAI,CAACC,KAAK,CAACH,GAAG,CAACI,OAAO,CAAC,CAACP,IAAI,CAACD,GAAG;MACnD;IACF,CAAC;;IAED;IACA,MAAMS,SAAS,GAAGpC,UAAU,CAAC,CAAC;;IAE9B;IACA,MAAMqC,SAAS,GAAGtC,GAAG,CAAC,cAAc,CAAC;IACrC,MAAMuC,IAAI,GAAGvC,GAAG,CAAC,SAAS,CAAC;IAE3B,MAAMwC,aAAa,GAAG,CAAC,CAAC;IACxB,MAAMC,YAAY,GAAG;MACnBC,WAAW,EAAE,UAAU;MACvBC,SAAS,EAAE;QACTC,WAAW,EAAE;UACXC,SAAS,EAAE,MAAM;UACjBC,WAAW,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;UAAE;UAC/BC,MAAM,EAAExB,KAAK,CAACyB,YAAY,GAAG,eAAe;UAC5C/B,OAAO,EAAE;YACP,cAAc,EAAEX,YAAY,CAAC;UAC/B,CAAC;UACD2C,YAAYA,CAACxB,GAAG,EAAEyB,QAAQ,EAAE;YAC1BC,OAAO,CAACC,GAAG,CAAC3B,GAAG,CAAC;YAChB,MAAMG,GAAG,GAAGL,KAAK,CAAC8B,YAAY,GAAG5B,GAAG,CAACI,IAAI,CAACD,GAAG;YAC7C,MAAM0B,GAAG,GAAG7B,GAAG,CAACI,IAAI,CAACyB,GAAG;YACxB,MAAMC,IAAI,GAAG9B,GAAG,CAACI,IAAI,CAAC0B,IAAI;YAC1BL,QAAQ,CAACtB,GAAG,EAAE0B,GAAG,EAAEC,IAAI,CAAC;UAC1B,CAAC;UACDC,OAAOA,CAACC,IAAI,EAAEzB,GAAG,EAAEP,GAAG,EAAE;YACtB0B,OAAO,CAACC,GAAG,CAAC,GAAGK,IAAI,CAACC,IAAI,OAAO,EAAE1B,GAAG,EAAEP,GAAG,CAAC;UAC5C;QACF;MACF;IACF,CAAC;;IAED;IACA3B,eAAe,CAAC,MAAM;MACpB,MAAM6D,MAAM,GAAGtB,SAAS,CAACnB,KAAK;MAC9B,IAAIyC,MAAM,IAAI,IAAI,EAAE;MACpBA,MAAM,CAACC,OAAO,CAAC,CAAC;IAClB,CAAC,CAAC;IAEF,MAAMC,aAAa,GAAIF,MAAM,IAAK;MAChCtB,SAAS,CAACnB,KAAK,GAAGyC,MAAM,CAAC,CAAC;IAC5B,CAAC;IAEDG,QAAY,CAAC;MAAEvD;IAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}