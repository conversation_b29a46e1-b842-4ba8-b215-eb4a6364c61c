{"ast": null, "code": "import escapeHtmlChar from './_escapeHtmlChar.js';\nimport toString from './toString.js';\n\n/** Used to match HTML entities and HTML characters. */\nvar reUnescapedHtml = /[&<>\"']/g,\n  reHasUnescapedHtml = RegExp(reUnescapedHtml.source);\n\n/**\n * Converts the characters \"&\", \"<\", \">\", '\"', and \"'\" in `string` to their\n * corresponding HTML entities.\n *\n * **Note:** No other characters are escaped. To escape additional\n * characters use a third-party library like [_he_](https://mths.be/he).\n *\n * Though the \">\" character is escaped for symmetry, characters like\n * \">\" and \"/\" don't need escaping in HTML and have no special meaning\n * unless they're part of a tag or unquoted attribute value. See\n * [<PERSON>'s article](https://mathiasbynens.be/notes/ambiguous-ampersands)\n * (under \"semi-related fun fact\") for more details.\n *\n * When working with HTML you should always\n * [quote attribute values](http://wonko.com/post/html-escaping) to reduce\n * XSS vectors.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category String\n * @param {string} [string=''] The string to escape.\n * @returns {string} Returns the escaped string.\n * @example\n *\n * _.escape('fred, barney, & pebbles');\n * // => 'fred, barney, &amp; pebbles'\n */\nfunction escape(string) {\n  string = toString(string);\n  return string && reHasUnescapedHtml.test(string) ? string.replace(reUnescapedHtml, escapeHtmlChar) : string;\n}\nexport default escape;", "map": {"version": 3, "names": ["escapeHtmlChar", "toString", "reUnescapedHtml", "reHasUnescapedHtml", "RegExp", "source", "escape", "string", "test", "replace"], "sources": ["D:/WorkSpace/facai7/facai7_admin/node_modules/lodash-es/escape.js"], "sourcesContent": ["import escapeHtmlChar from './_escapeHtmlChar.js';\nimport toString from './toString.js';\n\n/** Used to match HTML entities and HTML characters. */\nvar reUnescapedHtml = /[&<>\"']/g,\n    reHasUnescapedHtml = RegExp(reUnescapedHtml.source);\n\n/**\n * Converts the characters \"&\", \"<\", \">\", '\"', and \"'\" in `string` to their\n * corresponding HTML entities.\n *\n * **Note:** No other characters are escaped. To escape additional\n * characters use a third-party library like [_he_](https://mths.be/he).\n *\n * Though the \">\" character is escaped for symmetry, characters like\n * \">\" and \"/\" don't need escaping in HTML and have no special meaning\n * unless they're part of a tag or unquoted attribute value. See\n * [<PERSON>'s article](https://mathiasbynens.be/notes/ambiguous-ampersands)\n * (under \"semi-related fun fact\") for more details.\n *\n * When working with HTML you should always\n * [quote attribute values](http://wonko.com/post/html-escaping) to reduce\n * XSS vectors.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category String\n * @param {string} [string=''] The string to escape.\n * @returns {string} Returns the escaped string.\n * @example\n *\n * _.escape('fred, barney, & pebbles');\n * // => 'fred, barney, &amp; pebbles'\n */\nfunction escape(string) {\n  string = toString(string);\n  return (string && reHasUnescapedHtml.test(string))\n    ? string.replace(reUnescapedHtml, escapeHtmlChar)\n    : string;\n}\n\nexport default escape;\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,sBAAsB;AACjD,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA,IAAIC,eAAe,GAAG,UAAU;EAC5BC,kBAAkB,GAAGC,MAAM,CAACF,eAAe,CAACG,MAAM,CAAC;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACC,MAAM,EAAE;EACtBA,MAAM,GAAGN,QAAQ,CAACM,MAAM,CAAC;EACzB,OAAQA,MAAM,IAAIJ,kBAAkB,CAACK,IAAI,CAACD,MAAM,CAAC,GAC7CA,MAAM,CAACE,OAAO,CAACP,eAAe,EAAEF,cAAc,CAAC,GAC/CO,MAAM;AACZ;AAEA,eAAeD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}