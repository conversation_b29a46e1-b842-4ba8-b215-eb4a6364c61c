{"ast": null, "code": "import { defineComponent, computed, openBlock, createElementBlock, mergeProps, unref, renderSlot } from 'vue';\nimport { visualHiddenProps } from './visual-hidden.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nconst __default__ = defineComponent({\n  name: \"ElVisuallyHidden\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: visualHiddenProps,\n  setup(__props) {\n    const props = __props;\n    const computedStyle = computed(() => {\n      return [props.style, {\n        position: \"absolute\",\n        border: 0,\n        width: 1,\n        height: 1,\n        padding: 0,\n        margin: -1,\n        overflow: \"hidden\",\n        clip: \"rect(0, 0, 0, 0)\",\n        whiteSpace: \"nowrap\",\n        wordWrap: \"normal\"\n      }];\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"span\", mergeProps(_ctx.$attrs, {\n        style: unref(computedStyle)\n      }), [renderSlot(_ctx.$slots, \"default\")], 16);\n    };\n  }\n});\nvar ElVisuallyHidden = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"visual-hidden.vue\"]]);\nexport { ElVisuallyHidden as default };", "map": {"version": 3, "names": ["name", "computedStyle", "computed", "props", "style", "position", "border", "width", "height", "padding", "margin", "overflow", "clip", "whiteSpace", "wordWrap"], "sources": ["../../../../../../packages/components/visual-hidden/src/visual-hidden.vue"], "sourcesContent": ["<template>\n  <span v-bind=\"$attrs\" :style=\"computedStyle\">\n    <slot />\n  </span>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { visualHiddenProps } from './visual-hidden'\n\nimport type { StyleValue } from 'vue'\n\nconst props = defineProps(visualHiddenProps)\n\ndefineOptions({\n  name: 'ElVisuallyHidden',\n})\n\nconst computedStyle = computed<StyleValue>(() => {\n  return [\n    props.style,\n    {\n      position: 'absolute',\n      border: 0,\n      width: 1,\n      height: 1,\n      padding: 0,\n      margin: -1,\n      overflow: 'hidden',\n      clip: 'rect(0, 0, 0, 0)',\n      whiteSpace: 'nowrap',\n      wordWrap: 'normal',\n    },\n  ]\n})\n</script>\n"], "mappings": ";;;mCAcc;EACZA,IAAM;AACR;;;;;;IAEM,MAAAC,aAAA,GAAgBC,QAAA,CAAqB,MAAM;MACxC,QACLC,KAAM,CAAAC,KAAA,EACN;QACEC,QAAU;QACVC,MAAQ;QACRC,KAAO;QACPC,MAAQ;QACRC,OAAS;QACTC,MAAQ;QACRC,QAAU;QACVC,IAAM;QACNC,UAAY;QACZC,QAAU;MAAA,CACZ,CACF;IAAA,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}