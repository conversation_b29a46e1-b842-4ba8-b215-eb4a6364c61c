{"ast": null, "code": "import { couponStatusEnums } from \"@/config/enums\";\nimport { htmlDecodeByRegExp } from '@/utils/utils';\nimport { onBeforeUnmount, nextTick, ref, shallowRef, onMounted, getCurrentInstance } from \"vue\";\nimport { getTokenAUTH } from \"@/utils/auth\";\nexport default {\n  __name: 'editPop',\n  props: [\"item\"],\n  setup(__props, {\n    expose: __expose\n  }) {\n    const form = ref({\n      phone: \"\",\n      amount: \"\",\n      allow: \"\",\n      status: \"\",\n      expire_time: \"\"\n    });\n    const props = __props;\n\n    // const headers = ref({})\n    onMounted(() => {\n      // headers.value['Accept-Token'] = getTokenAUTH()\n      nextTick(() => {\n        // props.item.content = htmlDecodeByRegExp(props.item.content)\n        form.value = Object.assign(form.value, props.item);\n      });\n    });\n    const {\n      proxy\n    } = getCurrentInstance();\n\n    // const typesEnum = ref([])\n\n    // const successUpload = (res) => {\n    //   form.value.img = res.data.url;\n    // };\n\n    // const handleErr = (err) => {\n    //   if (err.status == 320) {\n    //     form.value.img = JSON.parse(err.message).data.url;\n    //   }\n    // }\n\n    // // 编辑器实例，必须用 shallowRef\n    // const editorRef = shallowRef();\n\n    // // 内容 HTML\n    // const valueHtml = ref(\"<p>hello</p>\");\n    // const mode = ref(\"default\");\n\n    // const toolbarConfig = {};\n    // const editorConfig =  {\n    //   placeholder: \"请输入内容...\",\n    //   MENU_CONF: {\n    //     uploadImage: {\n    //       fieldName: \"file\",\n    //       maxFileSize: 10 * 1024 * 1024, // 10M\n    //       server: proxy.BASE_API_URL + \"index/uploadX\",\n    //       headers: {\n    //         \"Accept-Token\": getTokenAUTH(),\n    //       },\n    //       customInsert(res, insertFn) {\n    //         const url = proxy.IMG_BASE_URL + res.data.url;\n    //         const alt = res.data.alt\n    //         const href = res.data.href\n    //         insertFn(url, alt, href);\n    //       },\n    //     },\n    //   },\n    // };\n\n    // 组件销毁时，也及时销毁编辑器\n    onBeforeUnmount(() => {\n      // const editor = editorRef.value;\n      // if (editor == null) return;\n      // editor.destroy();\n    });\n    const handleCreated = editor => {\n      // editorRef.value = editor; // 记录 editor 实例，重要！\n    };\n    __expose({\n      form\n    });\n    const __returned__ = {\n      form,\n      props,\n      proxy,\n      handleCreated,\n      get couponStatusEnums() {\n        return couponStatusEnums;\n      },\n      get htmlDecodeByRegExp() {\n        return htmlDecodeByRegExp;\n      },\n      onBeforeUnmount,\n      nextTick,\n      ref,\n      shallowRef,\n      onMounted,\n      getCurrentInstance,\n      get getTokenAUTH() {\n        return getTokenAUTH;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["couponStatusEnums", "htmlDecodeByRegExp", "onBeforeUnmount", "nextTick", "ref", "shallowRef", "onMounted", "getCurrentInstance", "getTokenAUTH", "form", "phone", "amount", "allow", "status", "expire_time", "props", "__props", "value", "Object", "assign", "item", "proxy", "handleCreated", "editor", "__expose"], "sources": ["D:/WorkSpace/facai7/facai7_admin/src/views/goodsManage/components/discountList/editPop.vue"], "sourcesContent": ["<template>\r\n  <el-form\r\n    label-width=\"100px\"\r\n    :inline=\"true\"\r\n    :model=\"form\"\r\n    class=\"demo-form-inline\"\r\n  >\r\n    <el-form-item label=\"电话\" required>\r\n      <el-input v-model=\"form.phone\" clearable />\r\n    </el-form-item>\r\n\r\n    <el-form-item label=\"数额\" required>\r\n      <el-input v-model=\"form.amount\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"最小使用金额\" required>\r\n      <el-input v-model=\"form.allow\" clearable />\r\n    </el-form-item>\r\n    <el-form-item\r\n        label=\"状态\"\r\n        prop=\"status\"\r\n      >\r\n        <el-select v-model=\"form.status\" placeholder=\"状态\" clearable>\r\n          <el-option\r\n            v-for=\"item in couponStatusEnums\"\r\n            :label=\"item.label\"\r\n            :key=\"item.label\"\r\n            :value=\"item.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n    <el-form-item label=\"过期时间\" required>\r\n      <el-date-picker\r\n        v-model=\"form.expire_time\"\r\n        type=\"datetime\"\r\n        value-format=\"YYYY-MM-DD HH:mm:ss\"\r\n        placeholder=\"过期时间\"\r\n        style=\"width: 100%\"\r\n      />\r\n    </el-form-item>\r\n   \r\n  </el-form>\r\n</template>\r\n\r\n<script setup>\r\nimport { couponStatusEnums } from \"@/config/enums\";\r\nimport { htmlDecodeByRegExp } from '@/utils/utils'\r\nimport { onBeforeUnmount, nextTick, ref, shallowRef, onMounted, getCurrentInstance } from \"vue\";\r\nimport { getTokenAUTH } from \"@/utils/auth\";\r\n\r\nconst form = ref({\r\n  phone: \"\",\r\n  amount: \"\",\r\n  allow: \"\",\r\n  status: \"\",\r\n  expire_time: \"\",\r\n});\r\nconst props = defineProps([\"item\"]);\r\n\r\n// const headers = ref({})\r\nonMounted(() => {\r\n  // headers.value['Accept-Token'] = getTokenAUTH()\r\n  nextTick(() => {\r\n    // props.item.content = htmlDecodeByRegExp(props.item.content)\r\n    form.value = Object.assign(form.value, props.item);\r\n  });\r\n});\r\n\r\nconst { proxy } = getCurrentInstance()\r\n\r\n// const typesEnum = ref([])\r\n\r\n\r\n// const successUpload = (res) => {\r\n//   form.value.img = res.data.url;\r\n// };\r\n\r\n// const handleErr = (err) => {\r\n//   if (err.status == 320) {\r\n//     form.value.img = JSON.parse(err.message).data.url;\r\n//   }\r\n// }\r\n\r\n// // 编辑器实例，必须用 shallowRef\r\n// const editorRef = shallowRef();\r\n\r\n// // 内容 HTML\r\n// const valueHtml = ref(\"<p>hello</p>\");\r\n// const mode = ref(\"default\");\r\n\r\n// const toolbarConfig = {};\r\n// const editorConfig =  {\r\n//   placeholder: \"请输入内容...\",\r\n//   MENU_CONF: {\r\n//     uploadImage: {\r\n//       fieldName: \"file\",\r\n//       maxFileSize: 10 * 1024 * 1024, // 10M\r\n//       server: proxy.BASE_API_URL + \"index/uploadX\",\r\n//       headers: {\r\n//         \"Accept-Token\": getTokenAUTH(),\r\n//       },\r\n//       customInsert(res, insertFn) {\r\n//         const url = proxy.IMG_BASE_URL + res.data.url;\r\n//         const alt = res.data.alt\r\n//         const href = res.data.href\r\n//         insertFn(url, alt, href);\r\n//       },\r\n//     },\r\n//   },\r\n// };\r\n\r\n// 组件销毁时，也及时销毁编辑器\r\nonBeforeUnmount(() => {\r\n  // const editor = editorRef.value;\r\n  // if (editor == null) return;\r\n  // editor.destroy();\r\n});\r\n\r\nconst handleCreated = (editor) => {\r\n  // editorRef.value = editor; // 记录 editor 实例，重要！\r\n};\r\n\r\ndefineExpose({ form });\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.demo-form-inline {\r\n  justify-content: flex-start;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.demo-form-inline .el-input {\r\n  --el-input-width: 220px;\r\n}\r\n\r\n.demo-form-inline .el-select {\r\n  --el-select-width: 220px;\r\n}\r\n\r\n/deep/ .el-radio-group {\r\n  width: 220px;\r\n}\r\n\r\n.form-title {\r\n  text-align: left;\r\n  padding-left: 30px;\r\n  margin: 20px auto 10px;\r\n  height: 44px;\r\n  background-color: #f2f2f2;\r\n  border-radius: 5px;\r\n  line-height: 44px;\r\n}\r\n\r\n/deep/ .el-form-item {\r\n  align-items: flex-start;\r\n}\r\n</style>\r\n"], "mappings": "AA4CA,SAASA,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,kBAAkB,QAAQ,eAAe;AAClD,SAASC,eAAe,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAEC,kBAAkB,QAAQ,KAAK;AAC/F,SAASC,YAAY,QAAQ,cAAc;;;;;;;IAE3C,MAAMC,IAAI,GAAGL,GAAG,CAAC;MACfM,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,EAAE;MACVC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,EAAE;MACVC,WAAW,EAAE;IACf,CAAC,CAAC;IACF,MAAMC,KAAK,GAAGC,OAAqB;;IAEnC;IACAV,SAAS,CAAC,MAAM;MACd;MACAH,QAAQ,CAAC,MAAM;QACb;QACAM,IAAI,CAACQ,KAAK,GAAGC,MAAM,CAACC,MAAM,CAACV,IAAI,CAACQ,KAAK,EAAEF,KAAK,CAACK,IAAI,CAAC;MACpD,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,MAAM;MAAEC;IAAM,CAAC,GAAGd,kBAAkB,CAAC,CAAC;;IAEtC;;IAGA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;;IAEA;IACA;;IAEA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACAL,eAAe,CAAC,MAAM;MACpB;MACA;MACA;IAAA,CACD,CAAC;IAEF,MAAMoB,aAAa,GAAIC,MAAM,IAAK;MAChC;IAAA,CACD;IAEDC,QAAY,CAAC;MAAEf;IAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}