{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = [\"src\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_Plus = _resolveComponent(\"Plus\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_upload = _resolveComponent(\"el-upload\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createBlock(_component_el_form, {\n    \"label-width\": \"100px\",\n    inline: true,\n    model: $setup.form,\n    class: \"demo-form-inline\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_form_item, {\n      label: \"标题\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.title,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.form.title = $event),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"金额\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.money,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.form.money = $event),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" <el-form-item label=\\\"集字\\\" required>\\r\\n      <el-input v-model=\\\"form.words\\\" clearable />\\r\\n    </el-form-item> \"), _createVNode(_component_el_form_item, {\n      label: \"奖品类型\",\n      prop: \"type\",\n      rules: [{\n        required: true,\n        message: '请选择奖品类型',\n        trigger: ['change']\n      }]\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_select, {\n        modelValue: $setup.form.type,\n        \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.form.type = $event),\n        placeholder: \"奖品类型\",\n        clearable: \"\"\n      }, {\n        default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.lottoryTypeBEnums, item => {\n          return _openBlock(), _createBlock(_component_el_option, {\n            label: item.label,\n            key: item.label,\n            value: item.value\n          }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n        }), 128 /* KEYED_FRAGMENT */))]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"概率\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.chance,\n        \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.form.chance = $event),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"图片\",\n      prop: \"img\",\n      rules: [{\n        required: true,\n        message: '请上传图片'\n      }]\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_upload, {\n        class: \"upload-demo\",\n        style: {\n          \"width\": \"114px\"\n        },\n        \"show-file-list\": false,\n        drag: \"\",\n        headers: $setup.headers,\n        action: `${$setup.proxy.BASE_API_URL}index/upload`,\n        \"on-success\": $setup.successUpload,\n        \"on-error\": $setup.handleErr,\n        multiple: false\n      }, {\n        default: _withCtx(() => [$setup.form.img ? (_openBlock(), _createElementBlock(\"img\", {\n          key: 0,\n          src: $setup.proxy.IMG_BASE_URL + $setup.form.img,\n          width: \"100%\",\n          class: \"avatar\"\n        }, null, 8 /* PROPS */, _hoisted_1)) : (_openBlock(), _createBlock(_component_el_icon, {\n          key: 1,\n          class: \"avatar-uploader-icon\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_Plus)]),\n          _: 1 /* STABLE */\n        }))]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"headers\", \"action\"])]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\"]);\n}", "map": {"version": 3, "names": ["_createBlock", "_component_el_form", "inline", "model", "$setup", "form", "class", "_createVNode", "_component_el_form_item", "label", "required", "_component_el_input", "title", "$event", "clearable", "money", "_createCommentVNode", "prop", "rules", "_component_el_select", "type", "placeholder", "_createElementBlock", "_Fragment", "_renderList", "lottoryTypeBEnums", "item", "_component_el_option", "key", "value", "chance", "message", "_component_el_upload", "style", "drag", "headers", "action", "proxy", "BASE_API_URL", "successUpload", "handleErr", "multiple", "img", "src", "IMG_BASE_URL", "width", "_component_el_icon", "_component_Plus"], "sources": ["D:\\WorkSpace\\facai7\\facai7_admin\\src\\views\\operationManage\\components\\lottory\\editPop.vue"], "sourcesContent": ["<template>\r\n  <el-form\r\n    label-width=\"100px\"\r\n    :inline=\"true\"\r\n    :model=\"form\"\r\n    class=\"demo-form-inline\"\r\n  >\r\n    <el-form-item label=\"标题\" required>\r\n      <el-input v-model=\"form.title\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"金额\" required>\r\n      <el-input v-model=\"form.money\" clearable />\r\n    </el-form-item>\r\n    <!-- <el-form-item label=\"集字\" required>\r\n      <el-input v-model=\"form.words\" clearable />\r\n    </el-form-item> -->\r\n    <el-form-item\r\n      label=\"奖品类型\"\r\n      prop=\"type\"\r\n      :rules=\"[\r\n        {\r\n          required: true,\r\n          message: '请选择奖品类型',\r\n          trigger: ['change'],\r\n        },\r\n      ]\"\r\n    >\r\n      <el-select v-model=\"form.type\" placeholder=\"奖品类型\" clearable>\r\n        <el-option\r\n          v-for=\"item in lottoryTypeBEnums\"\r\n          :label=\"item.label\"\r\n          :key=\"item.label\"\r\n          :value=\"item.value\"\r\n        />\r\n      </el-select>\r\n    </el-form-item>\r\n    <el-form-item label=\"概率\" required>\r\n      <el-input v-model=\"form.chance\" clearable />\r\n    </el-form-item>\r\n\r\n    <el-form-item\r\n      label=\"图片\"\r\n      prop=\"img\"\r\n      :rules=\"[{ required: true, message: '请上传图片' }]\"\r\n    >\r\n      <el-upload\r\n        class=\"upload-demo\"\r\n        style=\"width: 114px\"\r\n        :show-file-list=\"false\"\r\n        drag\r\n        :headers=\"headers\"\r\n        :action=\"`${proxy.BASE_API_URL}index/upload`\"\r\n        :on-success=\"successUpload\"\r\n        :on-error=\"handleErr\"\r\n        :multiple=\"false\"\r\n      >\r\n        <img v-if=\"form.img\" :src=\"proxy.IMG_BASE_URL + form.img\" width=\"100%\" class=\"avatar\" />\r\n        <el-icon v-else class=\"avatar-uploader-icon\"><Plus /></el-icon\r\n      ></el-upload>\r\n    </el-form-item>\r\n  </el-form>\r\n</template>\r\n\r\n<script setup>\r\nimport { lottoryTypeBEnums, rolesEnums } from \"@/config/enums\";\r\nimport { onBeforeUnmount, nextTick, ref, shallowRef, onMounted, getCurrentInstance } from \"vue\";\r\nimport { getTokenAUTH } from \"@/utils/auth\";\r\n\r\nconst form = ref({\r\n  title: \"\",\r\n  money: \"\",\r\n  // words: \"\",\r\n  type: \"\",\r\n  chance: \"\",\r\n  img: \"\",\r\n});\r\nconst props = defineProps([\"item\"]);\r\n\r\nconst successUpload = (res) => {\r\n  form.value.img = res.data.url;\r\n};\r\n\r\nconst handleErr = (err) => {\r\n  if (err.status == 320) {\r\n    form.value.img = JSON.parse(err.message).data.url;\r\n  }\r\n}\r\n\r\nconst headers = ref({})\r\nonMounted(() => {\r\n  headers.value['Accept-Token'] = getTokenAUTH()\r\n  nextTick(() => {\r\n    form.value = Object.assign(form.value, props.item);\r\n  });\r\n});\r\n\r\nconst { proxy } = getCurrentInstance()\r\n\r\n// 编辑器实例，必须用 shallowRef\r\nconst editorRef = shallowRef();\r\n\r\n// 内容 HTML\r\nconst valueHtml = ref(\"<p>hello</p>\");\r\nconst mode = ref(\"default\");\r\n\r\nconst toolbarConfig = {};\r\nconst editorConfig =  {\r\n  placeholder: \"请输入内容...\",\r\n  MENU_CONF: {\r\n    uploadImage: {\r\n      fieldName: \"file\",\r\n      maxFileSize: 10 * 1024 * 1024, // 10M\r\n      server: proxy.BASE_API_URL + \"index/uploadX\",\r\n      headers: {\r\n        \"Accept-Token\": getTokenAUTH(),\r\n      },\r\n      customInsert(res, insertFn) {\r\n        const url = proxy.IMG_BASE_URL + res.data.url;\r\n        const alt = res.data.alt\r\n        const href = res.data.href\r\n        insertFn(url, alt, href);\r\n      },\r\n    },\r\n  },\r\n};\r\n\r\n// 组件销毁时，也及时销毁编辑器\r\nonBeforeUnmount(() => {\r\n  const editor = editorRef.value;\r\n  if (editor == null) return;\r\n  editor.destroy();\r\n});\r\n\r\nconst handleCreated = (editor) => {\r\n  editorRef.value = editor; // 记录 editor 实例，重要！\r\n};\r\n\r\ndefineExpose({ form });\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.demo-form-inline {\r\n  justify-content: flex-start;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.demo-form-inline .el-input {\r\n  --el-input-width: 220px;\r\n}\r\n\r\n.demo-form-inline .el-select {\r\n  --el-select-width: 220px;\r\n}\r\n\r\n/deep/ .el-radio-group {\r\n  width: 220px;\r\n}\r\n\r\n.form-title {\r\n  text-align: left;\r\n  padding-left: 30px;\r\n  margin: 20px auto 10px;\r\n  height: 44px;\r\n  background-color: #f2f2f2;\r\n  border-radius: 5px;\r\n  line-height: 44px;\r\n}\r\n\r\n/deep/ .el-form-item {\r\n  align-items: flex-start;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;uBACEA,YAAA,CA2DUC,kBAAA;IA1DR,aAAW,EAAC,OAAO;IAClBC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IACZC,KAAK,EAAC;;sBAEN,MAEe,CAFfC,YAAA,CAEeC,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,QAAQ,EAAR;;wBACvB,MAA2C,CAA3CH,YAAA,CAA2CI,mBAAA;oBAAxBP,MAAA,CAAAC,IAAI,CAACO,KAAK;mEAAVR,MAAA,CAAAC,IAAI,CAACO,KAAK,GAAAC,MAAA;QAAEC,SAAS,EAAT;;;QAEjCP,YAAA,CAEeC,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,QAAQ,EAAR;;wBACvB,MAA2C,CAA3CH,YAAA,CAA2CI,mBAAA;oBAAxBP,MAAA,CAAAC,IAAI,CAACU,KAAK;mEAAVX,MAAA,CAAAC,IAAI,CAACU,KAAK,GAAAF,MAAA;QAAEC,SAAS,EAAT;;;QAEjCE,mBAAA,wHAEmB,EACnBT,YAAA,CAmBeC,uBAAA;MAlBbC,KAAK,EAAC,MAAM;MACZQ,IAAI,EAAC,MAAM;MACVC,KAAK,EAAE,C;;;;;;wBAQR,MAOY,CAPZX,YAAA,CAOYY,oBAAA;oBAPQf,MAAA,CAAAC,IAAI,CAACe,IAAI;mEAAThB,MAAA,CAAAC,IAAI,CAACe,IAAI,GAAAP,MAAA;QAAEQ,WAAW,EAAC,MAAM;QAACP,SAAS,EAAT;;0BAE9C,MAAiC,E,kBADnCQ,mBAAA,CAKEC,SAAA,QAAAC,WAAA,CAJepB,MAAA,CAAAqB,iBAAiB,EAAzBC,IAAI;+BADb1B,YAAA,CAKE2B,oBAAA;YAHClB,KAAK,EAAEiB,IAAI,CAACjB,KAAK;YACjBmB,GAAG,EAAEF,IAAI,CAACjB,KAAK;YACfoB,KAAK,EAAEH,IAAI,CAACG;;;;;;QAInBtB,YAAA,CAEeC,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,QAAQ,EAAR;;wBACvB,MAA4C,CAA5CH,YAAA,CAA4CI,mBAAA;oBAAzBP,MAAA,CAAAC,IAAI,CAACyB,MAAM;mEAAX1B,MAAA,CAAAC,IAAI,CAACyB,MAAM,GAAAjB,MAAA;QAAEC,SAAS,EAAT;;;QAGlCP,YAAA,CAmBeC,uBAAA;MAlBbC,KAAK,EAAC,IAAI;MACVQ,IAAI,EAAC,KAAK;MACTC,KAAK,EAAE;QAAAR,QAAA;QAAAqB,OAAA;MAAA;;wBAER,MAaa,CAbbxB,YAAA,CAaayB,oBAAA;QAZX1B,KAAK,EAAC,aAAa;QACnB2B,KAAoB,EAApB;UAAA;QAAA,CAAoB;QACnB,gBAAc,EAAE,KAAK;QACtBC,IAAI,EAAJ,EAAI;QACHC,OAAO,EAAE/B,MAAA,CAAA+B,OAAO;QAChBC,MAAM,KAAKhC,MAAA,CAAAiC,KAAK,CAACC,YAAY;QAC7B,YAAU,EAAElC,MAAA,CAAAmC,aAAa;QACzB,UAAQ,EAAEnC,MAAA,CAAAoC,SAAS;QACnBC,QAAQ,EAAE;;0BAPb,MAME,CAGWrC,MAAA,CAAAC,IAAI,CAACqC,GAAG,I,cAAnBpB,mBAAA,CAAwF;;UAAlEqB,GAAG,EAAEvC,MAAA,CAAAiC,KAAK,CAACO,YAAY,GAAGxC,MAAA,CAAAC,IAAI,CAACqC,GAAG;UAAEG,KAAK,EAAC,MAAM;UAACvC,KAAK,EAAC;8DAC7EN,YAAA,CAAuE8C,kBAAA;;UAAvDxC,KAAK,EAAC;;4BAAuB,MAAQ,CAARC,YAAA,CAAQwC,eAAA,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}