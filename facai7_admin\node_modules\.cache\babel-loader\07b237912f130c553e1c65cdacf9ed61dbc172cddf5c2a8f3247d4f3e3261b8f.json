{"ast": null, "code": "import { getCurrentInstance, useAttrs, useSlots } from 'vue';\nimport dayjs from 'dayjs';\nimport { isFunction } from '@vue/shared';\nconst useShortcut = lang => {\n  const {\n    emit\n  } = getCurrentInstance();\n  const attrs = useAttrs();\n  const slots = useSlots();\n  const handleShortcutClick = shortcut => {\n    const shortcutValues = isFunction(shortcut.value) ? shortcut.value() : shortcut.value;\n    if (shortcutValues) {\n      emit(\"pick\", [dayjs(shortcutValues[0]).locale(lang.value), dayjs(shortcutValues[1]).locale(lang.value)]);\n      return;\n    }\n    if (shortcut.onClick) {\n      shortcut.onClick({\n        attrs,\n        slots,\n        emit\n      });\n    }\n  };\n  return handleShortcutClick;\n};\nexport { useShortcut };", "map": {"version": 3, "names": ["useShortcut", "lang", "emit", "getCurrentInstance", "attrs", "useAttrs", "slots", "useSlots", "handleShortcutClick", "shortcut", "shortcutValues", "isFunction", "value", "dayjs", "locale", "onClick"], "sources": ["../../../../../../../packages/components/date-picker/src/composables/use-shortcut.ts"], "sourcesContent": ["import { getCurrentInstance, useAttrs, useSlots } from 'vue'\nimport dayjs from 'dayjs'\nimport { isFunction } from '@element-plus/utils'\n\nimport type { SetupContext } from 'vue'\nimport type { useLocale } from '@element-plus/hooks'\nimport type { RangePickerSharedEmits } from '../props/shared'\n\n// FIXME: extract this to `date-picker.ts`\nexport type Shortcut = {\n  text: string\n  value: [Date, Date] | (() => [Date, Date])\n  onClick?: (ctx: Omit<SetupContext<RangePickerSharedEmits>, 'expose'>) => void\n}\n\nexport const useShortcut = (lang: ReturnType<typeof useLocale>['lang']) => {\n  const { emit } = getCurrentInstance()!\n  const attrs = useAttrs()\n  const slots = useSlots()\n\n  const handleShortcutClick = (shortcut: Shortcut) => {\n    const shortcutValues = isFunction(shortcut.value)\n      ? shortcut.value()\n      : shortcut.value\n\n    if (shortcutValues) {\n      emit('pick', [\n        dayjs(shortcutValues[0]).locale(lang.value),\n        dayjs(shortcutValues[1]).locale(lang.value),\n      ])\n      return\n    }\n    if (shortcut.onClick) {\n      shortcut.onClick({\n        attrs,\n        slots,\n        emit,\n      })\n    }\n  }\n\n  return handleShortcutClick\n}\n"], "mappings": ";;;AAGY,MAACA,WAAW,GAAIC,IAAI,IAAK;EACnC,MAAM;IAAEC;EAAI,CAAE,GAAGC,kBAAkB,EAAE;EACrC,MAAMC,KAAK,GAAGC,QAAQ,EAAE;EACxB,MAAMC,KAAK,GAAGC,QAAQ,EAAE;EACxB,MAAMC,mBAAmB,GAAIC,QAAQ,IAAK;IACxC,MAAMC,cAAc,GAAGC,UAAU,CAACF,QAAQ,CAACG,KAAK,CAAC,GAAGH,QAAQ,CAACG,KAAK,EAAE,GAAGH,QAAQ,CAACG,KAAK;IACrF,IAAIF,cAAc,EAAE;MAClBR,IAAI,CAAC,MAAM,EAAE,CACXW,KAAK,CAACH,cAAc,CAAC,CAAC,CAAC,CAAC,CAACI,MAAM,CAACb,IAAI,CAACW,KAAK,CAAC,EAC3CC,KAAK,CAACH,cAAc,CAAC,CAAC,CAAC,CAAC,CAACI,MAAM,CAACb,IAAI,CAACW,KAAK,CAAC,CAC5C,CAAC;MACF;IACN;IACI,IAAIH,QAAQ,CAACM,OAAO,EAAE;MACpBN,QAAQ,CAACM,OAAO,CAAC;QACfX,KAAK;QACLE,KAAK;QACLJ;MACR,CAAO,CAAC;IACR;EACA,CAAG;EACD,OAAOM,mBAAmB;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}