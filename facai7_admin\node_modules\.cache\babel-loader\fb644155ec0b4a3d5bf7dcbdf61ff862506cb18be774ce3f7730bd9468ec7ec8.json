{"ast": null, "code": "import { rolesEnums } from \"@/config/enums\";\nimport { onBeforeUnmount, nextTick, ref, shallowRef, onMounted, getCurrentInstance } from \"vue\";\nimport fileUploadHoook from \"@/hooks/fileUpload\";\nimport { htmlDecodeByRegExp } from \"@/utils/utils\";\nimport { getTokenAUTH } from \"@/utils/auth\";\nexport default {\n  __name: 'editPop',\n  props: [\"item\"],\n  setup(__props, {\n    expose: __expose\n  }) {\n    const form = ref({\n      title: \"\",\n      desc: \"\"\n    });\n    const props = __props;\n    onMounted(() => {\n      nextTick(() => {\n        form.value = Object.assign(form.value, props.item);\n      });\n    });\n    __expose({\n      form\n    });\n    const __returned__ = {\n      form,\n      props,\n      get rolesEnums() {\n        return rolesEnums;\n      },\n      onBeforeUnmount,\n      nextTick,\n      ref,\n      shallowRef,\n      onMounted,\n      getCurrentInstance,\n      get fileUploadHoook() {\n        return fileUploadHoook;\n      },\n      get htmlDecodeByRegExp() {\n        return htmlDecodeByRegExp;\n      },\n      get getTokenAUTH() {\n        return getTokenAUTH;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["rolesEnums", "onBeforeUnmount", "nextTick", "ref", "shallowRef", "onMounted", "getCurrentInstance", "fileUploadHoook", "htmlDecodeByRegExp", "getTokenAUTH", "form", "title", "desc", "props", "__props", "value", "Object", "assign", "item", "__expose"], "sources": ["D:/WorkSpace/facai7/facai7_admin/src/views/operationManage/components/questionList/editPop.vue"], "sourcesContent": ["<template>\r\n  <el-form\r\n    label-width=\"100px\"\r\n    :inline=\"true\"\r\n    :model=\"form\"\r\n    class=\"demo-form-inline\"\r\n  >\r\n    <el-form-item label=\"问题\" required>\r\n      <el-input v-model=\"form.title\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"答复\" required>\r\n      <el-input v-model=\"form.desc\" clearable />\r\n    </el-form-item>\r\n  </el-form>\r\n</template>\r\n\r\n<script setup>\r\nimport { rolesEnums } from \"@/config/enums\";\r\nimport {\r\n  onBeforeUnmount,\r\n  nextTick,\r\n  ref,\r\n  shallowRef,\r\n  onMounted,\r\n  getCurrentInstance,\r\n} from \"vue\";\r\nimport fileUploadHoook from \"@/hooks/fileUpload\";\r\nimport { htmlDecodeByRegExp } from \"@/utils/utils\";\r\nimport { getTokenAUTH } from \"@/utils/auth\";\r\n\r\nconst form = ref({\r\n  title: \"\",\r\n  desc: \"\",\r\n});\r\nconst props = defineProps([\"item\"]);\r\n\r\nonMounted(() => {\r\n  nextTick(() => {\r\n    form.value = Object.assign(form.value, props.item);\r\n\r\n  });\r\n});\r\n\r\n\r\n\r\ndefineExpose({ form });\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.demo-form-inline {\r\n  justify-content: flex-start;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.demo-form-inline .el-input {\r\n  --el-input-width: 220px;\r\n}\r\n\r\n.demo-form-inline .el-select {\r\n  --el-select-width: 220px;\r\n}\r\n\r\n/deep/ .el-radio-group {\r\n  width: 220px;\r\n}\r\n\r\n.form-title {\r\n  text-align: left;\r\n  padding-left: 30px;\r\n  margin: 20px auto 10px;\r\n  height: 44px;\r\n  background-color: #f2f2f2;\r\n  border-radius: 5px;\r\n  line-height: 44px;\r\n}\r\n\r\n/deep/ .el-form-item {\r\n  align-items: flex-start;\r\n}\r\n</style>\r\n"], "mappings": "AAiBA,SAASA,UAAU,QAAQ,gBAAgB;AAC3C,SACEC,eAAe,EACfC,QAAQ,EACRC,GAAG,EACHC,UAAU,EACVC,SAAS,EACTC,kBAAkB,QACb,KAAK;AACZ,OAAOC,eAAe,MAAM,oBAAoB;AAChD,SAASC,kBAAkB,QAAQ,eAAe;AAClD,SAASC,YAAY,QAAQ,cAAc;;;;;;;IAE3C,MAAMC,IAAI,GAAGP,GAAG,CAAC;MACfQ,KAAK,EAAE,EAAE;MACTC,IAAI,EAAE;IACR,CAAC,CAAC;IACF,MAAMC,KAAK,GAAGC,OAAqB;IAEnCT,SAAS,CAAC,MAAM;MACdH,QAAQ,CAAC,MAAM;QACbQ,IAAI,CAACK,KAAK,GAAGC,MAAM,CAACC,MAAM,CAACP,IAAI,CAACK,KAAK,EAAEF,KAAK,CAACK,IAAI,CAAC;MAEpD,CAAC,CAAC;IACJ,CAAC,CAAC;IAIFC,QAAY,CAAC;MAAET;IAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}