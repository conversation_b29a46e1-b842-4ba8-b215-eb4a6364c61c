{"ast": null, "code": "import { nextTick, onMounted, ref, getCurrentInstance } from \"vue\";\n// import { rolesEnums } from \"@/config/enums\";\n// import { getTokenAUTH } from \"@/utils/auth\";\n\nexport default {\n  __name: 'editPop',\n  props: [\"item\"],\n  setup(__props, {\n    expose: __expose\n  }) {\n    const form = ref({\n      title: \"\"\n      // img: \"\",\n    });\n    // const { proxy } = getCurrentInstance();\n    const props = __props;\n\n    // const headers = ref({})\n    onMounted(() => {\n      // headers.value['Accept-Token'] = getTokenAUTH()\n      nextTick(() => {\n        form.value = Object.assign(form, props.item);\n      });\n    });\n\n    // const successUpload = (res) => {\n    //   form.value.img = res.data.url;\n    // };\n\n    const handleErr = err => {\n      if (err.status == 320) {\n        form.value.img = JSON.parse(err.message).data.url;\n      }\n    };\n    __expose({\n      form\n    });\n    const __returned__ = {\n      form,\n      props,\n      handleErr,\n      nextTick,\n      onMounted,\n      ref,\n      getCurrentInstance\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["nextTick", "onMounted", "ref", "getCurrentInstance", "form", "title", "props", "__props", "value", "Object", "assign", "item", "handleErr", "err", "status", "img", "JSON", "parse", "message", "data", "url", "__expose"], "sources": ["D:/WorkSpace/facai7/facai7_admin/src/views/payments/components/bankList/editPop.vue"], "sourcesContent": ["<template>\r\n  <el-form\r\n    label-width=\"100px\"\r\n    :inline=\"true\"\r\n    :model=\"form\"\r\n    class=\"demo-form-inline\"\r\n  >\r\n    <el-form-item label=\"银行名称\" required>\r\n      <el-input v-model=\"form.title\" clearable />\r\n    </el-form-item>\r\n\r\n\r\n    <!-- <el-form-item\r\n      label=\"银行logo\"\r\n      prop=\"img\"\r\n      :rules=\"[{ required: true, message: '请上传图片' }]\"\r\n    >\r\n      <el-upload\r\n        class=\"upload-demo\"\r\n        style=\"width: 114px\"\r\n        :show-file-list=\"false\"\r\n        drag\r\n        :headers=\"headers\"\r\n        :action=\"`${proxy.BASE_API_URL}index/upload`\"\r\n        :on-success=\"successUpload\"\r\n        :on-error=\"handleErr\"\r\n        :multiple=\"false\"\r\n      >\r\n        <img\r\n          v-if=\"form.img\"\r\n          :src=\"proxy.IMG_BASE_URL + form.img\"\r\n          width=\"100%\"\r\n          class=\"avatar\" />\r\n        <el-icon v-else class=\"avatar-uploader-icon\"><Plus /></el-icon\r\n      ></el-upload>\r\n    </el-form-item> -->\r\n  </el-form>\r\n</template>\r\n\r\n<script setup>\r\nimport { nextTick, onMounted, ref, getCurrentInstance } from \"vue\";\r\n// import { rolesEnums } from \"@/config/enums\";\r\n// import { getTokenAUTH } from \"@/utils/auth\";\r\n\r\nconst form = ref({\r\n  title: \"\",\r\n  // img: \"\",\r\n});\r\n// const { proxy } = getCurrentInstance();\r\nconst props = defineProps([\"item\"]);\r\n\r\n// const headers = ref({})\r\nonMounted(() => {\r\n  // headers.value['Accept-Token'] = getTokenAUTH()\r\n  nextTick(() => {\r\n    form.value = Object.assign(form, props.item);\r\n  });\r\n});\r\n\r\n// const successUpload = (res) => {\r\n//   form.value.img = res.data.url;\r\n// };\r\n\r\nconst handleErr = (err) => {\r\n  if (err.status == 320) {\r\n    form.value.img = JSON.parse(err.message).data.url;\r\n  }\r\n}\r\n\r\ndefineExpose({ form });\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.demo-form-inline {\r\n  justify-content: flex-start;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n.demo-form-inline .el-input {\r\n  --el-input-width: 220px;\r\n}\r\n\r\n.demo-form-inline .el-select {\r\n  --el-select-width: 220px;\r\n}\r\n/deep/ .el-radio-group {\r\n  width: 220px;\r\n}\r\n.form-title {\r\n  text-align: left;\r\n  padding-left: 30px;\r\n  margin: 20px auto 10px;\r\n  height: 44px;\r\n  background-color: #f2f2f2;\r\n  border-radius: 5px;\r\n  line-height: 44px;\r\n}\r\n/deep/ .el-form-item {\r\n  align-items: flex-start;\r\n}\r\n</style>\r\n"], "mappings": "AAwCA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,GAAG,EAAEC,kBAAkB,QAAQ,KAAK;AAClE;AACA;;;;;;;;IAEA,MAAMC,IAAI,GAAGF,GAAG,CAAC;MACfG,KAAK,EAAE;MACP;IACF,CAAC,CAAC;IACF;IACA,MAAMC,KAAK,GAAGC,OAAqB;;IAEnC;IACAN,SAAS,CAAC,MAAM;MACd;MACAD,QAAQ,CAAC,MAAM;QACbI,IAAI,CAACI,KAAK,GAAGC,MAAM,CAACC,MAAM,CAACN,IAAI,EAAEE,KAAK,CAACK,IAAI,CAAC;MAC9C,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA;IACA;;IAEA,MAAMC,SAAS,GAAIC,GAAG,IAAK;MACzB,IAAIA,GAAG,CAACC,MAAM,IAAI,GAAG,EAAE;QACrBV,IAAI,CAACI,KAAK,CAACO,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACJ,GAAG,CAACK,OAAO,CAAC,CAACC,IAAI,CAACC,GAAG;MACnD;IACF,CAAC;IAEDC,QAAY,CAAC;MAAEjB;IAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}