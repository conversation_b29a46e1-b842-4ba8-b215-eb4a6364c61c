{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode } from \"vue\";\nconst _hoisted_1 = [\"src\"];\nconst _hoisted_2 = {\n  style: {\n    \"border\": \"1px solid #ccc\"\n  }\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_date_picker = _resolveComponent(\"el-date-picker\");\n  const _component_Plus = _resolveComponent(\"Plus\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_upload = _resolveComponent(\"el-upload\");\n  const _component_Toolbar = _resolveComponent(\"Toolbar\");\n  const _component_Editor = _resolveComponent(\"Editor\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createBlock(_component_el_form, {\n    \"label-width\": \"100px\",\n    inline: true,\n    model: $setup.form,\n    class: \"demo-form-inline\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_form_item, {\n      label: \"标题\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.title,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.form.title = $event),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"描述\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.desc,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.form.desc = $event),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"文章唯一编码\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.code,\n        \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.form.code = $event),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"文章类型\",\n      prop: \"class_id\",\n      rules: [{\n        required: true,\n        message: '请选择文章类型',\n        trigger: ['change']\n      }]\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_select, {\n        modelValue: $setup.form.class_id,\n        \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.form.class_id = $event),\n        placeholder: \"文章类型\",\n        clearable: \"\"\n      }, {\n        default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.typesEnum, item => {\n          return _openBlock(), _createBlock(_component_el_option, {\n            label: item.title,\n            key: item.title,\n            value: item.id\n          }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n        }), 128 /* KEYED_FRAGMENT */))]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"发布时间\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_date_picker, {\n        modelValue: $setup.form.release_time,\n        \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.form.release_time = $event),\n        type: \"datetime\",\n        \"value-format\": \"YYYY-MM-DD HH:mm:ss\",\n        placeholder: \"发布时间\",\n        style: {\n          \"width\": \"100%\"\n        }\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"图片\",\n      prop: \"img\",\n      rules: [{\n        required: true,\n        message: '请上传图片'\n      }]\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_upload, {\n        class: \"upload-demo\",\n        style: {\n          \"width\": \"114px\"\n        },\n        \"show-file-list\": false,\n        drag: \"\",\n        headers: $setup.headers,\n        action: `${$setup.proxy.BASE_API_URL}index/upload`,\n        \"on-success\": $setup.successUpload,\n        \"on-error\": $setup.handleErr,\n        multiple: false\n      }, {\n        default: _withCtx(() => [$setup.form.img ? (_openBlock(), _createElementBlock(\"img\", {\n          key: 0,\n          src: $setup.proxy.IMG_BASE_URL + $setup.form.img,\n          width: \"100%\",\n          class: \"avatar\"\n        }, null, 8 /* PROPS */, _hoisted_1)) : (_openBlock(), _createBlock(_component_el_icon, {\n          key: 1,\n          class: \"avatar-uploader-icon\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_Plus)]),\n          _: 1 /* STABLE */\n        }))]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"headers\", \"action\"])]),\n      _: 1 /* STABLE */\n    }), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_Toolbar, {\n      style: {\n        \"border-bottom\": \"1px solid #ccc\"\n      },\n      editor: $setup.editorRef,\n      defaultConfig: $setup.toolbarConfig,\n      mode: $setup.mode\n    }, null, 8 /* PROPS */, [\"editor\", \"mode\"]), _createVNode(_component_Editor, {\n      style: {\n        \"height\": \"500px\",\n        \"overflow-y\": \"hidden\"\n      },\n      modelValue: $setup.form.content,\n      \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.form.content = $event),\n      defaultConfig: $setup.editorConfig,\n      mode: $setup.mode,\n      onOnCreated: $setup.handleCreated\n    }, null, 8 /* PROPS */, [\"modelValue\", \"mode\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\"]);\n}", "map": {"version": 3, "names": ["style", "_createBlock", "_component_el_form", "inline", "model", "$setup", "form", "class", "_createVNode", "_component_el_form_item", "label", "required", "_component_el_input", "title", "$event", "clearable", "desc", "code", "prop", "rules", "_component_el_select", "class_id", "placeholder", "_createElementBlock", "_Fragment", "_renderList", "typesEnum", "item", "_component_el_option", "key", "value", "id", "_component_el_date_picker", "release_time", "type", "message", "_component_el_upload", "drag", "headers", "action", "proxy", "BASE_API_URL", "successUpload", "handleErr", "multiple", "img", "src", "IMG_BASE_URL", "width", "_component_el_icon", "_component_Plus", "_createElementVNode", "_hoisted_2", "_component_Toolbar", "editor", "editor<PERSON><PERSON>", "defaultConfig", "toolbarConfig", "mode", "_component_Editor", "content", "editorConfig", "onOnCreated", "handleCreated"], "sources": ["D:\\WorkSpace\\facai7\\facai7_admin\\src\\views\\operationManage\\components\\newsList\\editPop.vue"], "sourcesContent": ["<template>\r\n  <el-form\r\n    label-width=\"100px\"\r\n    :inline=\"true\"\r\n    :model=\"form\"\r\n    class=\"demo-form-inline\"\r\n  >\r\n    <el-form-item label=\"标题\" required>\r\n      <el-input v-model=\"form.title\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"描述\" required>\r\n      <el-input v-model=\"form.desc\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"文章唯一编码\" required>\r\n      <el-input v-model=\"form.code\" clearable />\r\n    </el-form-item>\r\n\r\n    <el-form-item\r\n      label=\"文章类型\"\r\n      prop=\"class_id\"\r\n      :rules=\"[\r\n        {\r\n          required: true,\r\n          message: '请选择文章类型',\r\n          trigger: ['change'],\r\n        },\r\n      ]\"\r\n    >\r\n      <el-select v-model=\"form.class_id\" placeholder=\"文章类型\" clearable>\r\n        <el-option\r\n          v-for=\"item in typesEnum\"\r\n          :label=\"item.title\"\r\n          :key=\"item.title\"\r\n          :value=\"item.id\"\r\n        />\r\n      </el-select>\r\n    </el-form-item>\r\n    <el-form-item label=\"发布时间\" required>\r\n      <el-date-picker\r\n        v-model=\"form.release_time\"\r\n        type=\"datetime\"\r\n        value-format=\"YYYY-MM-DD HH:mm:ss\"\r\n        placeholder=\"发布时间\"\r\n        style=\"width: 100%\"\r\n      />\r\n    </el-form-item>\r\n    <el-form-item\r\n      label=\"图片\"\r\n      prop=\"img\"\r\n      :rules=\"[{ required: true, message: '请上传图片' }]\"\r\n    >\r\n      <el-upload\r\n        class=\"upload-demo\"\r\n        style=\"width: 114px\"\r\n        :show-file-list=\"false\"\r\n        drag\r\n        :headers=\"headers\"\r\n        :action=\"`${proxy.BASE_API_URL}index/upload`\"\r\n        :on-success=\"successUpload\"\r\n        :on-error=\"handleErr\"\r\n        :multiple=\"false\"\r\n      >\r\n        <img\r\n          v-if=\"form.img\"\r\n          :src=\"proxy.IMG_BASE_URL + form.img\"\r\n          width=\"100%\"\r\n          class=\"avatar\" />\r\n        <el-icon v-else class=\"avatar-uploader-icon\"><Plus /></el-icon\r\n      ></el-upload>\r\n    </el-form-item>\r\n    <div style=\"border: 1px solid #ccc\">\r\n      <Toolbar\r\n        style=\"border-bottom: 1px solid #ccc\"\r\n        :editor=\"editorRef\"\r\n        :defaultConfig=\"toolbarConfig\"\r\n        :mode=\"mode\"\r\n      />\r\n      <Editor\r\n        style=\"height: 500px; overflow-y: hidden\"\r\n        v-model=\"form.content\"\r\n        :defaultConfig=\"editorConfig\"\r\n        :mode=\"mode\"\r\n        @onCreated=\"handleCreated\"\r\n      />\r\n    </div>\r\n  </el-form>\r\n</template>\r\n\r\n<script setup>\r\nimport { rolesEnums } from \"@/config/enums\";\r\nimport {\r\n  onBeforeUnmount,\r\n  nextTick,\r\n  ref,\r\n  shallowRef,\r\n  onMounted,\r\n  getCurrentInstance,\r\n} from \"vue\";\r\nimport fileUploadHoook from \"@/hooks/fileUpload\";\r\nimport { htmlDecodeByRegExp } from \"@/utils/utils\";\r\nimport { getTokenAUTH } from \"@/utils/auth\";\r\n\r\nconst form = ref({\r\n  title: \"\",\r\n  content: \"\",\r\n  class_id: \"\",\r\n  release_time: \"\",\r\n  code: \"\",\r\n  desc: \"\",\r\n  img: \"\",\r\n});\r\nconst props = defineProps([\"item\"]);\r\n\r\nconst headers = ref({})\r\nonMounted(() => {\r\n  headers.value['Accept-Token'] = getTokenAUTH()\r\n  nextTick(() => {\r\n    props.item.content = htmlDecodeByRegExp(props.item.content);\r\n    form.value = Object.assign(form.value, props.item);\r\n\r\n  });\r\n  getTYpesEnum();\r\n});\r\n\r\nconst { proxy } = getCurrentInstance();\r\nconst typesEnum = ref([]);\r\n\r\nconst getTYpesEnum = async () => {\r\n  const res = await proxy.$http({\r\n    method: \"get\",\r\n    url: \"/Article/getArticleClassLists\",\r\n  });\r\n  if (res.code == 0) {\r\n    typesEnum.value = res.data.data;\r\n  }\r\n};\r\n\r\nconst successUpload = (res) => {\r\n  form.value.img = res.data.url;\r\n};\r\n\r\nconst handleErr = (err) => {\r\n  if (err.status == 320) {\r\n    form.value.img = JSON.parse(err.message).data.url;\r\n  }\r\n}\r\n\r\n// 编辑器实例，必须用 shallowRef\r\nconst editorRef = shallowRef();\r\n\r\n// 内容 HTML\r\nconst valueHtml = ref(\"<p>hello</p>\");\r\nconst mode = ref(\"default\");\r\n\r\nconst toolbarConfig = {};\r\nconst editorConfig = {\r\n  placeholder: \"请输入内容...\",\r\n  MENU_CONF: {\r\n    uploadImage: {\r\n      fieldName: \"file\",\r\n      maxFileSize: 10 * 1024 * 1024, // 10M\r\n      server: proxy.BASE_API_URL + \"index/uploadX\",\r\n      headers: {\r\n        \"Accept-Token\": getTokenAUTH(),\r\n      },\r\n      customInsert(res, insertFn) {\r\n        console.log(res)\r\n        const url = proxy.IMG_BASE_URL + res.data.url;\r\n        const alt = res.data.alt\r\n        const href = res.data.href\r\n        insertFn(url, alt, href);\r\n      },\r\n      onError(file, err, res) {\r\n        console.log(`${file.name} 上传出错`, err, res)\r\n      }\r\n    },\r\n  },\r\n};\r\n\r\n// 组件销毁时，也及时销毁编辑器\r\nonBeforeUnmount(() => {\r\n  const editor = editorRef.value;\r\n  if (editor == null) return;\r\n  editor.destroy();\r\n});\r\n\r\nconst handleCreated = (editor) => {\r\n  editorRef.value = editor; // 记录 editor 实例，重要！\r\n};\r\n\r\ndefineExpose({ form });\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.demo-form-inline {\r\n  justify-content: flex-start;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.demo-form-inline .el-input {\r\n  --el-input-width: 220px;\r\n}\r\n\r\n.demo-form-inline .el-select {\r\n  --el-select-width: 220px;\r\n}\r\n\r\n/deep/ .el-radio-group {\r\n  width: 220px;\r\n}\r\n\r\n.form-title {\r\n  text-align: left;\r\n  padding-left: 30px;\r\n  margin: 20px auto 10px;\r\n  height: 44px;\r\n  background-color: #f2f2f2;\r\n  border-radius: 5px;\r\n  line-height: 44px;\r\n}\r\n\r\n/deep/ .el-form-item {\r\n  align-items: flex-start;\r\n}\r\n</style>\r\n"], "mappings": ";;;EAsESA,KAA8B,EAA9B;IAAA;EAAA;AAA8B;;;;;;;;;;;;;uBArErCC,YAAA,CAoFUC,kBAAA;IAnFR,aAAW,EAAC,OAAO;IAClBC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IACZC,KAAK,EAAC;;sBAEN,MAEe,CAFfC,YAAA,CAEeC,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,QAAQ,EAAR;;wBACvB,MAA2C,CAA3CH,YAAA,CAA2CI,mBAAA;oBAAxBP,MAAA,CAAAC,IAAI,CAACO,KAAK;mEAAVR,MAAA,CAAAC,IAAI,CAACO,KAAK,GAAAC,MAAA;QAAEC,SAAS,EAAT;;;QAEjCP,YAAA,CAEeC,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,QAAQ,EAAR;;wBACvB,MAA0C,CAA1CH,YAAA,CAA0CI,mBAAA;oBAAvBP,MAAA,CAAAC,IAAI,CAACU,IAAI;mEAATX,MAAA,CAAAC,IAAI,CAACU,IAAI,GAAAF,MAAA;QAAEC,SAAS,EAAT;;;QAEhCP,YAAA,CAEeC,uBAAA;MAFDC,KAAK,EAAC,QAAQ;MAACC,QAAQ,EAAR;;wBAC3B,MAA0C,CAA1CH,YAAA,CAA0CI,mBAAA;oBAAvBP,MAAA,CAAAC,IAAI,CAACW,IAAI;mEAATZ,MAAA,CAAAC,IAAI,CAACW,IAAI,GAAAH,MAAA;QAAEC,SAAS,EAAT;;;QAGhCP,YAAA,CAmBeC,uBAAA;MAlBbC,KAAK,EAAC,MAAM;MACZQ,IAAI,EAAC,UAAU;MACdC,KAAK,EAAE,C;;;;;;wBAQR,MAOY,CAPZX,YAAA,CAOYY,oBAAA;oBAPQf,MAAA,CAAAC,IAAI,CAACe,QAAQ;mEAAbhB,MAAA,CAAAC,IAAI,CAACe,QAAQ,GAAAP,MAAA;QAAEQ,WAAW,EAAC,MAAM;QAACP,SAAS,EAAT;;0BAElD,MAAyB,E,kBAD3BQ,mBAAA,CAKEC,SAAA,QAAAC,WAAA,CAJepB,MAAA,CAAAqB,SAAS,EAAjBC,IAAI;+BADb1B,YAAA,CAKE2B,oBAAA;YAHClB,KAAK,EAAEiB,IAAI,CAACd,KAAK;YACjBgB,GAAG,EAAEF,IAAI,CAACd,KAAK;YACfiB,KAAK,EAAEH,IAAI,CAACI;;;;;;QAInBvB,YAAA,CAQeC,uBAAA;MARDC,KAAK,EAAC,MAAM;MAACC,QAAQ,EAAR;;wBACzB,MAME,CANFH,YAAA,CAMEwB,yBAAA;oBALS3B,MAAA,CAAAC,IAAI,CAAC2B,YAAY;mEAAjB5B,MAAA,CAAAC,IAAI,CAAC2B,YAAY,GAAAnB,MAAA;QAC1BoB,IAAI,EAAC,UAAU;QACf,cAAY,EAAC,qBAAqB;QAClCZ,WAAW,EAAC,MAAM;QAClBtB,KAAmB,EAAnB;UAAA;QAAA;;;QAGJQ,YAAA,CAuBeC,uBAAA;MAtBbC,KAAK,EAAC,IAAI;MACVQ,IAAI,EAAC,KAAK;MACTC,KAAK,EAAE;QAAAR,QAAA;QAAAwB,OAAA;MAAA;;wBAER,MAiBa,CAjBb3B,YAAA,CAiBa4B,oBAAA;QAhBX7B,KAAK,EAAC,aAAa;QACnBP,KAAoB,EAApB;UAAA;QAAA,CAAoB;QACnB,gBAAc,EAAE,KAAK;QACtBqC,IAAI,EAAJ,EAAI;QACHC,OAAO,EAAEjC,MAAA,CAAAiC,OAAO;QAChBC,MAAM,KAAKlC,MAAA,CAAAmC,KAAK,CAACC,YAAY;QAC7B,YAAU,EAAEpC,MAAA,CAAAqC,aAAa;QACzB,UAAQ,EAAErC,MAAA,CAAAsC,SAAS;QACnBC,QAAQ,EAAE;;0BALjB,MAOS,CACKvC,MAAA,CAAAC,IAAI,CAACuC,GAAG,I,cADhBtB,mBAAA,CAImB;;UAFhBuB,GAAG,EAAEzC,MAAA,CAAAmC,KAAK,CAACO,YAAY,GAAG1C,MAAA,CAAAC,IAAI,CAACuC,GAAG;UACnCG,KAAK,EAAC,MAAM;UACZzC,KAAK,EAAC;8DACRN,YAAA,CAAuEgD,kBAAA;;UAAvD1C,KAAK,EAAC;;4BAAuB,MAAQ,CAARC,YAAA,CAAQ0C,eAAA,E;;;;;;QAGzDC,mBAAA,CAcM,OAdNC,UAcM,GAbJ5C,YAAA,CAKE6C,kBAAA;MAJArD,KAAqC,EAArC;QAAA;MAAA,CAAqC;MACpCsD,MAAM,EAAEjD,MAAA,CAAAkD,SAAS;MACjBC,aAAa,EAAEnD,MAAA,CAAAoD,aAAa;MAC5BC,IAAI,EAAErD,MAAA,CAAAqD;iDAETlD,YAAA,CAMEmD,iBAAA;MALA3D,KAAyC,EAAzC;QAAA;QAAA;MAAA,CAAyC;kBAChCK,MAAA,CAAAC,IAAI,CAACsD,OAAO;iEAAZvD,MAAA,CAAAC,IAAI,CAACsD,OAAO,GAAA9C,MAAA;MACpB0C,aAAa,EAAEnD,MAAA,CAAAwD,YAAY;MAC3BH,IAAI,EAAErD,MAAA,CAAAqD,IAAI;MACVI,WAAS,EAAEzD,MAAA,CAAA0D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}