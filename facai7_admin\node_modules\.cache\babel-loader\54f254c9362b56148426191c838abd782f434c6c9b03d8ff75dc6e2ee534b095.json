{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode } from \"vue\";\nconst _hoisted_1 = [\"src\"];\nconst _hoisted_2 = [\"src\"];\nconst _hoisted_3 = [\"src\"];\nconst _hoisted_4 = [\"src\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_Plus = _resolveComponent(\"Plus\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_upload = _resolveComponent(\"el-upload\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createBlock(_component_el_form, {\n    \"label-width\": \"100px\",\n    inline: true,\n    model: $setup.form,\n    class: \"demo-form-inline\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_form_item, {\n      label: \"标题\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.title,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.form.title = $event)\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"充值方式\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_select, {\n        modelValue: $setup.form.type,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.form.type = $event),\n        placeholder: \"充值方式\",\n        clearable: \"\"\n      }, {\n        default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.withdrawTypeEnums, item => {\n          return _openBlock(), _createBlock(_component_el_option, {\n            label: item.label,\n            key: item.label,\n            value: item.value\n          }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n        }), 128 /* KEYED_FRAGMENT */))]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"汇率\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.rate,\n        \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.form.rate = $event)\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), $setup.form.type == 0 ? (_openBlock(), _createElementBlock(_Fragment, {\n      key: 0\n    }, [_createVNode(_component_el_form_item, {\n      label: \"姓名\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.bank_owner,\n        \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.form.bank_owner = $event),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"开户行\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.bank_name,\n        \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.form.bank_name = $event),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"支行\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.bank_branch,\n        \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.form.bank_branch = $event),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"卡号\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.bank_account,\n        \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $setup.form.bank_account = $event),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    })], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true), $setup.form.type == 1 ? (_openBlock(), _createElementBlock(_Fragment, {\n      key: 1\n    }, [_createVNode(_component_el_form_item, {\n      label: \"虚拟币名称\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.coin_name,\n        \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $setup.form.coin_name = $event),\n        disabled: \"\",\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"钱包地址\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.coin_account,\n        \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $setup.form.coin_account = $event),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"区块链\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.coin_blockchain,\n        \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $setup.form.coin_blockchain = $event),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    })], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true), $setup.form.type == 2 ? (_openBlock(), _createElementBlock(_Fragment, {\n      key: 2\n    }, [_createVNode(_component_el_form_item, {\n      label: \"账号\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.alipay_account,\n        \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $setup.form.alipay_account = $event),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _cache[15] || (_cache[15] = _createElementVNode(\"br\", null, null, -1 /* CACHED */)), _createCommentVNode(\" <el-form-item label=\\\"收款码\\\" required>\\r\\n        <img :src=\\\"form.alipay_img\\\" alt=\\\"\\\" />\\r\\n      </el-form-item> \")], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true), $setup.form.type == 4 ? (_openBlock(), _createElementBlock(_Fragment, {\n      key: 3\n    }, [_createVNode(_component_el_form_item, {\n      label: \"收款人\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.bank_name,\n        \"onUpdate:modelValue\": _cache[11] || (_cache[11] = $event => $setup.form.bank_name = $event),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"账号\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.bank_account,\n        \"onUpdate:modelValue\": _cache[12] || (_cache[12] = $event => $setup.form.bank_account = $event),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    })], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_form_item, {\n      label: \"开启状态\",\n      prop: \"status\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_select, {\n        modelValue: $setup.form.status,\n        \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $setup.form.status = $event),\n        placeholder: \"开启状态\",\n        clearable: \"\"\n      }, {\n        default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.openEnums, item => {\n          return _openBlock(), _createBlock(_component_el_option, {\n            label: item.label,\n            key: item.label,\n            value: item.value\n          }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n        }), 128 /* KEYED_FRAGMENT */))]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"备注\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.remark,\n        \"onUpdate:modelValue\": _cache[14] || (_cache[14] = $event => $setup.form.remark = $event),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), $setup.form.type == 3 ? (_openBlock(), _createBlock(_component_el_form_item, {\n      key: 4,\n      label: \"微信收款码\",\n      prop: \"img\",\n      rules: [{\n        required: true,\n        message: '请上传收款码'\n      }]\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_upload, {\n        class: \"upload-demo\",\n        style: {\n          \"width\": \"114px\"\n        },\n        \"show-file-list\": false,\n        drag: \"\",\n        headers: $setup.headers,\n        action: `${$setup.proxy.BASE_API_URL}index/upload`,\n        \"on-success\": $setup.wxsuccessUpload,\n        \"on-error\": $setup.handleErr1,\n        multiple: false\n      }, {\n        default: _withCtx(() => [$setup.form.img ? (_openBlock(), _createElementBlock(\"img\", {\n          key: 0,\n          src: $setup.proxy.IMG_BASE_URL + $setup.form.img,\n          width: \"100%\",\n          class: \"avatar\"\n        }, null, 8 /* PROPS */, _hoisted_1)) : (_openBlock(), _createBlock(_component_el_icon, {\n          key: 1,\n          class: \"avatar-uploader-icon\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_Plus)]),\n          _: 1 /* STABLE */\n        }))]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"headers\", \"action\"])]),\n      _: 1 /* STABLE */\n    })) : _createCommentVNode(\"v-if\", true), $setup.form.type == 2 ? (_openBlock(), _createBlock(_component_el_form_item, {\n      key: 5,\n      label: \"支付宝收款码\",\n      prop: \"alipay_img\",\n      rules: [{\n        required: true,\n        message: '请上传收款码'\n      }]\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_upload, {\n        class: \"upload-demo\",\n        style: {\n          \"width\": \"114px\"\n        },\n        \"show-file-list\": false,\n        drag: \"\",\n        headers: $setup.headers,\n        action: `${$setup.proxy.BASE_API_URL}index/upload`,\n        \"on-success\": $setup.alipaySuccessUpload,\n        \"on-error\": $setup.handleErr2,\n        multiple: false\n      }, {\n        default: _withCtx(() => [$setup.form.img ? (_openBlock(), _createElementBlock(\"img\", {\n          key: 0,\n          src: $setup.proxy.IMG_BASE_URL + $setup.form.img,\n          width: \"100%\",\n          class: \"avatar\"\n        }, null, 8 /* PROPS */, _hoisted_2)) : (_openBlock(), _createBlock(_component_el_icon, {\n          key: 1,\n          class: \"avatar-uploader-icon\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_Plus)]),\n          _: 1 /* STABLE */\n        }))]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"headers\", \"action\"])]),\n      _: 1 /* STABLE */\n    })) : _createCommentVNode(\"v-if\", true), $setup.form.type == 4 ? (_openBlock(), _createBlock(_component_el_form_item, {\n      key: 6,\n      label: \"收款码（如有）\",\n      prop: \"img\",\n      rules: [{\n        required: true,\n        message: '请上传收款码'\n      }]\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_upload, {\n        class: \"upload-demo\",\n        style: {\n          \"width\": \"114px\"\n        },\n        \"show-file-list\": false,\n        drag: \"\",\n        headers: $setup.headers,\n        action: `${$setup.proxy.BASE_API_URL}index/upload`,\n        \"on-success\": $setup.alipaySuccessUpload,\n        \"on-error\": $setup.handleErr2,\n        multiple: false\n      }, {\n        default: _withCtx(() => [$setup.form.img ? (_openBlock(), _createElementBlock(\"img\", {\n          key: 0,\n          src: $setup.proxy.IMG_BASE_URL + $setup.form.img,\n          width: \"100%\",\n          class: \"avatar\"\n        }, null, 8 /* PROPS */, _hoisted_3)) : (_openBlock(), _createBlock(_component_el_icon, {\n          key: 1,\n          class: \"avatar-uploader-icon\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_Plus)]),\n          _: 1 /* STABLE */\n        }))]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"headers\", \"action\"])]),\n      _: 1 /* STABLE */\n    })) : _createCommentVNode(\"v-if\", true), $setup.form.type == 1 ? (_openBlock(), _createBlock(_component_el_form_item, {\n      key: 7,\n      label: \"钱包二维码\",\n      prop: \"img\",\n      rules: [{\n        required: true,\n        message: '请上传钱包二维码'\n      }]\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_upload, {\n        class: \"upload-demo\",\n        style: {\n          \"width\": \"114px\"\n        },\n        \"show-file-list\": false,\n        drag: \"\",\n        headers: $setup.headers,\n        action: `${$setup.proxy.BASE_API_URL}index/upload`,\n        \"on-success\": $setup.wxsuccessUpload,\n        \"on-error\": $setup.handleErr1,\n        multiple: false\n      }, {\n        default: _withCtx(() => [$setup.form.img ? (_openBlock(), _createElementBlock(\"img\", {\n          key: 0,\n          src: $setup.proxy.IMG_BASE_URL + $setup.form.img,\n          width: \"100%\",\n          class: \"avatar\"\n        }, null, 8 /* PROPS */, _hoisted_4)) : (_openBlock(), _createBlock(_component_el_icon, {\n          key: 1,\n          class: \"avatar-uploader-icon\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_Plus)]),\n          _: 1 /* STABLE */\n        }))]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"headers\", \"action\"])]),\n      _: 1 /* STABLE */\n    })) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\"]);\n}", "map": {"version": 3, "names": ["_createBlock", "_component_el_form", "inline", "model", "$setup", "form", "class", "_createVNode", "_component_el_form_item", "label", "required", "_component_el_input", "title", "$event", "_component_el_select", "type", "placeholder", "clearable", "_createElementBlock", "_Fragment", "_renderList", "withdrawTypeEnums", "item", "_component_el_option", "key", "value", "rate", "bank_owner", "bank_name", "bank_branch", "bank_account", "coin_name", "disabled", "coin_account", "coin_blockchain", "alipay_account", "_createElementVNode", "_createCommentVNode", "prop", "status", "openEnums", "remark", "rules", "message", "_component_el_upload", "style", "drag", "headers", "action", "proxy", "BASE_API_URL", "wxsuccessUpload", "handleErr1", "multiple", "img", "src", "IMG_BASE_URL", "width", "_component_el_icon", "_component_Plus", "alipaySuccessUpload", "handleErr2"], "sources": ["D:\\WorkSpace\\facai7\\facai7_admin\\src\\views\\payments\\components\\paymentAccount\\editPop.vue"], "sourcesContent": ["<template>\r\n  <el-form\r\n    label-width=\"100px\"\r\n    :inline=\"true\"\r\n    :model=\"form\"\r\n    class=\"demo-form-inline\"\r\n  >\r\n    <el-form-item label=\"标题\" required>\r\n      <el-input v-model=\"form.title\" />\r\n    </el-form-item>\r\n\r\n    <el-form-item label=\"充值方式\" required>\r\n      <el-select v-model=\"form.type\" placeholder=\"充值方式\" clearable>\r\n        <el-option\r\n          v-for=\"item in withdrawTypeEnums\"\r\n          :label=\"item.label\"\r\n          :key=\"item.label\"\r\n          :value=\"item.value\"\r\n        />\r\n      </el-select>\r\n    </el-form-item>\r\n\r\n    <el-form-item label=\"汇率\" required>\r\n      <el-input v-model=\"form.rate\" />\r\n    </el-form-item>\r\n\r\n    <template v-if=\"form.type == 0\">\r\n      <el-form-item label=\"姓名\"  required>\r\n              <el-input v-model=\"form.bank_owner\"  clearable />\r\n          </el-form-item>\r\n      <el-form-item label=\"开户行\" required>\r\n        <el-input v-model=\"form.bank_name\" clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"支行\" required>\r\n        <el-input v-model=\"form.bank_branch\" clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"卡号\" required>\r\n        <el-input v-model=\"form.bank_account\" clearable />\r\n      </el-form-item>\r\n    </template>\r\n    <template v-if=\"form.type == 1\">\r\n      <el-form-item label=\"虚拟币名称\" required>\r\n        <el-input v-model=\"form.coin_name\" disabled clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"钱包地址\" required>\r\n        <el-input v-model=\"form.coin_account\" clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"区块链\" required>\r\n        <el-input v-model=\"form.coin_blockchain\" clearable />\r\n      </el-form-item>\r\n      \r\n    </template>\r\n    <template v-if=\"form.type == 2\">\r\n      <el-form-item label=\"账号\" required>\r\n        <el-input v-model=\"form.alipay_account\" clearable />\r\n      </el-form-item>\r\n      <br />\r\n      <!-- <el-form-item label=\"收款码\" required>\r\n        <img :src=\"form.alipay_img\" alt=\"\" />\r\n      </el-form-item> -->\r\n    </template>\r\n    <template v-if=\"form.type == 4\">\r\n      <el-form-item label=\"收款人\" required>\r\n        <el-input v-model=\"form.bank_name\" clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"账号\" required>\r\n        <el-input v-model=\"form.bank_account\" clearable />\r\n      </el-form-item>\r\n      \r\n    </template>\r\n\r\n    <el-form-item label=\"开启状态\" prop=\"status\">\r\n      <el-select v-model=\"form.status\" placeholder=\"开启状态\" clearable>\r\n        <el-option\r\n          v-for=\"item in openEnums\"\r\n          :label=\"item.label\"\r\n          :key=\"item.label\"\r\n          :value=\"item.value\"\r\n        />\r\n      </el-select>\r\n    </el-form-item>\r\n    <el-form-item label=\"备注\" required>\r\n      <el-input v-model=\"form.remark\" clearable />\r\n    </el-form-item>\r\n    <el-form-item\r\n      v-if=\"form.type == 3\"\r\n      label=\"微信收款码\"\r\n      prop=\"img\"\r\n      :rules=\"[{ required: true, message: '请上传收款码' }]\"\r\n    >\r\n      <el-upload\r\n        class=\"upload-demo\"\r\n        style=\"width: 114px\"\r\n        :show-file-list=\"false\"\r\n        drag\r\n        :headers=\"headers\"\r\n        :action=\"`${proxy.BASE_API_URL}index/upload`\"\r\n        :on-success=\"wxsuccessUpload\"\r\n        :on-error=\"handleErr1\"\r\n        :multiple=\"false\"\r\n      >\r\n        <img\r\n          v-if=\"form.img\"\r\n          :src=\"proxy.IMG_BASE_URL + form.img\"\r\n          width=\"100%\"\r\n          class=\"avatar\" />\r\n        <el-icon v-else class=\"avatar-uploader-icon\"><Plus /></el-icon\r\n      ></el-upload>\r\n    </el-form-item>\r\n    <el-form-item\r\n      v-if=\"form.type == 2\"\r\n      label=\"支付宝收款码\"\r\n      prop=\"alipay_img\"\r\n      :rules=\"[{ required: true, message: '请上传收款码' }]\"\r\n    >\r\n      <el-upload\r\n        class=\"upload-demo\"\r\n        style=\"width: 114px\"\r\n        :show-file-list=\"false\"\r\n        drag\r\n        :headers=\"headers\"\r\n        :action=\"`${proxy.BASE_API_URL}index/upload`\"\r\n        :on-success=\"alipaySuccessUpload\"\r\n        :on-error=\"handleErr2\"\r\n        :multiple=\"false\"\r\n      >\r\n        <img\r\n          v-if=\"form.img\"\r\n          :src=\"proxy.IMG_BASE_URL + form.img\"\r\n          width=\"100%\"\r\n          class=\"avatar\" />\r\n        <el-icon v-else class=\"avatar-uploader-icon\"><Plus /></el-icon\r\n      ></el-upload>\r\n    </el-form-item>\r\n    <el-form-item\r\n      v-if=\"form.type == 4\"\r\n      label=\"收款码（如有）\"\r\n      prop=\"img\"\r\n      :rules=\"[{ required: true, message: '请上传收款码' }]\"\r\n    >\r\n      <el-upload\r\n        class=\"upload-demo\"\r\n        style=\"width: 114px\"\r\n        :show-file-list=\"false\"\r\n        drag\r\n        :headers=\"headers\"\r\n        :action=\"`${proxy.BASE_API_URL}index/upload`\"\r\n        :on-success=\"alipaySuccessUpload\"\r\n        :on-error=\"handleErr2\"\r\n        :multiple=\"false\"\r\n      >\r\n        <img\r\n          v-if=\"form.img\"\r\n          :src=\"proxy.IMG_BASE_URL + form.img\"\r\n          width=\"100%\"\r\n          class=\"avatar\" />\r\n        <el-icon v-else class=\"avatar-uploader-icon\"><Plus /></el-icon\r\n      ></el-upload>\r\n    </el-form-item>\r\n      <el-form-item\r\n       v-if=\"form.type == 1\"\r\n      label=\"钱包二维码\"\r\n      prop=\"img\"\r\n      :rules=\"[{ required: true, message: '请上传钱包二维码' }]\"\r\n    >\r\n      <el-upload\r\n        class=\"upload-demo\"\r\n        style=\"width: 114px\"\r\n        :show-file-list=\"false\"\r\n        drag\r\n        :headers=\"headers\"\r\n        :action=\"`${proxy.BASE_API_URL}index/upload`\"\r\n        :on-success=\"wxsuccessUpload\"\r\n        :on-error=\"handleErr1\"\r\n        :multiple=\"false\"\r\n      >\r\n        <img\r\n          v-if=\"form.img\"\r\n          :src=\"proxy.IMG_BASE_URL + form.img\"\r\n          width=\"100%\"\r\n          class=\"avatar\" />\r\n        <el-icon v-else class=\"avatar-uploader-icon\"><Plus /></el-icon\r\n      ></el-upload>\r\n    </el-form-item>\r\n  </el-form>\r\n</template>\r\n\r\n<script setup>\r\nimport { getCurrentInstance, nextTick, onMounted, ref } from \"vue\";\r\nimport { withdrawTypeEnums, getLabelByVal, openEnums } from \"@/config/enums\";\r\nimport { getTokenAUTH } from \"@/utils/auth\";\r\n\r\nconst form = ref({\r\n  title: \"\",\r\n  type: \"\",\r\n  rate: \"\",\r\n  bank_name: \"\",\r\n  bank_branch: \"\",\r\n  bank_account: \"\",\r\n  coin_name: \"USDT\",\r\n  coin_account: \"\",\r\n  coin_blockchain: \"\",\r\n  alipay_account: \"\",\r\n  img: \"\",\r\n  status: \"\",\r\n  bank_owner: \"\",\r\n  remark: \"\",\r\n});\r\nconst props = defineProps([\"item\"]);\r\n\r\nconst headers = ref({})\r\nonMounted(() => {\r\n  headers.value['Accept-Token'] = getTokenAUTH()\r\n  nextTick(() => {\r\n    form.value = Object.assign(form, props.item);\r\n    form.value.coin_name = \"USDT\"\r\n  });\r\n});\r\n\r\nconst { proxy } = getCurrentInstance();\r\n\r\nconst alipaySuccessUpload = (res) => {\r\n  form.value.img = res.data.url;\r\n};\r\n\r\nconst handleErr1 = (err) => {\r\n  if (err.status == 320) {\r\n    form.value.img = JSON.parse(err.message).data.url;\r\n  }\r\n}\r\n\r\nconst handleErr2 = (err) => {\r\n  if (err.status == 320) {\r\n    form.value.img = JSON.parse(err.message).data.url;\r\n  }\r\n}\r\n\r\nconst wxsuccessUpload = (res) => {\r\n  form.value.img = res.data.url;\r\n};\r\n\r\ndefineExpose({ form });\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.demo-form-inline {\r\n  justify-content: flex-start;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n.demo-form-inline .el-input {\r\n  --el-input-width: 220px;\r\n}\r\n\r\n.demo-form-inline .el-select {\r\n  --el-select-width: 220px;\r\n}\r\n/deep/ .el-radio-group {\r\n  width: 220px;\r\n}\r\n.form-title {\r\n  text-align: left;\r\n  padding-left: 30px;\r\n  margin: 20px auto 10px;\r\n  height: 44px;\r\n  background-color: #f2f2f2;\r\n  border-radius: 5px;\r\n  line-height: 44px;\r\n}\r\n/deep/ .el-form-item {\r\n  align-items: flex-start;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;uBACEA,YAAA,CAuLUC,kBAAA;IAtLR,aAAW,EAAC,OAAO;IAClBC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IACZC,KAAK,EAAC;;sBAEN,MAEe,CAFfC,YAAA,CAEeC,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,QAAQ,EAAR;;wBACvB,MAAiC,CAAjCH,YAAA,CAAiCI,mBAAA;oBAAdP,MAAA,CAAAC,IAAI,CAACO,KAAK;mEAAVR,MAAA,CAAAC,IAAI,CAACO,KAAK,GAAAC,MAAA;;;QAG/BN,YAAA,CASeC,uBAAA;MATDC,KAAK,EAAC,MAAM;MAACC,QAAQ,EAAR;;wBACzB,MAOY,CAPZH,YAAA,CAOYO,oBAAA;oBAPQV,MAAA,CAAAC,IAAI,CAACU,IAAI;mEAATX,MAAA,CAAAC,IAAI,CAACU,IAAI,GAAAF,MAAA;QAAEG,WAAW,EAAC,MAAM;QAACC,SAAS,EAAT;;0BAE9C,MAAiC,E,kBADnCC,mBAAA,CAKEC,SAAA,QAAAC,WAAA,CAJehB,MAAA,CAAAiB,iBAAiB,EAAzBC,IAAI;+BADbtB,YAAA,CAKEuB,oBAAA;YAHCd,KAAK,EAAEa,IAAI,CAACb,KAAK;YACjBe,GAAG,EAAEF,IAAI,CAACb,KAAK;YACfgB,KAAK,EAAEH,IAAI,CAACG;;;;;;QAKnBlB,YAAA,CAEeC,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,QAAQ,EAAR;;wBACvB,MAAgC,CAAhCH,YAAA,CAAgCI,mBAAA;oBAAbP,MAAA,CAAAC,IAAI,CAACqB,IAAI;mEAATtB,MAAA,CAAAC,IAAI,CAACqB,IAAI,GAAAb,MAAA;;;QAGdT,MAAA,CAAAC,IAAI,CAACU,IAAI,S,cAAzBG,mBAAA,CAaWC,SAAA;MAAAK,GAAA;IAAA,IAZTjB,YAAA,CAEmBC,uBAAA;MAFLC,KAAK,EAAC,IAAI;MAAEC,QAAQ,EAAR;;wBAClB,MAAiD,CAAjDH,YAAA,CAAiDI,mBAAA;oBAA9BP,MAAA,CAAAC,IAAI,CAACsB,UAAU;mEAAfvB,MAAA,CAAAC,IAAI,CAACsB,UAAU,GAAAd,MAAA;QAAGI,SAAS,EAAT;;;QAE7CV,YAAA,CAEeC,uBAAA;MAFDC,KAAK,EAAC,KAAK;MAACC,QAAQ,EAAR;;wBACxB,MAA+C,CAA/CH,YAAA,CAA+CI,mBAAA;oBAA5BP,MAAA,CAAAC,IAAI,CAACuB,SAAS;mEAAdxB,MAAA,CAAAC,IAAI,CAACuB,SAAS,GAAAf,MAAA;QAAEI,SAAS,EAAT;;;QAErCV,YAAA,CAEeC,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,QAAQ,EAAR;;wBACvB,MAAiD,CAAjDH,YAAA,CAAiDI,mBAAA;oBAA9BP,MAAA,CAAAC,IAAI,CAACwB,WAAW;mEAAhBzB,MAAA,CAAAC,IAAI,CAACwB,WAAW,GAAAhB,MAAA;QAAEI,SAAS,EAAT;;;QAEvCV,YAAA,CAEeC,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,QAAQ,EAAR;;wBACvB,MAAkD,CAAlDH,YAAA,CAAkDI,mBAAA;oBAA/BP,MAAA,CAAAC,IAAI,CAACyB,YAAY;mEAAjB1B,MAAA,CAAAC,IAAI,CAACyB,YAAY,GAAAjB,MAAA;QAAEI,SAAS,EAAT;;;yEAG1Bb,MAAA,CAAAC,IAAI,CAACU,IAAI,S,cAAzBG,mBAAA,CAWWC,SAAA;MAAAK,GAAA;IAAA,IAVTjB,YAAA,CAEeC,uBAAA;MAFDC,KAAK,EAAC,OAAO;MAACC,QAAQ,EAAR;;wBAC1B,MAAwD,CAAxDH,YAAA,CAAwDI,mBAAA;oBAArCP,MAAA,CAAAC,IAAI,CAAC0B,SAAS;mEAAd3B,MAAA,CAAAC,IAAI,CAAC0B,SAAS,GAAAlB,MAAA;QAAEmB,QAAQ,EAAR,EAAQ;QAACf,SAAS,EAAT;;;QAE9CV,YAAA,CAEeC,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,QAAQ,EAAR;;wBACzB,MAAkD,CAAlDH,YAAA,CAAkDI,mBAAA;oBAA/BP,MAAA,CAAAC,IAAI,CAAC4B,YAAY;mEAAjB7B,MAAA,CAAAC,IAAI,CAAC4B,YAAY,GAAApB,MAAA;QAAEI,SAAS,EAAT;;;QAExCV,YAAA,CAEeC,uBAAA;MAFDC,KAAK,EAAC,KAAK;MAACC,QAAQ,EAAR;;wBACxB,MAAqD,CAArDH,YAAA,CAAqDI,mBAAA;oBAAlCP,MAAA,CAAAC,IAAI,CAAC6B,eAAe;mEAApB9B,MAAA,CAAAC,IAAI,CAAC6B,eAAe,GAAArB,MAAA;QAAEI,SAAS,EAAT;;;yEAI7Bb,MAAA,CAAAC,IAAI,CAACU,IAAI,S,cAAzBG,mBAAA,CAQWC,SAAA;MAAAK,GAAA;IAAA,IAPTjB,YAAA,CAEeC,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,QAAQ,EAAR;;wBACvB,MAAoD,CAApDH,YAAA,CAAoDI,mBAAA;oBAAjCP,MAAA,CAAAC,IAAI,CAAC8B,cAAc;qEAAnB/B,MAAA,CAAAC,IAAI,CAAC8B,cAAc,GAAAtB,MAAA;QAAEI,SAAS,EAAT;;;oCAE1CmB,mBAAA,CAAM,qCACNC,mBAAA,yHAEmB,C,kEAELjC,MAAA,CAAAC,IAAI,CAACU,IAAI,S,cAAzBG,mBAAA,CAQWC,SAAA;MAAAK,GAAA;IAAA,IAPTjB,YAAA,CAEeC,uBAAA;MAFDC,KAAK,EAAC,KAAK;MAACC,QAAQ,EAAR;;wBACxB,MAA+C,CAA/CH,YAAA,CAA+CI,mBAAA;oBAA5BP,MAAA,CAAAC,IAAI,CAACuB,SAAS;qEAAdxB,MAAA,CAAAC,IAAI,CAACuB,SAAS,GAAAf,MAAA;QAAEI,SAAS,EAAT;;;QAErCV,YAAA,CAEeC,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,QAAQ,EAAR;;wBACvB,MAAkD,CAAlDH,YAAA,CAAkDI,mBAAA;oBAA/BP,MAAA,CAAAC,IAAI,CAACyB,YAAY;qEAAjB1B,MAAA,CAAAC,IAAI,CAACyB,YAAY,GAAAjB,MAAA;QAAEI,SAAS,EAAT;;;yEAK1CV,YAAA,CASeC,uBAAA;MATDC,KAAK,EAAC,MAAM;MAAC6B,IAAI,EAAC;;wBAC9B,MAOY,CAPZ/B,YAAA,CAOYO,oBAAA;oBAPQV,MAAA,CAAAC,IAAI,CAACkC,MAAM;qEAAXnC,MAAA,CAAAC,IAAI,CAACkC,MAAM,GAAA1B,MAAA;QAAEG,WAAW,EAAC,MAAM;QAACC,SAAS,EAAT;;0BAEhD,MAAyB,E,kBAD3BC,mBAAA,CAKEC,SAAA,QAAAC,WAAA,CAJehB,MAAA,CAAAoC,SAAS,EAAjBlB,IAAI;+BADbtB,YAAA,CAKEuB,oBAAA;YAHCd,KAAK,EAAEa,IAAI,CAACb,KAAK;YACjBe,GAAG,EAAEF,IAAI,CAACb,KAAK;YACfgB,KAAK,EAAEH,IAAI,CAACG;;;;;;QAInBlB,YAAA,CAEeC,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,QAAQ,EAAR;;wBACvB,MAA4C,CAA5CH,YAAA,CAA4CI,mBAAA;oBAAzBP,MAAA,CAAAC,IAAI,CAACoC,MAAM;qEAAXrC,MAAA,CAAAC,IAAI,CAACoC,MAAM,GAAA5B,MAAA;QAAEI,SAAS,EAAT;;;QAG1Bb,MAAA,CAAAC,IAAI,CAACU,IAAI,S,cADjBf,YAAA,CAwBeQ,uBAAA;;MAtBbC,KAAK,EAAC,OAAO;MACb6B,IAAI,EAAC,KAAK;MACTI,KAAK,EAAE;QAAAhC,QAAA;QAAAiC,OAAA;MAAA;;wBAER,MAiBa,CAjBbpC,YAAA,CAiBaqC,oBAAA;QAhBXtC,KAAK,EAAC,aAAa;QACnBuC,KAAoB,EAApB;UAAA;QAAA,CAAoB;QACnB,gBAAc,EAAE,KAAK;QACtBC,IAAI,EAAJ,EAAI;QACHC,OAAO,EAAE3C,MAAA,CAAA2C,OAAO;QAChBC,MAAM,KAAK5C,MAAA,CAAA6C,KAAK,CAACC,YAAY;QAC7B,YAAU,EAAE9C,MAAA,CAAA+C,eAAe;QAC3B,UAAQ,EAAE/C,MAAA,CAAAgD,UAAU;QACpBC,QAAQ,EAAE;;iCAGHjD,MAAA,CAAAC,IAAI,CAACiD,GAAG,I,cADhBpC,mBAAA,CAImB;;UAFhBqC,GAAG,EAAEnD,MAAA,CAAA6C,KAAK,CAACO,YAAY,GAAGpD,MAAA,CAAAC,IAAI,CAACiD,GAAG;UACnCG,KAAK,EAAC,MAAM;UACZnD,KAAK,EAAC;8DACRN,YAAA,CAAuE0D,kBAAA;;UAAvDpD,KAAK,EAAC;;4BAAuB,MAAQ,CAARC,YAAA,CAAQoD,eAAA,E;;;;;;6CAIjDvD,MAAA,CAAAC,IAAI,CAACU,IAAI,S,cADjBf,YAAA,CAwBeQ,uBAAA;;MAtBbC,KAAK,EAAC,QAAQ;MACd6B,IAAI,EAAC,YAAY;MAChBI,KAAK,EAAE;QAAAhC,QAAA;QAAAiC,OAAA;MAAA;;wBAER,MAiBa,CAjBbpC,YAAA,CAiBaqC,oBAAA;QAhBXtC,KAAK,EAAC,aAAa;QACnBuC,KAAoB,EAApB;UAAA;QAAA,CAAoB;QACnB,gBAAc,EAAE,KAAK;QACtBC,IAAI,EAAJ,EAAI;QACHC,OAAO,EAAE3C,MAAA,CAAA2C,OAAO;QAChBC,MAAM,KAAK5C,MAAA,CAAA6C,KAAK,CAACC,YAAY;QAC7B,YAAU,EAAE9C,MAAA,CAAAwD,mBAAmB;QAC/B,UAAQ,EAAExD,MAAA,CAAAyD,UAAU;QACpBR,QAAQ,EAAE;;iCAGHjD,MAAA,CAAAC,IAAI,CAACiD,GAAG,I,cADhBpC,mBAAA,CAImB;;UAFhBqC,GAAG,EAAEnD,MAAA,CAAA6C,KAAK,CAACO,YAAY,GAAGpD,MAAA,CAAAC,IAAI,CAACiD,GAAG;UACnCG,KAAK,EAAC,MAAM;UACZnD,KAAK,EAAC;8DACRN,YAAA,CAAuE0D,kBAAA;;UAAvDpD,KAAK,EAAC;;4BAAuB,MAAQ,CAARC,YAAA,CAAQoD,eAAA,E;;;;;;6CAIjDvD,MAAA,CAAAC,IAAI,CAACU,IAAI,S,cADjBf,YAAA,CAwBeQ,uBAAA;;MAtBbC,KAAK,EAAC,SAAS;MACf6B,IAAI,EAAC,KAAK;MACTI,KAAK,EAAE;QAAAhC,QAAA;QAAAiC,OAAA;MAAA;;wBAER,MAiBa,CAjBbpC,YAAA,CAiBaqC,oBAAA;QAhBXtC,KAAK,EAAC,aAAa;QACnBuC,KAAoB,EAApB;UAAA;QAAA,CAAoB;QACnB,gBAAc,EAAE,KAAK;QACtBC,IAAI,EAAJ,EAAI;QACHC,OAAO,EAAE3C,MAAA,CAAA2C,OAAO;QAChBC,MAAM,KAAK5C,MAAA,CAAA6C,KAAK,CAACC,YAAY;QAC7B,YAAU,EAAE9C,MAAA,CAAAwD,mBAAmB;QAC/B,UAAQ,EAAExD,MAAA,CAAAyD,UAAU;QACpBR,QAAQ,EAAE;;iCAGHjD,MAAA,CAAAC,IAAI,CAACiD,GAAG,I,cADhBpC,mBAAA,CAImB;;UAFhBqC,GAAG,EAAEnD,MAAA,CAAA6C,KAAK,CAACO,YAAY,GAAGpD,MAAA,CAAAC,IAAI,CAACiD,GAAG;UACnCG,KAAK,EAAC,MAAM;UACZnD,KAAK,EAAC;8DACRN,YAAA,CAAuE0D,kBAAA;;UAAvDpD,KAAK,EAAC;;4BAAuB,MAAQ,CAARC,YAAA,CAAQoD,eAAA,E;;;;;;6CAIhDvD,MAAA,CAAAC,IAAI,CAACU,IAAI,S,cADhBf,YAAA,CAwBaQ,uBAAA;;MAtBbC,KAAK,EAAC,OAAO;MACb6B,IAAI,EAAC,KAAK;MACTI,KAAK,EAAE;QAAAhC,QAAA;QAAAiC,OAAA;MAAA;;wBAER,MAiBa,CAjBbpC,YAAA,CAiBaqC,oBAAA;QAhBXtC,KAAK,EAAC,aAAa;QACnBuC,KAAoB,EAApB;UAAA;QAAA,CAAoB;QACnB,gBAAc,EAAE,KAAK;QACtBC,IAAI,EAAJ,EAAI;QACHC,OAAO,EAAE3C,MAAA,CAAA2C,OAAO;QAChBC,MAAM,KAAK5C,MAAA,CAAA6C,KAAK,CAACC,YAAY;QAC7B,YAAU,EAAE9C,MAAA,CAAA+C,eAAe;QAC3B,UAAQ,EAAE/C,MAAA,CAAAgD,UAAU;QACpBC,QAAQ,EAAE;;iCAGHjD,MAAA,CAAAC,IAAI,CAACiD,GAAG,I,cADhBpC,mBAAA,CAImB;;UAFhBqC,GAAG,EAAEnD,MAAA,CAAA6C,KAAK,CAACO,YAAY,GAAGpD,MAAA,CAAAC,IAAI,CAACiD,GAAG;UACnCG,KAAK,EAAC,MAAM;UACZnD,KAAK,EAAC;8DACRN,YAAA,CAAuE0D,kBAAA;;UAAvDpD,KAAK,EAAC;;4BAAuB,MAAQ,CAARC,YAAA,CAAQoD,eAAA,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}