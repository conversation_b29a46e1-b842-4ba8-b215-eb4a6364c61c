{"ast": null, "code": "import getNative from './_getNative.js';\nimport root from './_root.js';\n\n/* Built-in method references that are verified to be native. */\nvar DataView = getNative(root, 'DataView');\nexport default DataView;", "map": {"version": 3, "names": ["getNative", "root", "DataView"], "sources": ["D:/WorkSpace/facai7/facai7_admin/node_modules/lodash-es/_DataView.js"], "sourcesContent": ["import getNative from './_getNative.js';\nimport root from './_root.js';\n\n/* Built-in method references that are verified to be native. */\nvar DataView = getNative(root, 'DataView');\n\nexport default DataView;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,IAAI,MAAM,YAAY;;AAE7B;AACA,IAAIC,QAAQ,GAAGF,SAAS,CAACC,IAAI,EAAE,UAAU,CAAC;AAE1C,eAAeC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}