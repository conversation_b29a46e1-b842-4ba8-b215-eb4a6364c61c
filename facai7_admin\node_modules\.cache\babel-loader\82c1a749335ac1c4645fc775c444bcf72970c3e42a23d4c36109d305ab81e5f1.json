{"ast": null, "code": "import { getCurrentInstance, nextTick, onMounted, ref } from \"vue\";\nimport { openEnums, payChannelStyleEnums, getLabelByVal } from \"@/config/enums\";\nexport default {\n  __name: 'editPop',\n  props: [\"item\"],\n  setup(__props, {\n    expose: __expose\n  }) {\n    const form = ref({\n      sort: \"\",\n      desc: \"\",\n      code: \"\",\n      status: \"\",\n      upper_id: \"\",\n      class_id: \"\",\n      min: \"\",\n      max: \"\",\n      style: 1,\n      accounts: '',\n      title: '',\n      style: ''\n    });\n    const props = __props;\n    onMounted(() => {\n      nextTick(() => {\n        form.value = Object.assign(form, props.item);\n      });\n      getTYpesEnum();\n      getUppersEnum();\n      getAccountList();\n    });\n    const typesEnum = ref([]);\n    const getTYpesEnum = async () => {\n      const res = await proxy.$http({\n        method: 'get',\n        url: '/PaymentClass/getPaymentClassLists'\n      });\n      if (res.code == 0) {\n        typesEnum.value = res.data.data;\n      }\n    };\n    const accountList = ref([]);\n    const getAccountList = async () => {\n      const res = await proxy.$http({\n        method: \"get\",\n        url: \"/PaymentAccount/getPaymentAccountLists\"\n      });\n      if (res.code == 0) {\n        accountList.value = res.data.data;\n      }\n    };\n    const {\n      proxy\n    } = getCurrentInstance();\n    const uppersEnum = ref([]);\n    const getUppersEnum = async () => {\n      const res = await proxy.$http({\n        method: 'get',\n        url: '/PaymentUpper/getPaymentUpperLists'\n      });\n      if (res.code == 0) {\n        uppersEnum.value = res.data.data;\n      }\n    };\n    __expose({\n      form\n    });\n    const __returned__ = {\n      form,\n      props,\n      typesEnum,\n      getTYpesEnum,\n      accountList,\n      getAccountList,\n      proxy,\n      uppersEnum,\n      getUppersEnum,\n      getCurrentInstance,\n      nextTick,\n      onMounted,\n      ref,\n      get openEnums() {\n        return openEnums;\n      },\n      get payChannelStyleEnums() {\n        return payChannelStyleEnums;\n      },\n      get getLabelByVal() {\n        return getLabelByVal;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["getCurrentInstance", "nextTick", "onMounted", "ref", "openEnums", "payChannelStyleEnums", "getLabelByVal", "form", "sort", "desc", "code", "status", "upper_id", "class_id", "min", "max", "style", "accounts", "title", "props", "__props", "value", "Object", "assign", "item", "getTYpesEnum", "getUppersEnum", "getAccountList", "typesEnum", "res", "proxy", "$http", "method", "url", "data", "accountList", "uppersEnum", "__expose"], "sources": ["D:/WorkSpace/facai7/facai7_admin/src/views/payments/components/paymentChannel/editPop.vue"], "sourcesContent": ["<template>\r\n    <el-form\r\n      label-width=\"80px\"\r\n      :inline=\"true\"\r\n      :model=\"form\"\r\n      class=\"demo-form-inline\"\r\n    >\r\n      <el-form-item label=\"排序\" required>\r\n        <el-input v-model=\"form.sort\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"支付名称\" required>\r\n        <el-input v-model=\"form.title\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"支付编码\" required>\r\n        <el-input v-model=\"form.code\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"支付账号\" required>\r\n        <el-select v-model=\"form.accounts\" placeholder=\"支付账号\" clearable>\r\n          <el-option\r\n            v-for=\"item in accountList\"\r\n            :label=\"item.title\"\r\n            :key=\"item.id\"\r\n            :value=\"item.id + ''\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n\r\n      <el-form-item\r\n        label=\"开启状态\"\r\n        prop=\"status\"\r\n      >\r\n        <el-select v-model=\"form.status\" placeholder=\"开启状态\" clearable>\r\n          <el-option\r\n            v-for=\"item in openEnums\"\r\n            :label=\"item.label\"\r\n            :key=\"item.label\"\r\n            :value=\"item.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item\r\n        label=\"支付方式\"\r\n        prop=\"style\"\r\n      >\r\n        <el-select v-model=\"form.style\" placeholder=\"支付方式\" clearable>\r\n          <el-option\r\n            v-for=\"item in payChannelStyleEnums\"\r\n            :label=\"item.label\"\r\n            :key=\"item.label\"\r\n            :value=\"item.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item\r\n        label=\"支付上游\"\r\n        prop=\"upper_id\"\r\n      >\r\n        <el-select v-model=\"form.upper_id\" placeholder=\"支付上游\" clearable>\r\n          <el-option\r\n            v-for=\"item in uppersEnum\"\r\n            :label=\"item.title\"\r\n            :key=\"item.title\"\r\n            :value=\"item.id\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item\r\n        label=\"充值类型\"\r\n        prop=\"class_id\"\r\n      >\r\n        <el-select v-model=\"form.class_id\" placeholder=\"充值类型\" clearable>\r\n          <el-option\r\n            v-for=\"item in typesEnum\"\r\n            :label=\"item.title\"\r\n            :key=\"item.title\"\r\n            :value=\"item.id\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"最大充值金额\" required>\r\n        <el-input v-model=\"form.max\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"最小充值金额\" required>\r\n        <el-input v-model=\"form.min\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"描述(|换行)\" required>\r\n        <el-input v-model=\"form.desc\" />\r\n      </el-form-item>\r\n    </el-form>\r\n  </template>\r\n  \r\n  <script setup>\r\n  import { getCurrentInstance, nextTick, onMounted, ref } from \"vue\";\r\n  import { openEnums, payChannelStyleEnums, getLabelByVal } from \"@/config/enums\";\r\n  \r\n  const form = ref({\r\n    sort: \"\",\r\n    desc: \"\",\r\n    code: \"\",\r\n    status: \"\",\r\n    upper_id: \"\",\r\n    class_id: \"\",\r\n    min: \"\",\r\n    max: \"\",\r\n    style: 1,\r\n    accounts: '',\r\n    title: '',\r\n    style: '',\r\n  });\r\n  const props = defineProps([\"item\"]);\r\n  \r\n  onMounted(() => {\r\n    nextTick(() => {\r\n      form.value = Object.assign(form, props.item);\r\n    });\r\n    getTYpesEnum()\r\n    getUppersEnum()\r\n    getAccountList()\r\n  });\r\n  \r\n  const typesEnum = ref([])\r\n\r\nconst getTYpesEnum = async () => {\r\n    const res = await proxy.$http({\r\n        method: 'get',\r\n        url: '/PaymentClass/getPaymentClassLists'\r\n    })\r\n    if (res.code == 0) {\r\n        typesEnum.value = res.data.data\r\n    }\r\n}\r\n\r\nconst accountList = ref([])\r\nconst getAccountList = async () => {\r\n  const res = await proxy.$http({\r\n    method: \"get\",\r\n    url: \"/PaymentAccount/getPaymentAccountLists\",\r\n  });\r\n  if (res.code == 0) {\r\n    accountList.value = res.data.data;\r\n  }\r\n};\r\n\r\nconst { proxy } = getCurrentInstance()\r\nconst uppersEnum = ref([])\r\n\r\nconst getUppersEnum = async () => {\r\n    const res = await proxy.$http({\r\n        method: 'get',\r\n        url: '/PaymentUpper/getPaymentUpperLists'\r\n    })\r\n    if (res.code == 0) {\r\n      uppersEnum.value = res.data.data\r\n    }\r\n}\r\n  \r\n  defineExpose({ form });\r\n  </script>\r\n  \r\n  <style lang=\"less\" scoped>\r\n  .demo-form-inline {\r\n    justify-content: flex-start;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n  }\r\n  .demo-form-inline .el-input {\r\n    --el-input-width: 220px;\r\n  }\r\n  \r\n  .demo-form-inline .el-select {\r\n    --el-select-width: 220px;\r\n  }\r\n  /deep/ .el-radio-group {\r\n    width: 220px;\r\n  }\r\n  .form-title {\r\n    text-align: left;\r\n    padding-left: 30px;\r\n    margin: 20px auto 10px;\r\n    height: 44px;\r\n    background-color: #f2f2f2;\r\n    border-radius: 5px;\r\n    line-height: 44px;\r\n  }\r\n  </style>\r\n  "], "mappings": "AA6FE,SAASA,kBAAkB,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,GAAG,QAAQ,KAAK;AAClE,SAASC,SAAS,EAAEC,oBAAoB,EAAEC,aAAa,QAAQ,gBAAgB;;;;;;;IAE/E,MAAMC,IAAI,GAAGJ,GAAG,CAAC;MACfK,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,EAAE;MACRC,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,GAAG,EAAE,EAAE;MACPC,GAAG,EAAE,EAAE;MACPC,KAAK,EAAE,CAAC;MACRC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTF,KAAK,EAAE;IACT,CAAC,CAAC;IACF,MAAMG,KAAK,GAAGC,OAAqB;IAEnClB,SAAS,CAAC,MAAM;MACdD,QAAQ,CAAC,MAAM;QACbM,IAAI,CAACc,KAAK,GAAGC,MAAM,CAACC,MAAM,CAAChB,IAAI,EAAEY,KAAK,CAACK,IAAI,CAAC;MAC9C,CAAC,CAAC;MACFC,YAAY,CAAC,CAAC;MACdC,aAAa,CAAC,CAAC;MACfC,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC;IAEF,MAAMC,SAAS,GAAGzB,GAAG,CAAC,EAAE,CAAC;IAE3B,MAAMsB,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC7B,MAAMI,GAAG,GAAG,MAAMC,KAAK,CAACC,KAAK,CAAC;QAC1BC,MAAM,EAAE,KAAK;QACbC,GAAG,EAAE;MACT,CAAC,CAAC;MACF,IAAIJ,GAAG,CAACnB,IAAI,IAAI,CAAC,EAAE;QACfkB,SAAS,CAACP,KAAK,GAAGQ,GAAG,CAACK,IAAI,CAACA,IAAI;MACnC;IACJ,CAAC;IAED,MAAMC,WAAW,GAAGhC,GAAG,CAAC,EAAE,CAAC;IAC3B,MAAMwB,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,MAAME,GAAG,GAAG,MAAMC,KAAK,CAACC,KAAK,CAAC;QAC5BC,MAAM,EAAE,KAAK;QACbC,GAAG,EAAE;MACP,CAAC,CAAC;MACF,IAAIJ,GAAG,CAACnB,IAAI,IAAI,CAAC,EAAE;QACjByB,WAAW,CAACd,KAAK,GAAGQ,GAAG,CAACK,IAAI,CAACA,IAAI;MACnC;IACF,CAAC;IAED,MAAM;MAAEJ;IAAM,CAAC,GAAG9B,kBAAkB,CAAC,CAAC;IACtC,MAAMoC,UAAU,GAAGjC,GAAG,CAAC,EAAE,CAAC;IAE1B,MAAMuB,aAAa,GAAG,MAAAA,CAAA,KAAY;MAC9B,MAAMG,GAAG,GAAG,MAAMC,KAAK,CAACC,KAAK,CAAC;QAC1BC,MAAM,EAAE,KAAK;QACbC,GAAG,EAAE;MACT,CAAC,CAAC;MACF,IAAIJ,GAAG,CAACnB,IAAI,IAAI,CAAC,EAAE;QACjB0B,UAAU,CAACf,KAAK,GAAGQ,GAAG,CAACK,IAAI,CAACA,IAAI;MAClC;IACJ,CAAC;IAECG,QAAY,CAAC;MAAE9B;IAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}