<?php
namespace app\common\jobs;

use app\common\cache\RedisLock;
use app\common\repository\LevelLogRepository;
use app\common\repository\LevelRepository;
use app\common\repository\PaymentRepository;
use app\common\repository\UserRepository;
use app\common\utils\Plan;
use think\facade\Db;
use think\facade\Log;
use think\queue\Job;


/**
 * 会员升级(充值)
 * Class LevelUpRechargeJob
 * @package app\job
 */
class LevelUpRechargeJob
{
    /**
     * 脚本对象
     * @var
     */
    protected $job;

    /**
     * 尝试次数
     * @var int
     */
    protected $tryMax = 5;

    /**
     * 变量信息
     * @var
     */
    protected $info;

    /**
     * 锁
     * @var
     */
    protected $RedisLock;

    /**
     * 执行
     * @param Job $job
     * @param $data
     */
    public function fire(Job $job, $data)
    {

        //redis锁
        $this->RedisLock  = new RedisLock();
        //对象
        $this->job        = $job;
        //信息
        $this->info       = $data;

        //最大尝试
        if (empty($this->maxTry()))
        {
            return;
        }

        try {

            //等锁
            if (empty($this->waitLock()))
            {
                return;
            }

            $this->exec();

            $job->delete();

        }catch (\Exception $exception)
        {
            Log::channel('job')->error($exception->getFile() . ' ' .  $exception->getLine() . ' ' . $exception->getMessage());
        }finally
        {
            $this->finishLock();
        }

    }


    /**
     * 执行
     */
    protected function exec()
    {
        $uid      = $this->info['uid'];
        $orderId  = $this->info['order_id'];

        $PaymentRepo = new PaymentRepository();
        $recharge    = $PaymentRepo->findById($orderId);


        if (empty($recharge))
        {
            return;
        }


        $UserRepo = new UserRepository();
        $user     = $UserRepo->findById($uid);


        if(empty($user))
        {
            return;
        }


        $LevelRepo          = new LevelRepository();
        $where              = [];
        $where[]            = ['id', '>', $user['level']];
        $level              = $LevelRepo->findByCondition($where,'*', ['id' => 'asc']);

        $money  = $recharge['amount_real'];

        if($money < $level['recharge'])
        {
            return;
        }


        Db::startTrans();

        try
        {
            $update = [
                'level'         => $level['id'],
                'level_name'    => $level['title'],
                'update_time'   => time()
            ];

            $res          = $UserRepo->updateById($user['id'], $update);

            if (!$res)
            {
                Db::rollback();
                return;
            }


            $LevelLogRepo = new LevelLogRepository();

            $insert  = [
                'uid'           => $user['id'],
                'username'      => $user['username'],
                'phone'         => $user['phone'],
                'is_test'       => $user['is_test'],
                'lv_id'         => $level['id'],
                'lv_name'       => $level['title'],
                'desc'          => '充值升级:从' . $user['level_name'] . '升级到' . $level['title'],
                'create_time'   => time(),
                'update_time'   => time()
            ];


            $res = $LevelLogRepo->inserts($insert);

            if (!$res)
            {
                Db::rollback();
                return;
            }

            $Plan = new Plan();
            $res  = $Plan->level($user['id']);


            // 提交事务
            Db::commit();


        } catch (\Exception $e)
        {
            // 回滚事务
            Db::rollback();
        }

    }


    public function failed(array $data)
    {
        // ...任务达到最大重试次数后，失败了
    }

    /**
     * 最大尝试
     */
    protected function maxTry(): bool
    {

        if ($this->job->attempts() > $this->tryMax)
        {
            $this->job->delete();
            return false;
        }
        else
        {
            return true;
        }

    }

    /**
     * 等锁
     * @return bool
     */
    protected function waitLock(): bool
    {
        $RedisLock   = new RedisLock();

        $status      = $RedisLock->wait('LevelUpRechargeJob:' . $this->info['uid'],20,20);

        if (empty($status))
        {
            return false;
        }
        else
        {
            return true;
        }
    }


    /**
     * 释放锁
     * @return void
     */
    protected function finishLock()
    {
        $this->RedisLock->unLock('LevelUpRechargeJob:' . $this->info['uid']);
    }


}

