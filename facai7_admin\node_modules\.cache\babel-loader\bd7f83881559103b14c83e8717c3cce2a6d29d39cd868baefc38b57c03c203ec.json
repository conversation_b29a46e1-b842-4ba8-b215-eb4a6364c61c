{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Malayalam [ml]\n//! author : <PERSON> : https://github.com/floydpink\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var ml = moment.defineLocale('ml', {\n    months: 'ജനുവരി_ഫെബ്രുവരി_മാർച്ച്_ഏപ്രിൽ_മേയ്_ജൂൺ_ജൂലൈ_ഓഗസ്റ്റ്_സെപ്റ്റംബർ_ഒക്ടോബർ_നവംബർ_ഡിസംബർ'.split('_'),\n    monthsShort: 'ജനു._ഫെബ്രു._മാർ._ഏപ്രി._മേയ്_ജൂൺ_ജൂലൈ._ഓഗ._സെപ്റ്റ._ഒക്ടോ._നവം._ഡിസം.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'ഞായറാഴ്ച_തിങ്കളാഴ്ച_ചൊവ്വാഴ്ച_ബുധനാഴ്ച_വ്യാഴാഴ്ച_വെള്ളിയാഴ്ച_ശനിയാഴ്ച'.split('_'),\n    weekdaysShort: 'ഞായർ_തിങ്കൾ_ചൊവ്വ_ബുധൻ_വ്യാഴം_വെള്ളി_ശനി'.split('_'),\n    weekdaysMin: 'ഞാ_തി_ചൊ_ബു_വ്യാ_വെ_ശ'.split('_'),\n    longDateFormat: {\n      LT: 'A h:mm -നു',\n      LTS: 'A h:mm:ss -നു',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY, A h:mm -നു',\n      LLLL: 'dddd, D MMMM YYYY, A h:mm -നു'\n    },\n    calendar: {\n      sameDay: '[ഇന്ന്] LT',\n      nextDay: '[നാളെ] LT',\n      nextWeek: 'dddd, LT',\n      lastDay: '[ഇന്നലെ] LT',\n      lastWeek: '[കഴിഞ്ഞ] dddd, LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s കഴിഞ്ഞ്',\n      past: '%s മുൻപ്',\n      s: 'അൽപ നിമിഷങ്ങൾ',\n      ss: '%d സെക്കൻഡ്',\n      m: 'ഒരു മിനിറ്റ്',\n      mm: '%d മിനിറ്റ്',\n      h: 'ഒരു മണിക്കൂർ',\n      hh: '%d മണിക്കൂർ',\n      d: 'ഒരു ദിവസം',\n      dd: '%d ദിവസം',\n      M: 'ഒരു മാസം',\n      MM: '%d മാസം',\n      y: 'ഒരു വർഷം',\n      yy: '%d വർഷം'\n    },\n    meridiemParse: /രാത്രി|രാവിലെ|ഉച്ച കഴിഞ്ഞ്|വൈകുന്നേരം|രാത്രി/i,\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n      if (meridiem === 'രാത്രി' && hour >= 4 || meridiem === 'ഉച്ച കഴിഞ്ഞ്' || meridiem === 'വൈകുന്നേരം') {\n        return hour + 12;\n      } else {\n        return hour;\n      }\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 4) {\n        return 'രാത്രി';\n      } else if (hour < 12) {\n        return 'രാവിലെ';\n      } else if (hour < 17) {\n        return 'ഉച്ച കഴിഞ്ഞ്';\n      } else if (hour < 20) {\n        return 'വൈകുന്നേരം';\n      } else {\n        return 'രാത്രി';\n      }\n    }\n  });\n  return ml;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "ml", "defineLocale", "months", "split", "monthsShort", "monthsParseExact", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "meridiemParse", "meridiemHour", "hour", "meridiem", "minute", "isLower"], "sources": ["D:/WorkSpace/facai7/facai7_admin/node_modules/moment/locale/ml.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Malayalam [ml]\n//! author : <PERSON> : https://github.com/floydpink\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var ml = moment.defineLocale('ml', {\n        months: 'ജനുവരി_ഫെബ്രുവരി_മാർച്ച്_ഏപ്രിൽ_മേയ്_ജൂൺ_ജൂലൈ_ഓഗസ്റ്റ്_സെപ്റ്റംബർ_ഒക്ടോബർ_നവംബർ_ഡിസംബർ'.split(\n            '_'\n        ),\n        monthsShort:\n            'ജനു._ഫെബ്രു._മാർ._ഏപ്രി._മേയ്_ജൂൺ_ജൂലൈ._ഓഗ._സെപ്റ്റ._ഒക്ടോ._നവം._ഡിസം.'.split(\n                '_'\n            ),\n        monthsParseExact: true,\n        weekdays:\n            'ഞായറാഴ്ച_തിങ്കളാഴ്ച_ചൊവ്വാഴ്ച_ബുധനാഴ്ച_വ്യാഴാഴ്ച_വെള്ളിയാഴ്ച_ശനിയാഴ്ച'.split(\n                '_'\n            ),\n        weekdaysShort: 'ഞായർ_തിങ്കൾ_ചൊവ്വ_ബുധൻ_വ്യാഴം_വെള്ളി_ശനി'.split('_'),\n        weekdaysMin: 'ഞാ_തി_ചൊ_ബു_വ്യാ_വെ_ശ'.split('_'),\n        longDateFormat: {\n            LT: 'A h:mm -നു',\n            LTS: 'A h:mm:ss -നു',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY, A h:mm -നു',\n            LLLL: 'dddd, D MMMM YYYY, A h:mm -നു',\n        },\n        calendar: {\n            sameDay: '[ഇന്ന്] LT',\n            nextDay: '[നാളെ] LT',\n            nextWeek: 'dddd, LT',\n            lastDay: '[ഇന്നലെ] LT',\n            lastWeek: '[കഴിഞ്ഞ] dddd, LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: '%s കഴിഞ്ഞ്',\n            past: '%s മുൻപ്',\n            s: 'അൽപ നിമിഷങ്ങൾ',\n            ss: '%d സെക്കൻഡ്',\n            m: 'ഒരു മിനിറ്റ്',\n            mm: '%d മിനിറ്റ്',\n            h: 'ഒരു മണിക്കൂർ',\n            hh: '%d മണിക്കൂർ',\n            d: 'ഒരു ദിവസം',\n            dd: '%d ദിവസം',\n            M: 'ഒരു മാസം',\n            MM: '%d മാസം',\n            y: 'ഒരു വർഷം',\n            yy: '%d വർഷം',\n        },\n        meridiemParse: /രാത്രി|രാവിലെ|ഉച്ച കഴിഞ്ഞ്|വൈകുന്നേരം|രാത്രി/i,\n        meridiemHour: function (hour, meridiem) {\n            if (hour === 12) {\n                hour = 0;\n            }\n            if (\n                (meridiem === 'രാത്രി' && hour >= 4) ||\n                meridiem === 'ഉച്ച കഴിഞ്ഞ്' ||\n                meridiem === 'വൈകുന്നേരം'\n            ) {\n                return hour + 12;\n            } else {\n                return hour;\n            }\n        },\n        meridiem: function (hour, minute, isLower) {\n            if (hour < 4) {\n                return 'രാത്രി';\n            } else if (hour < 12) {\n                return 'രാവിലെ';\n            } else if (hour < 17) {\n                return 'ഉച്ച കഴിഞ്ഞ്';\n            } else if (hour < 20) {\n                return 'വൈകുന്നേരം';\n            } else {\n                return 'രാത്രി';\n            }\n        },\n    });\n\n    return ml;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,wFAAwF,CAACC,KAAK,CAClG,GACJ,CAAC;IACDC,WAAW,EACP,wEAAwE,CAACD,KAAK,CAC1E,GACJ,CAAC;IACLE,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EACJ,uEAAuE,CAACH,KAAK,CACzE,GACJ,CAAC;IACLI,aAAa,EAAE,0CAA0C,CAACJ,KAAK,CAAC,GAAG,CAAC;IACpEK,WAAW,EAAE,uBAAuB,CAACL,KAAK,CAAC,GAAG,CAAC;IAC/CM,cAAc,EAAE;MACZC,EAAE,EAAE,YAAY;MAChBC,GAAG,EAAE,eAAe;MACpBC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,yBAAyB;MAC9BC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,YAAY;MACrBC,OAAO,EAAE,WAAW;MACpBC,QAAQ,EAAE,UAAU;MACpBC,OAAO,EAAE,aAAa;MACtBC,QAAQ,EAAE,mBAAmB;MAC7BC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,YAAY;MACpBC,IAAI,EAAE,UAAU;MAChBC,CAAC,EAAE,eAAe;MAClBC,EAAE,EAAE,aAAa;MACjBC,CAAC,EAAE,cAAc;MACjBC,EAAE,EAAE,aAAa;MACjBC,CAAC,EAAE,cAAc;MACjBC,EAAE,EAAE,aAAa;MACjBC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE;IACR,CAAC;IACDC,aAAa,EAAE,+CAA+C;IAC9DC,YAAY,EAAE,SAAAA,CAAUC,IAAI,EAAEC,QAAQ,EAAE;MACpC,IAAID,IAAI,KAAK,EAAE,EAAE;QACbA,IAAI,GAAG,CAAC;MACZ;MACA,IACKC,QAAQ,KAAK,QAAQ,IAAID,IAAI,IAAI,CAAC,IACnCC,QAAQ,KAAK,cAAc,IAC3BA,QAAQ,KAAK,YAAY,EAC3B;QACE,OAAOD,IAAI,GAAG,EAAE;MACpB,CAAC,MAAM;QACH,OAAOA,IAAI;MACf;IACJ,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUD,IAAI,EAAEE,MAAM,EAAEC,OAAO,EAAE;MACvC,IAAIH,IAAI,GAAG,CAAC,EAAE;QACV,OAAO,QAAQ;MACnB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,QAAQ;MACnB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,cAAc;MACzB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,YAAY;MACvB,CAAC,MAAM;QACH,OAAO,QAAQ;MACnB;IACJ;EACJ,CAAC,CAAC;EAEF,OAAOxC,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}