{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Catalan [ca]\n//! author : <PERSON> : https://github.com/juanghurtado\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var ca = moment.defineLocale('ca', {\n    months: {\n      standalone: 'gener_febrer_març_abril_maig_juny_juliol_agost_setembre_octubre_novembre_desembre'.split('_'),\n      format: \"de gener_de febrer_de març_d'abril_de maig_de juny_de juliol_d'agost_de setembre_d'octubre_de novembre_de desembre\".split('_'),\n      isFormat: /D[oD]?(\\s)+MMMM/\n    },\n    monthsShort: 'gen._febr._març_abr._maig_juny_jul._ag._set._oct._nov._des.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'diumenge_dilluns_dimarts_dimecres_dijous_divendres_dissabte'.split('_'),\n    weekdaysShort: 'dg._dl._dt._dc._dj._dv._ds.'.split('_'),\n    weekdaysMin: 'dg_dl_dt_dc_dj_dv_ds'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'H:mm',\n      LTS: 'H:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM [de] YYYY',\n      ll: 'D MMM YYYY',\n      LLL: 'D MMMM [de] YYYY [a les] H:mm',\n      lll: 'D MMM YYYY, H:mm',\n      LLLL: 'dddd D MMMM [de] YYYY [a les] H:mm',\n      llll: 'ddd D MMM YYYY, H:mm'\n    },\n    calendar: {\n      sameDay: function () {\n        return '[avui a ' + (this.hours() !== 1 ? 'les' : 'la') + '] LT';\n      },\n      nextDay: function () {\n        return '[demà a ' + (this.hours() !== 1 ? 'les' : 'la') + '] LT';\n      },\n      nextWeek: function () {\n        return 'dddd [a ' + (this.hours() !== 1 ? 'les' : 'la') + '] LT';\n      },\n      lastDay: function () {\n        return '[ahir a ' + (this.hours() !== 1 ? 'les' : 'la') + '] LT';\n      },\n      lastWeek: function () {\n        return '[el] dddd [passat a ' + (this.hours() !== 1 ? 'les' : 'la') + '] LT';\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: \"d'aquí %s\",\n      past: 'fa %s',\n      s: 'uns segons',\n      ss: '%d segons',\n      m: 'un minut',\n      mm: '%d minuts',\n      h: 'una hora',\n      hh: '%d hores',\n      d: 'un dia',\n      dd: '%d dies',\n      M: 'un mes',\n      MM: '%d mesos',\n      y: 'un any',\n      yy: '%d anys'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(r|n|t|è|a)/,\n    ordinal: function (number, period) {\n      var output = number === 1 ? 'r' : number === 2 ? 'n' : number === 3 ? 'r' : number === 4 ? 't' : 'è';\n      if (period === 'w' || period === 'W') {\n        output = 'a';\n      }\n      return number + output;\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return ca;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "ca", "defineLocale", "months", "standalone", "split", "format", "isFormat", "monthsShort", "monthsParseExact", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "ll", "LLL", "lll", "LLLL", "llll", "calendar", "sameDay", "hours", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "number", "period", "output", "week", "dow", "doy"], "sources": ["D:/WorkSpace/facai7/facai7_admin/node_modules/moment/locale/ca.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Catalan [ca]\n//! author : <PERSON> : https://github.com/juanghurtado\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var ca = moment.defineLocale('ca', {\n        months: {\n            standalone:\n                'gener_febrer_març_abril_maig_juny_juliol_agost_setembre_octubre_novembre_desembre'.split(\n                    '_'\n                ),\n            format: \"de gener_de febrer_de març_d'abril_de maig_de juny_de juliol_d'agost_de setembre_d'octubre_de novembre_de desembre\".split(\n                '_'\n            ),\n            isFormat: /D[oD]?(\\s)+MMMM/,\n        },\n        monthsShort:\n            'gen._febr._març_abr._maig_juny_jul._ag._set._oct._nov._des.'.split(\n                '_'\n            ),\n        monthsParseExact: true,\n        weekdays:\n            'diumenge_dilluns_dimarts_dimecres_dijous_divendres_dissabte'.split(\n                '_'\n            ),\n        weekdaysShort: 'dg._dl._dt._dc._dj._dv._ds.'.split('_'),\n        weekdaysMin: 'dg_dl_dt_dc_dj_dv_ds'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'H:mm',\n            LTS: 'H:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM [de] YYYY',\n            ll: 'D MMM YYYY',\n            LLL: 'D MMMM [de] YYYY [a les] H:mm',\n            lll: 'D MMM YYYY, H:mm',\n            LLLL: 'dddd D MMMM [de] YYYY [a les] H:mm',\n            llll: 'ddd D MMM YYYY, H:mm',\n        },\n        calendar: {\n            sameDay: function () {\n                return '[avui a ' + (this.hours() !== 1 ? 'les' : 'la') + '] LT';\n            },\n            nextDay: function () {\n                return '[demà a ' + (this.hours() !== 1 ? 'les' : 'la') + '] LT';\n            },\n            nextWeek: function () {\n                return 'dddd [a ' + (this.hours() !== 1 ? 'les' : 'la') + '] LT';\n            },\n            lastDay: function () {\n                return '[ahir a ' + (this.hours() !== 1 ? 'les' : 'la') + '] LT';\n            },\n            lastWeek: function () {\n                return (\n                    '[el] dddd [passat a ' +\n                    (this.hours() !== 1 ? 'les' : 'la') +\n                    '] LT'\n                );\n            },\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: \"d'aquí %s\",\n            past: 'fa %s',\n            s: 'uns segons',\n            ss: '%d segons',\n            m: 'un minut',\n            mm: '%d minuts',\n            h: 'una hora',\n            hh: '%d hores',\n            d: 'un dia',\n            dd: '%d dies',\n            M: 'un mes',\n            MM: '%d mesos',\n            y: 'un any',\n            yy: '%d anys',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}(r|n|t|è|a)/,\n        ordinal: function (number, period) {\n            var output =\n                number === 1\n                    ? 'r'\n                    : number === 2\n                      ? 'n'\n                      : number === 3\n                        ? 'r'\n                        : number === 4\n                          ? 't'\n                          : 'è';\n            if (period === 'w' || period === 'W') {\n                output = 'a';\n            }\n            return number + output;\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return ca;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE;MACJC,UAAU,EACN,mFAAmF,CAACC,KAAK,CACrF,GACJ,CAAC;MACLC,MAAM,EAAE,oHAAoH,CAACD,KAAK,CAC9H,GACJ,CAAC;MACDE,QAAQ,EAAE;IACd,CAAC;IACDC,WAAW,EACP,6DAA6D,CAACH,KAAK,CAC/D,GACJ,CAAC;IACLI,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EACJ,6DAA6D,CAACL,KAAK,CAC/D,GACJ,CAAC;IACLM,aAAa,EAAE,6BAA6B,CAACN,KAAK,CAAC,GAAG,CAAC;IACvDO,WAAW,EAAE,sBAAsB,CAACP,KAAK,CAAC,GAAG,CAAC;IAC9CQ,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,MAAM;MACVC,GAAG,EAAE,SAAS;MACdC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,kBAAkB;MACtBC,EAAE,EAAE,YAAY;MAChBC,GAAG,EAAE,+BAA+B;MACpCC,GAAG,EAAE,kBAAkB;MACvBC,IAAI,EAAE,oCAAoC;MAC1CC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,SAAAA,CAAA,EAAY;QACjB,OAAO,UAAU,IAAI,IAAI,CAACC,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,MAAM;MACpE,CAAC;MACDC,OAAO,EAAE,SAAAA,CAAA,EAAY;QACjB,OAAO,UAAU,IAAI,IAAI,CAACD,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,MAAM;MACpE,CAAC;MACDE,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,OAAO,UAAU,IAAI,IAAI,CAACF,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,MAAM;MACpE,CAAC;MACDG,OAAO,EAAE,SAAAA,CAAA,EAAY;QACjB,OAAO,UAAU,IAAI,IAAI,CAACH,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,MAAM;MACpE,CAAC;MACDI,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,OACI,sBAAsB,IACrB,IAAI,CAACJ,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,GACnC,MAAM;MAEd,CAAC;MACDK,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,WAAW;MACnBC,IAAI,EAAE,OAAO;MACbC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,oBAAoB;IAC5CC,OAAO,EAAE,SAAAA,CAAUC,MAAM,EAAEC,MAAM,EAAE;MAC/B,IAAIC,MAAM,GACNF,MAAM,KAAK,CAAC,GACN,GAAG,GACHA,MAAM,KAAK,CAAC,GACV,GAAG,GACHA,MAAM,KAAK,CAAC,GACV,GAAG,GACHA,MAAM,KAAK,CAAC,GACV,GAAG,GACH,GAAG;MACnB,IAAIC,MAAM,KAAK,GAAG,IAAIA,MAAM,KAAK,GAAG,EAAE;QAClCC,MAAM,GAAG,GAAG;MAChB;MACA,OAAOF,MAAM,GAAGE,MAAM;IAC1B,CAAC;IACDC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOrD,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}