{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Romanian [ro]\n//! author : <PERSON> : https://github.com/gurdiga\n//! author : <PERSON><PERSON> : https://github.com/avaly\n//! author : <PERSON> : https://github.com/cepem\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  function relativeTimeWithPlural(number, withoutSuffix, key) {\n    var format = {\n        ss: 'secunde',\n        mm: 'minute',\n        hh: 'ore',\n        dd: 'zile',\n        ww: 'săptămâni',\n        MM: 'luni',\n        yy: 'ani'\n      },\n      separator = ' ';\n    if (number % 100 >= 20 || number >= 100 && number % 100 === 0) {\n      separator = ' de ';\n    }\n    return number + separator + format[key];\n  }\n  var ro = moment.defineLocale('ro', {\n    months: 'ianuarie_februarie_martie_aprilie_mai_iunie_iulie_august_septembrie_octombrie_noiembrie_decembrie'.split('_'),\n    monthsShort: 'ian._feb._mart._apr._mai_iun._iul._aug._sept._oct._nov._dec.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'duminică_luni_marți_miercuri_joi_vineri_sâmbătă'.split('_'),\n    weekdaysShort: 'Dum_Lun_Mar_Mie_Joi_Vin_Sâm'.split('_'),\n    weekdaysMin: 'Du_Lu_Ma_Mi_Jo_Vi_Sâ'.split('_'),\n    longDateFormat: {\n      LT: 'H:mm',\n      LTS: 'H:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY H:mm',\n      LLLL: 'dddd, D MMMM YYYY H:mm'\n    },\n    calendar: {\n      sameDay: '[azi la] LT',\n      nextDay: '[mâine la] LT',\n      nextWeek: 'dddd [la] LT',\n      lastDay: '[ieri la] LT',\n      lastWeek: '[fosta] dddd [la] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'peste %s',\n      past: '%s în urmă',\n      s: 'câteva secunde',\n      ss: relativeTimeWithPlural,\n      m: 'un minut',\n      mm: relativeTimeWithPlural,\n      h: 'o oră',\n      hh: relativeTimeWithPlural,\n      d: 'o zi',\n      dd: relativeTimeWithPlural,\n      w: 'o săptămână',\n      ww: relativeTimeWithPlural,\n      M: 'o lună',\n      MM: relativeTimeWithPlural,\n      y: 'un an',\n      yy: relativeTimeWithPlural\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n  return ro;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "relativeTimeWithPlural", "number", "withoutSuffix", "key", "format", "ss", "mm", "hh", "dd", "ww", "MM", "yy", "separator", "ro", "defineLocale", "months", "split", "monthsShort", "monthsParseExact", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "m", "h", "d", "w", "M", "y", "week", "dow", "doy"], "sources": ["D:/WorkSpace/facai7/facai7_admin/node_modules/moment/locale/ro.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Romanian [ro]\n//! author : <PERSON> : https://github.com/gurdiga\n//! author : <PERSON><PERSON> : https://github.com/avaly\n//! author : <PERSON> : https://github.com/cepem\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    function relativeTimeWithPlural(number, withoutSuffix, key) {\n        var format = {\n                ss: 'secunde',\n                mm: 'minute',\n                hh: 'ore',\n                dd: 'zile',\n                ww: 'săptămâni',\n                MM: 'luni',\n                yy: 'ani',\n            },\n            separator = ' ';\n        if (number % 100 >= 20 || (number >= 100 && number % 100 === 0)) {\n            separator = ' de ';\n        }\n        return number + separator + format[key];\n    }\n\n    var ro = moment.defineLocale('ro', {\n        months: 'ianuarie_februarie_martie_aprilie_mai_iunie_iulie_august_septembrie_octombrie_noiembrie_decembrie'.split(\n            '_'\n        ),\n        monthsShort:\n            'ian._feb._mart._apr._mai_iun._iul._aug._sept._oct._nov._dec.'.split(\n                '_'\n            ),\n        monthsParseExact: true,\n        weekdays: 'duminică_luni_marți_miercuri_joi_vineri_sâmbătă'.split('_'),\n        weekdaysShort: 'Dum_Lun_Mar_Mie_Joi_Vin_Sâm'.split('_'),\n        weekdaysMin: 'Du_Lu_Ma_Mi_Jo_Vi_Sâ'.split('_'),\n        longDateFormat: {\n            LT: 'H:mm',\n            LTS: 'H:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY H:mm',\n            LLLL: 'dddd, D MMMM YYYY H:mm',\n        },\n        calendar: {\n            sameDay: '[azi la] LT',\n            nextDay: '[mâine la] LT',\n            nextWeek: 'dddd [la] LT',\n            lastDay: '[ieri la] LT',\n            lastWeek: '[fosta] dddd [la] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'peste %s',\n            past: '%s în urmă',\n            s: 'câteva secunde',\n            ss: relativeTimeWithPlural,\n            m: 'un minut',\n            mm: relativeTimeWithPlural,\n            h: 'o oră',\n            hh: relativeTimeWithPlural,\n            d: 'o zi',\n            dd: relativeTimeWithPlural,\n            w: 'o săptămână',\n            ww: relativeTimeWithPlural,\n            M: 'o lună',\n            MM: relativeTimeWithPlural,\n            y: 'un an',\n            yy: relativeTimeWithPlural,\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 7, // The week that contains Jan 7th is the first week of the year.\n        },\n    });\n\n    return ro;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,SAASC,sBAAsBA,CAACC,MAAM,EAAEC,aAAa,EAAEC,GAAG,EAAE;IACxD,IAAIC,MAAM,GAAG;QACLC,EAAE,EAAE,SAAS;QACbC,EAAE,EAAE,QAAQ;QACZC,EAAE,EAAE,KAAK;QACTC,EAAE,EAAE,MAAM;QACVC,EAAE,EAAE,WAAW;QACfC,EAAE,EAAE,MAAM;QACVC,EAAE,EAAE;MACR,CAAC;MACDC,SAAS,GAAG,GAAG;IACnB,IAAIX,MAAM,GAAG,GAAG,IAAI,EAAE,IAAKA,MAAM,IAAI,GAAG,IAAIA,MAAM,GAAG,GAAG,KAAK,CAAE,EAAE;MAC7DW,SAAS,GAAG,MAAM;IACtB;IACA,OAAOX,MAAM,GAAGW,SAAS,GAAGR,MAAM,CAACD,GAAG,CAAC;EAC3C;EAEA,IAAIU,EAAE,GAAGd,MAAM,CAACe,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,mGAAmG,CAACC,KAAK,CAC7G,GACJ,CAAC;IACDC,WAAW,EACP,8DAA8D,CAACD,KAAK,CAChE,GACJ,CAAC;IACLE,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE,iDAAiD,CAACH,KAAK,CAAC,GAAG,CAAC;IACtEI,aAAa,EAAE,6BAA6B,CAACJ,KAAK,CAAC,GAAG,CAAC;IACvDK,WAAW,EAAE,sBAAsB,CAACL,KAAK,CAAC,GAAG,CAAC;IAC9CM,cAAc,EAAE;MACZC,EAAE,EAAE,MAAM;MACVC,GAAG,EAAE,SAAS;MACdC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,kBAAkB;MACvBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,aAAa;MACtBC,OAAO,EAAE,eAAe;MACxBC,QAAQ,EAAE,cAAc;MACxBC,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE,sBAAsB;MAChCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,UAAU;MAClBC,IAAI,EAAE,YAAY;MAClBC,CAAC,EAAE,gBAAgB;MACnBlC,EAAE,EAAEL,sBAAsB;MAC1BwC,CAAC,EAAE,UAAU;MACblC,EAAE,EAAEN,sBAAsB;MAC1ByC,CAAC,EAAE,OAAO;MACVlC,EAAE,EAAEP,sBAAsB;MAC1B0C,CAAC,EAAE,MAAM;MACTlC,EAAE,EAAER,sBAAsB;MAC1B2C,CAAC,EAAE,aAAa;MAChBlC,EAAE,EAAET,sBAAsB;MAC1B4C,CAAC,EAAE,QAAQ;MACXlC,EAAE,EAAEV,sBAAsB;MAC1B6C,CAAC,EAAE,OAAO;MACVlC,EAAE,EAAEX;IACR,CAAC;IACD8C,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOnC,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}