# Facai7项目部署模式和开发环境配置分析

## 项目概述

Facai7是一个多端项目，包含以下三个主要模块：
- **facai7_api**: 后端API服务（基于ThinkPHP 6.x）
- **facai7_admin**: 管理后台（基于Vue.js）
- **facai7_app**: 移动端应用（基于uni-app）

## 部署模式分析

### 1. 环境配置模式

项目支持多种环境配置，通过不同的`.env`文件进行管理：

#### 1.1 本地开发环境 (.env.local)
```ini
APP_DEBUG = true
DATABASE = facai4
USERNAME = facai4
PASSWORD = 6ycwLNxKYzGPCTG8
```

#### 1.2 开发环境 (.env.dev)
```ini
APP_DEBUG = true
DATABASE = facai7
USERNAME = facai7
PASSWORD = 5aRTTSwAeyiRWxFt
```

#### 1.3 生产环境 (.env.prod)
```ini
APP_DEBUG = false
DATABASE = facai7_com
USERNAME = facai7_com
PASSWORD = 8pHM6ZzsK3iG2cNR
```

### 2. 前端代理配置

#### 2.1 管理后台代理配置
- **开发端口**: 8888
- **代理配置**: 通过vue.config.js配置跨域代理
- **API地址**: https://facai16-api.g20-1.com/admin.php/

#### 2.2 移动端代理配置
- **代理配置**: 通过vite.config.js配置
- **API代理**: `/api/v1` → `http://127.0.0.1:8099`

## 本地开发与远程同步方案

### 1. 当前支持的同步方式

#### 1.1 Git自动同步脚本
项目提供了自动代码更新脚本：

```bash
# facai7_api/private/sh/update_code.sh
#!/bin/bash
WORK_DIR="/path/to/your/repo"
cd $WORK_DIR || { echo "目录不存在"; exit 1; }

# 拉取最新的代码
echo "拉取最新的代码..."
git pull origin main

# 检查是否有未提交的更改
if [[ $(git status --porcelain) ]]; then
    echo "存在未提交的更改，请先处理这些更改。"
    exit 1
fi

# 运行必要的命令
# composer install
# npm install
```

#### 1.2 自动化部署脚本
- **数据库迁移**: `migrate.sh` - 自动检查并运行数据库迁移
- **依赖更新**: `update_composer.sh` - 监控composer.json变化并自动更新
- **定时任务**: `update_crontab.sh` - 自动更新crontab配置

### 2. 推荐的本地开发与远程同步配置

#### 2.1 开发环境数据库配置
您可以配置本地代码连接远程开发环境数据库：

```ini
# 修改本地.env文件
[DATABASE]
TYPE = mysql
HOSTNAME = [远程开发服务器IP]
DATABASE = facai7
USERNAME = facai7
PASSWORD = 5aRTTSwAeyiRWxFt
HOSTPORT = 3306
```

#### 2.2 代码同步方案
1. **Git Hooks方式**:
   - 配置post-merge钩子自动部署
   - 本地提交后自动推送到远程仓库
   - 远程服务器自动拉取更新

2. **定时同步方式**:
   - 使用crontab定时执行update_code.sh
   - 建议每分钟检查一次代码更新

## 测试和调试配置

### 1. API测试配置

#### 1.1 ThinkPHP测试框架
项目已集成ThinkPHP测试框架：
```bash
# 运行单元测试
php think unit
```

#### 1.2 调试配置
- **调试模式**: 通过APP_DEBUG控制
- **SQL监听**: 在开发环境自动开启
- **错误日志**: 记录在runtime目录

### 2. 前端调试配置

#### 2.1 管理后台调试
```bash
# 开发模式启动
npm run serve
# 访问地址: http://localhost:8888
```

#### 2.2 移动端调试
```bash
# 开发模式启动
npm run dev:h5
# 代理配置自动处理API请求
```

### 3. 数据库调试

#### 3.1 数据库连接测试
```php
// 测试数据库连接
php think migrate:status
```

#### 3.2 缓存调试
- **Redis连接**: 配置在cache.php
- **队列调试**: 支持Redis队列

## CI/CD配置

### 1. Travis CI配置
项目已配置Travis CI自动化构建：
- **PHP版本**: 支持多版本PHP
- **自动测试**: 运行单元测试
- **自动部署**: 构建发布包

### 2. 定时任务配置
项目包含完整的定时任务配置：
```bash
# 数据迁移
0 1 * * * php think CleanTable

# 项目相关任务
* * * * * php think ItemFish
* * * * * php think ItemProcess
* * * * * php think ItemSettle
* * * * * php think YueBao
```

## 推荐的开发工作流

### 1. 本地开发配置
1. 克隆项目到本地
2. 配置.env.local连接远程开发数据库
3. 安装依赖：`composer install` 和 `npm install`
4. 启动本地开发服务器

### 2. 代码同步流程
1. 本地开发完成后提交到Git
2. 推送到远程仓库
3. 远程服务器通过Git Hooks或定时任务自动拉取
4. 自动运行数据库迁移和依赖更新

### 3. 测试流程
1. 本地运行单元测试
2. 前端组件测试
3. 集成测试在开发环境进行
4. 生产环境部署前的最终测试

## 总结

当前项目配置已经支持您需要的开发模式：
- ✅ 多环境配置支持
- ✅ 本地代码连接远程数据库
- ✅ 自动化代码同步脚本
- ✅ 完整的测试和调试配置
- ✅ CI/CD自动化部署

建议按照推荐的开发工作流进行配置，可以实现高效的本地开发与远程同步。
