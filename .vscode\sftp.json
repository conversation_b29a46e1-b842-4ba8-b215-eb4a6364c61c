{"name": "facai7_api_server", "host": "**************", "protocol": "ftp", "port": 21, "username": "facai7", "remotePath": "/", "localPath": "./facai7_api", "uploadOnSave": false, "downloadOnOpen": false, "ignore": ["**/.vscode/**", "**/.git/**", "**/node_modules/**", "**/docs/**", "**/facai7_admin/**", "**/temp/**", "**/facai7_app/**", "**/runtime/log/**", "**/runtime/cache/**", "**/runtime/temp/**", "**/.env.local", "**/vendor/**", "**/composer.lock"], "watcher": {"files": "facai7_api/**/*", "autoUpload": false, "autoDelete": false}}