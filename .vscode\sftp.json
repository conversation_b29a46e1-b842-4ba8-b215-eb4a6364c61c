{"name": "facai7_api_server", "host": "*************", "protocol": "ftp", "port": 21, "username": "facai7", "password": "ZJNp72j6hh9R", "remotePath": "/", "connectTimeout": 30000, "keepalive": 10000, "passive": true, "localPath": "./facai7_api", "uploadOnSave": false, "downloadOnOpen": true, "ignore": ["**/.vscode/**", "**/.git/**", "**/node_modules/**", "**/docs/**", "**/facai7_admin/**", "**/temp/**", "**/facai7_app/**", "**/runtime/log/**", "**/runtime/cache/**", "**/runtime/temp/**", "**/.env.local", "**/.env.prod", "**/.env.dev", "**/vendor/**", "**/composer.lock"], "watcher": {"files": "facai7_api/**/*", "autoUpload": false, "autoDelete": false}}