{"ast": null, "code": "import baseGetTag from './_baseGetTag.js';\nimport isObjectLike from './isObjectLike.js';\n\n/** `Object#toString` result references. */\nvar dateTag = '[object Date]';\n\n/**\n * The base implementation of `_.isDate` without Node.js optimizations.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a date object, else `false`.\n */\nfunction baseIsDate(value) {\n  return isObjectLike(value) && baseGetTag(value) == dateTag;\n}\nexport default baseIsDate;", "map": {"version": 3, "names": ["baseGetTag", "isObjectLike", "dateTag", "baseIsDate", "value"], "sources": ["D:/WorkSpace/facai7/facai7_admin/node_modules/lodash-es/_baseIsDate.js"], "sourcesContent": ["import baseGetTag from './_baseGetTag.js';\nimport isObjectLike from './isObjectLike.js';\n\n/** `Object#toString` result references. */\nvar dateTag = '[object Date]';\n\n/**\n * The base implementation of `_.isDate` without Node.js optimizations.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a date object, else `false`.\n */\nfunction baseIsDate(value) {\n  return isObjectLike(value) && baseGetTag(value) == dateTag;\n}\n\nexport default baseIsDate;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;AACzC,OAAOC,YAAY,MAAM,mBAAmB;;AAE5C;AACA,IAAIC,OAAO,GAAG,eAAe;;AAE7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,KAAK,EAAE;EACzB,OAAOH,YAAY,CAACG,KAAK,CAAC,IAAIJ,UAAU,CAACI,KAAK,CAAC,IAAIF,OAAO;AAC5D;AAEA,eAAeC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}