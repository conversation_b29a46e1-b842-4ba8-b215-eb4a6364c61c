# SFTP配置优化建议

## 📋 当前配置分析

### ✅ 配置正确的部分：

1. **远程路径配置**：`"remotePath": "/"` 
   - ✅ 符合需求：远程服务器根目录就是facai7_api目录

2. **排除其他模块**：
   - ✅ `**/facai7_admin/**` - 排除管理后台
   - ✅ `**/facai7_app/**` - 排除移动端应用
   - ✅ `**/docs/**` - 排除文档目录
   - ✅ `**/temp/**` - 排除临时文件

3. **安全设置**：
   - ✅ `"uploadOnSave": false` - 避免意外上传

## 🔧 优化建议

### 1. 添加本地路径限制

```json
{
    "localPath": "./facai7_api"
}
```

**作用**：确保只同步facai7_api目录内容

### 2. 增加运行时文件排除

```json
"ignore": [
    "**/runtime/log/**",      // 排除日志文件
    "**/runtime/cache/**",    // 排除缓存文件
    "**/runtime/temp/**",     // 排除临时文件
    "**/.env.local",          // 排除本地环境配置
    "**/vendor/**",           // 排除Composer依赖（服务器单独安装）
    "**/composer.lock"        // 排除锁定文件
]
```

### 3. 添加文件监控配置

```json
"watcher": {
    "files": "facai7_api/**/*",
    "autoUpload": false,
    "autoDelete": false
}
```

## 🚀 推荐的完整配置

```json
{
    "name": "facai7_api_server",
    "host": "**************",
    "protocol": "ftp",
    "port": 21,
    "username": "facai7",
    "remotePath": "/",
    "localPath": "./facai7_api",
    "uploadOnSave": false,
    "downloadOnOpen": false,
    "ignore": [
        "**/.vscode/**",
        "**/.git/**",
        "**/node_modules/**",
        "**/docs/**",
        "**/facai7_admin/**",
        "**/temp/**",
        "**/facai7_app/**",
        "**/runtime/log/**",
        "**/runtime/cache/**",
        "**/runtime/temp/**",
        "**/.env.local",
        "**/vendor/**",
        "**/composer.lock"
    ],
    "watcher": {
        "files": "facai7_api/**/*",
        "autoUpload": false,
        "autoDelete": false
    }
}
```

## 📁 同步目录结构

### 本地结构：
```
d:\WorkSpace\facai7\
├── facai7_api/          ← 您负责的部分
│   ├── app/
│   ├── config/
│   ├── public/
│   ├── runtime/
│   └── ...
├── facai7_admin/        ← 不同步
├── facai7_app/          ← 不同步
└── docs/                ← 不同步
```

### 远程结构：
```
/ (远程服务器根目录)
├── app/
├── config/
├── public/
├── runtime/
└── ...
```

## 🔄 使用方法

### 1. 手动同步
```bash
# 在VSCode中
Ctrl+Shift+P → SFTP: Sync Local -> Remote
```

### 2. 上传单个文件
```bash
# 右键文件 → Upload
```

### 3. 批量上传
```bash
# 右键facai7_api目录 → Upload Folder
```

## ⚠️ 注意事项

### 1. 环境文件处理
- ❌ 不要同步 `.env.local`
- ✅ 确保远程服务器有正确的 `.env.prod` 或 `.env.dev`

### 2. 依赖管理
- ❌ 不要同步 `vendor/` 目录
- ✅ 在远程服务器运行 `composer install --no-dev`

### 3. 权限设置
- ✅ 确保 `runtime/` 目录在远程服务器有写权限
- ✅ 确保 `public/upload/` 目录有写权限

### 4. 缓存清理
同步后在远程服务器执行：
```bash
php think cache:clear
php think route:clear
```

## 🧪 测试同步

### 1. 测试连接
```bash
# VSCode命令面板
SFTP: Test Connection
```

### 2. 验证同步
1. 修改一个PHP文件
2. 右键 → Upload
3. 在远程服务器检查文件是否更新

### 3. 检查排除规则
确认以下文件/目录没有被同步：
- facai7_admin/
- facai7_app/
- runtime/log/
- .env.local

## 📊 同步工作流建议

### 开发流程：
1. **本地开发** → 修改facai7_api中的代码
2. **本地测试** → 在本地Apache环境测试
3. **手动同步** → 使用SFTP上传到测试服务器
4. **远程测试** → 在测试服务器验证功能
5. **清理缓存** → 远程执行缓存清理命令

### 批量同步：
```bash
# 同步整个API目录（首次或大量修改时）
右键 facai7_api → Upload Folder

# 同步单个文件（小修改时）
右键具体文件 → Upload
```

这样配置后，您就可以高效地只同步facai7_api部分的代码到远程服务器了！
