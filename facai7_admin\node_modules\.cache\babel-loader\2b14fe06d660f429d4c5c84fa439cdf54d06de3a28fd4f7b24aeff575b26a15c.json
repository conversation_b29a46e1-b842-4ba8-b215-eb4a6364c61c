{"ast": null, "code": "import { nextTick, onMounted, ref } from 'vue';\nimport { withdrawTypeEnums, getLabelByVal } from '@/config/enums';\nexport default {\n  __name: 'editPop',\n  props: ['item'],\n  setup(__props, {\n    expose: __expose\n  }) {\n    const form = ref({\n      title: \"\",\n      extra: \"\",\n      handling_fee: \"\",\n      team_invest: \"\",\n      recharge: \"\",\n      lv1_invite: \"\",\n      lv2_invite: \"\",\n      lv1_recharge: \"\",\n      lv2_recharge: \"\"\n    });\n    const props = __props;\n    onMounted(() => {\n      nextTick(() => {\n        form.value = Object.assign(form, props.item);\n      });\n    });\n    __expose({\n      form\n    });\n    const __returned__ = {\n      form,\n      props,\n      nextTick,\n      onMounted,\n      ref,\n      get withdrawTypeEnums() {\n        return withdrawTypeEnums;\n      },\n      get getLabelByVal() {\n        return getLabelByVal;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["nextTick", "onMounted", "ref", "withdrawTypeEnums", "getLabelByVal", "form", "title", "extra", "handling_fee", "team_invest", "recharge", "lv1_invite", "lv2_invite", "lv1_recharge", "lv2_recharge", "props", "__props", "value", "Object", "assign", "item", "__expose"], "sources": ["D:/WorkSpace/facai7/facai7_admin/src/views/userManage/components/memberLevel/editPop.vue"], "sourcesContent": ["<template>\r\n    <el-form label-width=\"180px\" :inline=\"true\" :model=\"form\" class=\"demo-form-inline\">\r\n        <el-form-item label=\"等级名称\" required>\r\n            <el-input v-model=\"form.title\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"收益加成\" required>\r\n            <el-input v-model=\"form.extra\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"提现手续费\" required>\r\n            <el-input v-model=\"form.handling_fee\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"团队投资\" required>\r\n            <el-input v-model=\"form.team_invest\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"单笔充值\" required>\r\n            <el-input v-model=\"form.recharge\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"一级返利\" required>\r\n            <el-input v-model=\"form.lv1_invite\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"二级返利\" required>\r\n            <el-input v-model=\"form.lv2_invite\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"一级充值返利\" required>\r\n            <el-input v-model=\"form.lv1_recharge\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"二级充值返利\" required>\r\n            <el-input v-model=\"form.lv2_recharge\" />\r\n        </el-form-item>\r\n    </el-form>\r\n</template>\r\n\r\n<script setup>\r\nimport { nextTick, onMounted, ref } from 'vue'\r\nimport { withdrawTypeEnums, getLabelByVal } from '@/config/enums'\r\n\r\nconst form = ref({\r\n    title: \"\",\r\n    extra: \"\",\r\n    handling_fee: \"\",\r\n    team_invest: \"\",\r\n    recharge: \"\",\r\n    lv1_invite: \"\",\r\n    lv2_invite: \"\",\r\n    lv1_recharge: \"\",\r\n    lv2_recharge: \"\",\r\n})\r\nconst props = defineProps(['item'])\r\n\r\nonMounted(() => {\r\n    nextTick(() => {\r\n        form.value = Object.assign(form, props.item)\r\n    })\r\n})\r\n\r\ndefineExpose({ form })\r\n\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.demo-form-inline {\r\n    justify-content: flex-start;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n}\r\n\r\n.demo-form-inline .el-input {\r\n    --el-input-width: 220px;\r\n}\r\n\r\n.demo-form-inline .el-select {\r\n    --el-select-width: 220px;\r\n}\r\n\r\n/deep/ .el-radio-group {\r\n    width: 220px;\r\n}\r\n\r\n.form-title {\r\n    text-align: left;\r\n    padding-left: 30px;\r\n    margin: 20px auto 10px;\r\n    height: 44px;\r\n    background-color: #f2f2f2;\r\n    border-radius: 5px;\r\n    line-height: 44px;\r\n    width: 100%;\r\n}\r\n\r\n/deep/ .el-form-item {\r\n    align-items: flex-start;\r\n}\r\n</style>"], "mappings": "AAiCA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,GAAG,QAAQ,KAAK;AAC9C,SAASC,iBAAiB,EAAEC,aAAa,QAAQ,gBAAgB;;;;;;;IAEjE,MAAMC,IAAI,GAAGH,GAAG,CAAC;MACbI,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAE,EAAE;MACdC,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE;IAClB,CAAC,CAAC;IACF,MAAMC,KAAK,GAAGC,OAAqB;IAEnCf,SAAS,CAAC,MAAM;MACZD,QAAQ,CAAC,MAAM;QACXK,IAAI,CAACY,KAAK,GAAGC,MAAM,CAACC,MAAM,CAACd,IAAI,EAAEU,KAAK,CAACK,IAAI,CAAC;MAChD,CAAC,CAAC;IACN,CAAC,CAAC;IAEFC,QAAY,CAAC;MAAEhB;IAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}