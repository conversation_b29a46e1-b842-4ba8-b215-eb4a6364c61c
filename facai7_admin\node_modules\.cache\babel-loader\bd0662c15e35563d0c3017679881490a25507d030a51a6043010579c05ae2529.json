{"ast": null, "code": "import { nextTick, onMounted, ref } from 'vue';\nimport { withdrawTypeEnums, getLabelByVal } from '@/config/enums';\nexport default {\n  __name: 'editPop',\n  props: ['item'],\n  setup(__props, {\n    expose: __expose\n  }) {\n    const form = ref({\n      phone: '',\n      default: '',\n      address_name: '',\n      address_phone: '',\n      address_city: '',\n      address_place: ''\n    });\n    const props = __props;\n    onMounted(() => {\n      nextTick(() => {\n        form.value = Object.assign(form, props.item);\n      });\n    });\n    __expose({\n      form\n    });\n    const __returned__ = {\n      form,\n      props,\n      nextTick,\n      onMounted,\n      ref,\n      get withdrawTypeEnums() {\n        return withdrawTypeEnums;\n      },\n      get getLabelByVal() {\n        return getLabelByVal;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["nextTick", "onMounted", "ref", "withdrawTypeEnums", "getLabelByVal", "form", "phone", "default", "address_name", "address_phone", "address_city", "address_place", "props", "__props", "value", "Object", "assign", "item", "__expose"], "sources": ["D:/WorkSpace/facai7/facai7_admin/src/views/userManage/components/userAddress/editPop.vue"], "sourcesContent": ["<template>\r\n    <el-form label-width=\"100px\" :inline=\"true\" :model=\"form\" class=\"demo-form-inline\">\r\n        <el-form-item label=\"后台手机\" required>\r\n            <el-input v-model=\"form.phone\" />\r\n        </el-form-item>\r\n        <!-- <el-form-item label=\"总充值\" required>\r\n            <el-input v-model=\"form.recharge_money\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"总提款\" required>\r\n            <el-input v-model=\"form.withdraw_money\" disabled />\r\n        </el-form-item> -->\r\n        \r\n        <el-form-item label=\"是否默认\" required>\r\n            <el-radio-group v-model=\"form.default\">\r\n                <el-radio :value=\"1\">是</el-radio>\r\n                <el-radio :value=\"0\">否</el-radio>\r\n            </el-radio-group>\r\n        </el-form-item>\r\n\r\n\r\n        <el-form-item label=\"姓名\" required>\r\n            <el-input v-model=\"form.address_name\" clearable />\r\n        </el-form-item>\r\n        <el-form-item label=\"手机号\" required>\r\n            <el-input v-model=\"form.address_phone\" clearable />\r\n        </el-form-item>\r\n        <el-form-item label=\"省市区\" required>\r\n            <el-input v-model=\"form.address_city\" clearable />\r\n        </el-form-item>\r\n        <el-form-item label=\"详细地址\" required>\r\n            <el-input v-model=\"form.address_place\" clearable />\r\n        </el-form-item>\r\n\r\n    </el-form>\r\n</template>\r\n\r\n<script setup>\r\nimport { nextTick, onMounted, ref } from 'vue'\r\nimport { withdrawTypeEnums, getLabelByVal } from '@/config/enums'\r\n\r\nconst form = ref({\r\n    phone: '',\r\n    default: '',\r\n    address_name: '',\r\n    address_phone: '',\r\n    address_city: '',\r\n    address_place: '',\r\n\r\n})\r\nconst props = defineProps(['item'])\r\n\r\nonMounted(() => {\r\n    nextTick(() => {\r\n        form.value = Object.assign(form, props.item)\r\n    })\r\n})\r\n\r\ndefineExpose({ form })\r\n\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.demo-form-inline {\r\n    justify-content: flex-start;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n}\r\n\r\n.demo-form-inline .el-input {\r\n    --el-input-width: 220px;\r\n}\r\n\r\n.demo-form-inline .el-select {\r\n    --el-select-width: 220px;\r\n}\r\n\r\n/deep/ .el-radio-group {\r\n    width: 220px;\r\n}\r\n\r\n.form-title {\r\n    text-align: left;\r\n    padding-left: 30px;\r\n    margin: 20px auto 10px;\r\n    height: 44px;\r\n    background-color: #f2f2f2;\r\n    border-radius: 5px;\r\n    line-height: 44px;\r\n}\r\n\r\n/deep/ .el-form-item {\r\n    align-items: flex-start;\r\n}\r\n</style>"], "mappings": "AAqCA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,GAAG,QAAQ,KAAK;AAC9C,SAASC,iBAAiB,EAAEC,aAAa,QAAQ,gBAAgB;;;;;;;IAEjE,MAAMC,IAAI,GAAGH,GAAG,CAAC;MACbI,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE;IAEnB,CAAC,CAAC;IACF,MAAMC,KAAK,GAAGC,OAAqB;IAEnCZ,SAAS,CAAC,MAAM;MACZD,QAAQ,CAAC,MAAM;QACXK,IAAI,CAACS,KAAK,GAAGC,MAAM,CAACC,MAAM,CAACX,IAAI,EAAEO,KAAK,CAACK,IAAI,CAAC;MAChD,CAAC,CAAC;IACN,CAAC,CAAC;IAEFC,QAAY,CAAC;MAAEb;IAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}