{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst visualHiddenProps = buildProps({\n  style: {\n    type: definePropType([String, Object, Array]),\n    default: () => ({})\n  }\n});\nexport { visualHiddenProps };", "map": {"version": 3, "names": ["visualHiddenProps", "buildProps", "style", "type", "definePropType", "String", "Object", "Array", "default"], "sources": ["../../../../../../packages/components/visual-hidden/src/visual-hidden.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\n\nimport type { StyleValue } from 'vue'\n\nexport const visualHiddenProps = buildProps({\n  style: {\n    type: definePropType<StyleValue>([String, Object, Array]),\n    default: () => ({}),\n  },\n} as const)\n"], "mappings": ";AACY,MAACA,iBAAiB,GAAGC,UAAU,CAAC;EAC1CC,KAAK,EAAE;IACLC,IAAI,EAAEC,cAAc,CAAC,CAACC,MAAM,EAAEC,MAAM,EAAEC,KAAK,CAAC,CAAC;IAC7CC,OAAO,EAAEA,CAAA,MAAO,EAAE;EACtB;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}