<?php
namespace app\admin\service;

use app\common\cache\OnlineUser;
use app\common\model\MoneyClass;
use app\common\repository\ItemOrderRepository;
use app\common\repository\MoneyLogRepository;
use app\common\repository\MoneyRepository;
use app\common\repository\PaymentRepository;
use app\common\repository\RealNameRepository;
use app\common\repository\SystemSetRepository;
use app\common\repository\SystemUserRepository;
use app\common\repository\UserInfoRepository;
use app\common\repository\UserRelationRepository;
use app\common\repository\UserRepository;
use app\common\repository\UserStateRepository;
use app\common\repository\WithdrawRepository;
use app\common\utils\Arrays;
use app\common\utils\Result;
use PHPGangsta_GoogleAuthenticator;
use think\facade\Filesystem;
use think\facade\Request;


class IndexService
{

    public function index()
    {

    }


    /**
     * 用户信息
     * @return array
     */
    public function systemUser(): array
    {
        $SystemUserRepo = new SystemUserRepository();
        $data           = $SystemUserRepo->userByHeader();
        $data           = Arrays::withOut($data,['password']);
        return Result::success($data);
    }

    /**
     * 统计数据
     * @param $starttime
     * @param $endtime
     * @param $isTest
     * @param $phone
     * @return array
     */
    /**
     * 统计数据
     * @param $starttime
     * @param $endtime
     * @param $isTest
     * @param $phone
     * @param $lv
     * @return array
     */
    public function welcome($starttime, $endtime, $isTest, $phone, $lv): array
    {
        $ItemOrderRepo    = new ItemOrderRepository();
        $MoneyLogRepo     = new MoneyLogRepository();
        $UserStateRepo    = new UserStateRepository();
        $UserInfoRepo     = new UserInfoRepository();
        $MoneyRepo        = new MoneyRepository();
        $UserRelationRepo = new UserRelationRepository();
        $UserRepo         = new UserRepository();
        $PaymentRepo      = new PaymentRepository();
        $WithdrawRepo     = new WithdrawRepository();

        $history          = strtotime(date('Y-m-d 00:00:00', 1));

        $today            = strtotime(date('Y-m-d 00:00:00')) ;
        $tomorrow         = strtotime(date('Y-m-d 00:00:00', strtotime('+1 day', Request::time())));


        if ($starttime)
        {
            $starttime = strtotime($starttime);
        }
        else
        {
            $starttime = $history < 0 ? 0 : $history;
        }


        if ($endtime)
        {
            $endtime = strtotime($endtime);
        }
        else
        {
            $endtime = $tomorrow;
        }



        $members = [];

        if ($phone)
        {
            $uid      = $UserRepo->valueByCondition(['phone' => $phone],'id');

            $where    = [];
            $where[]  = ['top_id', '=', $uid];
            $where[]  = ['level', '<=', $lv];

            $members          = $UserRelationRepo->selectByCondition($where,'uid');
            $members          = array_column($members, 'uid');
        }

        $data               = [];

        if ($members)
        {
            //1.今日充值金额
            $data['today_recharge_money']       = $MoneyLogRepo->statistics($today, $tomorrow, [MoneyClass::RECHARGE,], $isTest, $members);

            //2今日提现金额
            $data['today_withdraw_money']       = $MoneyLogRepo->statistics($today, $tomorrow, [MoneyClass::WITHDRAW,], $isTest, $members);

            //3今日收益总额
            $data['today_shouyi_money']         = $MoneyLogRepo->statistics($today, $tomorrow, [MoneyClass::INCOME,], $isTest, $members);

            //4今日注册人数
            $data['today_zhuceshu']             = $UserStateRepo->registerUsers($today, $tomorrow, $isTest, $members);

            //5今日实名人数
            $data['today_shiming']              = $UserStateRepo->realNameUsers($today, $tomorrow, $isTest, $members);

            //6今日充值人数
            $data['today_chongzhi_shu']         = $MoneyLogRepo->uniqCountBy($today, $tomorrow, [MoneyClass::RECHARGE,],'uid', $isTest, $members);

            //7今日订阅金额
            $data['today_dingyue_jiner']        = $MoneyLogRepo->statistics($today, $tomorrow, [MoneyClass::BUY,], $isTest, $members);

            //8今日订阅订单数
            $data['today_dingyue_shu']          = $MoneyLogRepo->countBy($today, $tomorrow, [MoneyClass::BUY,], $isTest, $members);

            //9今日订阅人数
            $data['today_dingyue_reshu']        = $ItemOrderRepo->uniqCountBy($today, $tomorrow,'uid', $isTest, $members);

            //10今日抽奖红包
            $data['today_choujiang_hongbao']    = $MoneyLogRepo->statistics($today, $tomorrow, [MoneyClass::RAFFLE,], $isTest, $members);

            //11今日签到人数
            $data['today_qiandao_renshu']       = $MoneyLogRepo->uniqCountBy($today, $tomorrow, [MoneyClass::SIGNIN,],'uid',  $isTest, $members);

            //12今日上分
            $data['today_shangfen']             = $MoneyLogRepo->statistics($today, $tomorrow, [MoneyClass::RECHARGE],$isTest, $members);

            //今日USDT充值 USDT数量不需要换算
            $data['today_usdt_recharge']        = $PaymentRepo->usdtRecharge($today, $tomorrow, $isTest, $members);

            //今日U充值笔数
            $data['today_usdt_recharge_num']    = $PaymentRepo->usdtRechargeNum($today, $tomorrow, $isTest, $members);

            //今日RMB充值
            $data['today_rmb_recharge']         = $PaymentRepo->rmbRecharge($today, $tomorrow, $isTest, $members);

            //今日RMB充值笔数
            $data['today_rmb_recharge_num']     = $PaymentRepo->rmbRechargeNum($today, $tomorrow, $isTest, $members);

            //26今日首存人数
            $data['today_first_recharge_ren']   = $UserInfoRepo->firstRecharge($starttime, $tomorrow, $isTest, $members);

            //今日USDT提现 USDT数量不需要换算
            $data['today_usdt_withdraw']        = $WithdrawRepo->usdtWithdraw($today, $tomorrow, $isTest, $members);

            //今日U提现笔数
            $data['today_usdt_withdraw_num']    = $WithdrawRepo->usdtWithdrawNum($today, $tomorrow, $isTest, $members);

            //今日RMB提现
            $data['today_rmb_withdraw']         = $WithdrawRepo->rmbWithdraw($today, $tomorrow, $isTest, $members);

            //今日RMB提现笔数
            $data['today_rmb_withdraw_num']     = $WithdrawRepo->rmbWithdrawNum($today, $tomorrow, $isTest, $members);

            //13总充值
            $data['total_chongzhi']             = $UserInfoRepo->totalRecharge($starttime, $endtime, $isTest, $members);

            //14总提现
            $data['total_tixian']               = $UserInfoRepo->totalWithdraw($starttime, $endtime, $isTest, $members);

            //15总收益
            $data['total_shouyi']               = $MoneyLogRepo->statistics($starttime, $endtime, [MoneyClass::INCOME], $isTest, $members);

            //16总注册人数
            $data['total_zhuce']                = $UserStateRepo->registerUsers($starttime,$endtime,$isTest,$members);

            //17总实名人数
            $data['total_shiming']              = $UserStateRepo->realNameUsers($starttime,$endtime,$isTest,$members);

            //18总充值人数
            $data['total_chongzhi_reshu']       = $UserInfoRepo->totalRechargeUsers($starttime,$endtime,$isTest, $members);

            //19总订阅金额
            $data['total_dingyue_jiner']        = $UserInfoRepo->totalItemMoney($starttime,$endtime,$isTest, $members);

            //20总订阅订单数
            $data['total_dingyue_shu']          = $ItemOrderRepo->totalItemNum($starttime, $endtime, $isTest, $members);

            //21总订阅人数
            $data['total_dingyue_ershu']        = $UserInfoRepo->totalInvestUsers($starttime, $endtime, $isTest, $members);

            //22总抽奖红包
            $data['total_choujiang_hongbao']    = $UserInfoRepo->totalRaffleMoney($starttime, $endtime, $isTest, $members);

            //23总签到人数
            $data['total_qiandao_renshu']       = $UserInfoRepo->totalSignInUsers($starttime, $endtime, $isTest, $members);

            //24总上分
            $data['total_shangfen']             = $UserInfoRepo->totalRecharge($starttime, $endtime, $isTest, $members);

            //25总余额
            $data['total_yuer']                 = $MoneyRepo->totalMoney($starttime, $endtime, $isTest, $members);

            //总USDT充值 USDT数量不需要换算
            $data['total_usdt_recharge']        = $PaymentRepo->usdtRechargeReal($starttime, $endtime, $isTest, $members);

            //总RMB充值
            $data['total_rmb_recharge']         = $PaymentRepo->rmbRecharge($starttime, $endtime, $isTest, $members);

            //总USDT提现 USDT数量不需要换算
            $data['total_usdt_withdraw']        = $WithdrawRepo->usdtWithdrawReal($starttime, $endtime, $isTest, $members);

            //总RMB提现
            $data['total_rmb_withdraw']         = $WithdrawRepo->rmbWithdraw($starttime, $endtime, $isTest, $members);

            //明天分红
            $data['tomorrow_earn']              = $ItemOrderRepo->tomorrowEarn($isTest, $members);

            //明天本金
            $data['tomorrow_capital']           = $ItemOrderRepo->tomorrowCapital($isTest, $members);

            //总余额宝
            $data['total_yuebao']               = $UserInfoRepo->totalYueBao($starttime, $endtime, $isTest, $members);

        }
        else
        {

            //1.今日充值金额
            $data['today_recharge_money']       = 0;
            //2今日提现金额
            $data['today_withdraw_money']       = 0;
            //3今日收益总额
            $data['today_shouyi_money']         = 0;
            //4今日注册人数
            $data['today_zhuceshu']             = 0;
            //5今日实名人数
            $data['today_shiming']              = 0;
            //6今日充值人数
            $data['today_chongzhi_shu']         = 0;
            //7今日订阅金额
            $data['today_dingyue_jiner']        = 0;
            //8今日订阅订单数
            $data['today_dingyue_shu']          = 0;
            //9今日订阅人数
            $data['today_dingyue_reshu']        = 0;
            //10今日抽奖红包
            $data['today_choujiang_hongbao']    = 0;
            //11今日签到人数
            $data['today_qiandao_renshu']       = 0;
            //12今日上分
            $data['today_shangfen']             = 0;
            //13总充值
            $data['total_chongzhi']             = 0;
            //14总提现
            $data['total_tixian']               = 0;
            //15总收益
            $data['total_shouyi']               = 0;
            //16总注册人数
            $data['total_zhuce']                = 0;
            //17总实名人数
            $data['total_shiming']              = 0;
            //18总充值人数
            $data['total_chongzhi_reshu']       = 0;
            //19总订阅金额
            $data['total_dingyue_jiner']        = 0;
            //20总订阅订单数
            $data['total_dingyue_shu']          = 0;
            //21总订阅人数
            $data['total_dingyue_ershu']        = 0;
            //22总抽奖红包
            $data['total_choujiang_hongbao']    = 0;
            //23总签到人数
            $data['total_qiandao_renshu']       = 0;
            //24总上分
            $data['total_shangfen']             = 0;
            //25总余额
            $data['total_yuer']                 = 0;
            //26今日首存人数
            $data['today_first_recharge_ren']   = 0;
            //今日USDT充值 USDT数量不需要换算
            $data['today_usdt_recharge']        = 0;
            //今日U充值笔数
            $data['today_usdt_recharge_num']    = 0;
            //今日RMB充值
            $data['today_rmb_recharge']         = 0;
            //今日RMB充值笔数
            $data['today_rmb_recharge_num']     = 0;
            //今日USDT提现 USDT数量不需要换算
            $data['today_usdt_withdraw']        = 0;
            //今日U提现笔数
            $data['today_usdt_withdraw_num']    = 0;
            //今日RMB提现
            $data['today_rmb_withdraw']         = 0;
            //今日RMB提现笔数
            $data['today_rmb_withdraw_num']     = 0;
            //总USDT充值 USDT数量不需要换算
            $data['total_usdt_recharge']        = 0;
            //总RMB充值
            $data['total_rmb_recharge']         = 0;
            //总USDT提现 USDT数量不需要换算
            $data['total_usdt_withdraw']        = 0;
            //总RMB提现
            $data['total_rmb_withdraw']         = 0;
            //明天分红
            $data['tomorrow_earn']              = 0;
            //明天本金
            $data['tomorrow_capital']           = 0;
            //总余额宝
            $data['total_yuebao']               = 0;
        }


        return Result::success($data);
    }

    public function setting(string $key): array
    {
        $SystemSetRepo   = new SystemSetRepository();
        $where           = [];
        $where[]         = ['key','=', $key];
        $data            = $SystemSetRepo->valueByCondition($where,'val');

        return  Result::success(['val' => $data]);
    }

    /**
     * 上传
     * @param $file
     * @return array
     */
    public function upload($file): array
    {
        $info           = Filesystem::putFile('', $file);

        $info           = str_replace('\\','/',$info);
        $info           = '/upload/' . $info;

        return  Result::success(['url' => $info]);
    }


    /**
     * 上传
     * @param $file
     * @return array
     */
    public function uploadX($file): array
    {
        $info           = Filesystem::putFile('edit', $file);

        $info           = str_replace('\\','/',$info);
        $info           = '/upload/' . $info;

        return  Result::success(['url' => $info]);
    }


    /**
     * 查询提现和充值订单
     * @return array
     */
    public function notice(): array
    {
        $PaymentRepo  = new PaymentRepository();
        $where        = [];
        $where[]      = ['status','=', 1];
        $where[]      = ['is_voice','=', 0];
        $recharge     = $PaymentRepo->countByCondition($where);

        $WithdrawRepo  = new WithdrawRepository();
        $withdraw      = $WithdrawRepo->countByCondition(['status' => 1]);

        $RealNameRepo  = new RealNameRepository();
        $idCard        = $RealNameRepo->countByCondition(['status' => 1]);

        $data = [
            'recharge'     => $recharge,
            'withdraw'     => $withdraw,
            'idcard'       => $idCard,
        ];

        return  Result::success($data);
    }


    public function googleEdit(): array
    {
        $GoogleAuthenticator  = new  PHPGangsta_GoogleAuthenticator();

        try {

            $secret          = $GoogleAuthenticator->createSecret();
            $SystemSetRepo   = new SystemSetRepository();
            $where           = [];
            $where[]         = ['key','=','google'];
            $SystemSetRepo->updateByCondition($where,['val' => $secret]);
            return  Result::success();

        } catch (\Exception $e)
        {

            return Result::fail($e->getMessage());
        }

    }


    /**
     * @return array
     */
    public function googleQrcode(): array
    {
        $SystemSetRepo        = new SystemSetRepository();
        $data                 = $SystemSetRepo->valueByCondition(['key' => 'google'],'val');

        $GoogleAuthenticator  = new PHPGangsta_GoogleAuthenticator();
        $qrCode               = $GoogleAuthenticator->getQRCodeGoogleUrl('Admin', $data);

        return  Result::success(['val' => $qrCode , 'pin' => $data]);
    }


    public function onlineUser(): array
    {
        $OnlineUser = new OnlineUser();

        $data = [
            'count' => $OnlineUser->count(),
            'list'  => $OnlineUser->list(0,100000),
        ];

        return Result::success($data);
    }

}
