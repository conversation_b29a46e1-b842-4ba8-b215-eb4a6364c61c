<?php

/**
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

/*
 * Authors: <AUTHORS>
 */
return array_replace_recursive(require __DIR__.'/en.php', [
    'formats' => [
        'L' => 'YYYY-MM-DD',
    ],
    'months' => ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'Herbštmánet', 'Wímánet', 'Wintermánet', 'Chrištmánet'],
    'months_short' => ['<PERSON>', 'Hor', '<PERSON>är', 'Abr', 'Mei', 'Br<PERSON>', 'Hei', '<PERSON><PERSON>', 'Her', 'Wím', 'Win', 'Chr'],
    'weekdays' => ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>ita<PERSON>', '<PERSON><PERSON><PERSON>'],
    'weekdays_short' => ['<PERSON>', '<PERSON>än', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>i', '<PERSON>'],
    'weekdays_min' => ['<PERSON>', '<PERSON>än', 'Zis', 'Mit', 'Fro', 'Fri', 'Sam'],
    'first_day_of_week' => 1,
    'day_of_first_week_of_year' => 4,

    'month' => ':count Maano', // less reliable
    'm' => ':count Maano', // less reliable
    'a_month' => ':count Maano', // less reliable
]);
