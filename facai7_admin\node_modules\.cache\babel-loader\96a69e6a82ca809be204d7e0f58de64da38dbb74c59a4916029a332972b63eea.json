{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createCommentVNode as _createCommentVNode, openBlock as _openBlock, createBlock as _createBlock } from \"vue\";\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createBlock(_component_el_form, {\n    \"label-width\": \"100px\",\n    inline: true,\n    model: $setup.form,\n    class: \"demo-form-inline\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_form_item, {\n      label: \"银行名称\",\n      required: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.form.title,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.form.title = $event),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" <el-form-item\\r\\n      label=\\\"银行logo\\\"\\r\\n      prop=\\\"img\\\"\\r\\n      :rules=\\\"[{ required: true, message: '请上传图片' }]\\\"\\r\\n    >\\r\\n      <el-upload\\r\\n        class=\\\"upload-demo\\\"\\r\\n        style=\\\"width: 114px\\\"\\r\\n        :show-file-list=\\\"false\\\"\\r\\n        drag\\r\\n        :headers=\\\"headers\\\"\\r\\n        :action=\\\"`${proxy.BASE_API_URL}index/upload`\\\"\\r\\n        :on-success=\\\"successUpload\\\"\\r\\n        :on-error=\\\"handleErr\\\"\\r\\n        :multiple=\\\"false\\\"\\r\\n      >\\r\\n        <img\\r\\n          v-if=\\\"form.img\\\"\\r\\n          :src=\\\"proxy.IMG_BASE_URL + form.img\\\"\\r\\n          width=\\\"100%\\\"\\r\\n          class=\\\"avatar\\\" />\\r\\n        <el-icon v-else class=\\\"avatar-uploader-icon\\\"><Plus /></el-icon\\r\\n      ></el-upload>\\r\\n    </el-form-item> \")]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\"]);\n}", "map": {"version": 3, "names": ["_createBlock", "_component_el_form", "inline", "model", "$setup", "form", "class", "_createVNode", "_component_el_form_item", "label", "required", "_component_el_input", "title", "$event", "clearable", "_createCommentVNode"], "sources": ["D:\\WorkSpace\\facai7\\facai7_admin\\src\\views\\payments\\components\\bankList\\editPop.vue"], "sourcesContent": ["<template>\r\n  <el-form\r\n    label-width=\"100px\"\r\n    :inline=\"true\"\r\n    :model=\"form\"\r\n    class=\"demo-form-inline\"\r\n  >\r\n    <el-form-item label=\"银行名称\" required>\r\n      <el-input v-model=\"form.title\" clearable />\r\n    </el-form-item>\r\n\r\n\r\n    <!-- <el-form-item\r\n      label=\"银行logo\"\r\n      prop=\"img\"\r\n      :rules=\"[{ required: true, message: '请上传图片' }]\"\r\n    >\r\n      <el-upload\r\n        class=\"upload-demo\"\r\n        style=\"width: 114px\"\r\n        :show-file-list=\"false\"\r\n        drag\r\n        :headers=\"headers\"\r\n        :action=\"`${proxy.BASE_API_URL}index/upload`\"\r\n        :on-success=\"successUpload\"\r\n        :on-error=\"handleErr\"\r\n        :multiple=\"false\"\r\n      >\r\n        <img\r\n          v-if=\"form.img\"\r\n          :src=\"proxy.IMG_BASE_URL + form.img\"\r\n          width=\"100%\"\r\n          class=\"avatar\" />\r\n        <el-icon v-else class=\"avatar-uploader-icon\"><Plus /></el-icon\r\n      ></el-upload>\r\n    </el-form-item> -->\r\n  </el-form>\r\n</template>\r\n\r\n<script setup>\r\nimport { nextTick, onMounted, ref, getCurrentInstance } from \"vue\";\r\n// import { rolesEnums } from \"@/config/enums\";\r\n// import { getTokenAUTH } from \"@/utils/auth\";\r\n\r\nconst form = ref({\r\n  title: \"\",\r\n  // img: \"\",\r\n});\r\n// const { proxy } = getCurrentInstance();\r\nconst props = defineProps([\"item\"]);\r\n\r\n// const headers = ref({})\r\nonMounted(() => {\r\n  // headers.value['Accept-Token'] = getTokenAUTH()\r\n  nextTick(() => {\r\n    form.value = Object.assign(form, props.item);\r\n  });\r\n});\r\n\r\n// const successUpload = (res) => {\r\n//   form.value.img = res.data.url;\r\n// };\r\n\r\nconst handleErr = (err) => {\r\n  if (err.status == 320) {\r\n    form.value.img = JSON.parse(err.message).data.url;\r\n  }\r\n}\r\n\r\ndefineExpose({ form });\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.demo-form-inline {\r\n  justify-content: flex-start;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n.demo-form-inline .el-input {\r\n  --el-input-width: 220px;\r\n}\r\n\r\n.demo-form-inline .el-select {\r\n  --el-select-width: 220px;\r\n}\r\n/deep/ .el-radio-group {\r\n  width: 220px;\r\n}\r\n.form-title {\r\n  text-align: left;\r\n  padding-left: 30px;\r\n  margin: 20px auto 10px;\r\n  height: 44px;\r\n  background-color: #f2f2f2;\r\n  border-radius: 5px;\r\n  line-height: 44px;\r\n}\r\n/deep/ .el-form-item {\r\n  align-items: flex-start;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;uBACEA,YAAA,CAmCUC,kBAAA;IAlCR,aAAW,EAAC,OAAO;IAClBC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IACZC,KAAK,EAAC;;sBAEN,MAEe,CAFfC,YAAA,CAEeC,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,QAAQ,EAAR;;wBACzB,MAA2C,CAA3CH,YAAA,CAA2CI,mBAAA;oBAAxBP,MAAA,CAAAC,IAAI,CAACO,KAAK;mEAAVR,MAAA,CAAAC,IAAI,CAACO,KAAK,GAAAC,MAAA;QAAEC,SAAS,EAAT;;;QAIjCC,mBAAA,8vBAuBmB,C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}