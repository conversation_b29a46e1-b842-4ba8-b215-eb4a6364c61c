{"ast": null, "code": "import arrayMap from './_arrayMap.js';\nimport baseIntersection from './_baseIntersection.js';\nimport baseRest from './_baseRest.js';\nimport castArrayLikeObject from './_castArrayLikeObject.js';\n\n/**\n * Creates an array of unique values that are included in all given arrays\n * using [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * for equality comparisons. The order and references of result values are\n * determined by the first array.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {...Array} [arrays] The arrays to inspect.\n * @returns {Array} Returns the new array of intersecting values.\n * @example\n *\n * _.intersection([2, 1], [2, 3]);\n * // => [2]\n */\nvar intersection = baseRest(function (arrays) {\n  var mapped = arrayMap(arrays, castArrayLikeObject);\n  return mapped.length && mapped[0] === arrays[0] ? baseIntersection(mapped) : [];\n});\nexport default intersection;", "map": {"version": 3, "names": ["arrayMap", "baseIntersection", "baseRest", "castArrayLikeObject", "intersection", "arrays", "mapped", "length"], "sources": ["D:/WorkSpace/facai7/facai7_admin/node_modules/lodash-es/intersection.js"], "sourcesContent": ["import arrayMap from './_arrayMap.js';\nimport baseIntersection from './_baseIntersection.js';\nimport baseRest from './_baseRest.js';\nimport castArrayLikeObject from './_castArrayLikeObject.js';\n\n/**\n * Creates an array of unique values that are included in all given arrays\n * using [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * for equality comparisons. The order and references of result values are\n * determined by the first array.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {...Array} [arrays] The arrays to inspect.\n * @returns {Array} Returns the new array of intersecting values.\n * @example\n *\n * _.intersection([2, 1], [2, 3]);\n * // => [2]\n */\nvar intersection = baseRest(function(arrays) {\n  var mapped = arrayMap(arrays, castArrayLikeObject);\n  return (mapped.length && mapped[0] === arrays[0])\n    ? baseIntersection(mapped)\n    : [];\n});\n\nexport default intersection;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,gBAAgB,MAAM,wBAAwB;AACrD,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,mBAAmB,MAAM,2BAA2B;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,YAAY,GAAGF,QAAQ,CAAC,UAASG,MAAM,EAAE;EAC3C,IAAIC,MAAM,GAAGN,QAAQ,CAACK,MAAM,EAAEF,mBAAmB,CAAC;EAClD,OAAQG,MAAM,CAACC,MAAM,IAAID,MAAM,CAAC,CAAC,CAAC,KAAKD,MAAM,CAAC,CAAC,CAAC,GAC5CJ,gBAAgB,CAACK,MAAM,CAAC,GACxB,EAAE;AACR,CAAC,CAAC;AAEF,eAAeF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}