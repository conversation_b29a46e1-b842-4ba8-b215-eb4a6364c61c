{"ast": null, "code": "import { rolesEnums, booleanEnums, showStatusEnums } from \"@/config/enums\";\nimport { htmlDecodeByRegExp } from \"@/utils/utils\";\nimport { onBeforeUnmount, nextTick, ref, shallowRef, onMounted, getCurrentInstance } from \"vue\";\nimport { getTokenAUTH } from \"@/utils/auth\";\nexport default {\n  __name: 'editPop',\n  props: [\"item\"],\n  setup(__props, {\n    expose: __expose\n  }) {\n    const form = ref({\n      title: \"\",\n      class_id: \"\",\n      is_recommend: \"\",\n      sort: \"\",\n      price: \"\",\n      people: \"\",\n      // process_time: \"\",\n      people_add: \"\",\n      // process_update_time: \"\",\n      fail_money: \"\",\n      limit: \"\",\n      level: \"\",\n      item_money: \"\",\n      status: \"\",\n      next_time: \"\",\n      cycle: \"\",\n      desc: \"\",\n      good_id: \"\"\n    });\n    const props = __props;\n    const headers = ref({});\n    onMounted(() => {\n      headers.value[\"Accept-Token\"] = getTokenAUTH();\n      nextTick(() => {\n        form.value = Object.assign(form.value, props.item);\n      });\n      getTYpesEnum();\n      getGoodsList();\n    });\n    const {\n      proxy\n    } = getCurrentInstance();\n    const typesEnum = ref([]);\n    const getTYpesEnum = async () => {\n      const res = await proxy.$http({\n        method: \"get\",\n        url: \"/GroupBuy/getGroupBuyClassLists\"\n      });\n      if (res.code == 0) {\n        typesEnum.value = res.data.data;\n      }\n    };\n    const goodsList = ref([]);\n    const getGoodsList = async () => {\n      const res = await proxy.$http({\n        method: \"get\",\n        url: \"/Goods/getGoodsLists\",\n        params: {\n          page: 1,\n          limit: 999\n        }\n      });\n      if (res.code == 0) {\n        goodsList.value = res.data.data;\n      }\n    };\n    const successUpload = res => {\n      form.value.img = res.data.url;\n    };\n    const handleErr = err => {\n      if (err.status == 320) {\n        form.value.img = JSON.parse(err.message).data.url;\n      }\n    };\n\n    // 编辑器实例，必须用 shallowRef\n    const editorRef = shallowRef();\n\n    // 内容 HTML\n    const valueHtml = ref(\"<p>hello</p>\");\n    const mode = ref(\"default\");\n    const toolbarConfig = {};\n    const editorConfig = {\n      placeholder: \"请输入内容...\",\n      MENU_CONF: {\n        uploadImage: {\n          fieldName: \"file\",\n          maxFileSize: 10 * 1024 * 1024,\n          // 10M\n          server: proxy.BASE_API_URL + \"index/uploadX\",\n          headers: {\n            \"Accept-Token\": getTokenAUTH()\n          },\n          customInsert(res, insertFn) {\n            const url = proxy.IMG_BASE_URL + res.data.url;\n            const alt = res.data.alt;\n            const href = res.data.href;\n            insertFn(url, alt, href);\n          }\n        }\n      }\n    };\n\n    // 组件销毁时，也及时销毁编辑器\n    onBeforeUnmount(() => {\n      const editor = editorRef.value;\n      if (editor == null) return;\n      editor.destroy();\n    });\n    const handleCreated = editor => {\n      editorRef.value = editor; // 记录 editor 实例，重要！\n    };\n    __expose({\n      form\n    });\n    const __returned__ = {\n      form,\n      props,\n      headers,\n      proxy,\n      typesEnum,\n      getTYpesEnum,\n      goodsList,\n      getGoodsList,\n      successUpload,\n      handleErr,\n      editorRef,\n      valueHtml,\n      mode,\n      toolbarConfig,\n      editorConfig,\n      handleCreated,\n      get rolesEnums() {\n        return rolesEnums;\n      },\n      get booleanEnums() {\n        return booleanEnums;\n      },\n      get showStatusEnums() {\n        return showStatusEnums;\n      },\n      get htmlDecodeByRegExp() {\n        return htmlDecodeByRegExp;\n      },\n      onBeforeUnmount,\n      nextTick,\n      ref,\n      shallowRef,\n      onMounted,\n      getCurrentInstance,\n      get getTokenAUTH() {\n        return getTokenAUTH;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["rolesEnums", "booleanEnums", "showStatusEnums", "htmlDecodeByRegExp", "onBeforeUnmount", "nextTick", "ref", "shallowRef", "onMounted", "getCurrentInstance", "getTokenAUTH", "form", "title", "class_id", "is_recommend", "sort", "price", "people", "people_add", "fail_money", "limit", "level", "item_money", "status", "next_time", "cycle", "desc", "good_id", "props", "__props", "headers", "value", "Object", "assign", "item", "getTYpesEnum", "getGoodsList", "proxy", "typesEnum", "res", "$http", "method", "url", "code", "data", "goodsList", "params", "page", "successUpload", "img", "handleErr", "err", "JSON", "parse", "message", "editor<PERSON><PERSON>", "valueHtml", "mode", "toolbarConfig", "editorConfig", "placeholder", "MENU_CONF", "uploadImage", "fieldName", "maxFileSize", "server", "BASE_API_URL", "customInsert", "insertFn", "IMG_BASE_URL", "alt", "href", "editor", "destroy", "handleCreated", "__expose"], "sources": ["D:/WorkSpace/facai7/facai7_admin/src/views/pintuanManage/components/pintuanList/editPop.vue"], "sourcesContent": ["<template>\r\n  <el-form\r\n    label-width=\"160px\"\r\n    :inline=\"true\"\r\n    :model=\"form\"\r\n    class=\"demo-form-inline\"\r\n  >\r\n    <el-form-item label=\"标题\" required>\r\n      <el-input v-model=\"form.title\" clearable />\r\n    </el-form-item>\r\n\r\n    <el-form-item label=\"拼团分类\" prop=\"class_id\">\r\n      <el-select v-model=\"form.class_id\" placeholder=\"拼团分类\" clearable>\r\n        <el-option\r\n          v-for=\"item in typesEnum\"\r\n          :label=\"item.title\"\r\n          :key=\"item.title\"\r\n          :value=\"item.id\"\r\n        />\r\n      </el-select>\r\n    </el-form-item>\r\n    <el-form-item label=\"商品\" prop=\"good_id\">\r\n      <el-select v-model=\"form.good_id\" placeholder=\"商品\" clearable>\r\n        <el-option\r\n          v-for=\"item in goodsList\"\r\n          :label=\"item.title\"\r\n          :key=\"item.title\"\r\n          :value=\"item.id\"\r\n        />\r\n      </el-select>\r\n    </el-form-item>\r\n    <el-form-item label=\"是否推荐\" required>\r\n      <el-select v-model=\"form.is_recommend\" placeholder=\"是否推荐\" clearable>\r\n        <el-option\r\n          v-for=\"item in booleanEnums\"\r\n          :label=\"item.label\"\r\n          :key=\"item.label\"\r\n          :value=\"item.value\"\r\n        />\r\n      </el-select>\r\n    </el-form-item>\r\n\r\n    <el-form-item label=\"排序\" required>\r\n      <el-input type=\"number\" v-model=\"form.sort\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"拼团价格\" required>\r\n      <el-input v-model=\"form.price\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"拼团人数\" required>\r\n      <el-input v-model=\"form.people\" clearable />\r\n    </el-form-item>\r\n    <!-- <el-form-item label=\"进度时间\" required>\r\n      <el-date-picker\r\n        v-model=\"form.process_time\"\r\n        type=\"datetime\"\r\n        value-format=\"YYYY-MM-DD HH:mm:ss\"\r\n        placeholder=\"进度时间\"\r\n        style=\"width: 100%\"\r\n      />\r\n    </el-form-item> -->\r\n    <el-form-item label=\"每小时增加人数\" required>\r\n      <el-input v-model=\"form.people_add\" clearable />\r\n    </el-form-item>\r\n    <!-- <el-form-item label=\"进度更新时间\" required>\r\n      <el-date-picker\r\n        v-model=\"form.process_update_time\"\r\n        type=\"datetime\"\r\n        value-format=\"YYYY-MM-DD HH:mm:ss\"\r\n        placeholder=\"进度更新时间\"\r\n        style=\"width: 100%\"\r\n      />\r\n    </el-form-item> -->\r\n    <el-form-item label=\"拼团失败返现\" required>\r\n      <el-input v-model=\"form.fail_money\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"限购次数\" required>\r\n      <el-input v-model=\"form.limit\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"会员等级\" required>\r\n      <el-input v-model=\"form.level\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"需要投资金额\" required>\r\n      <el-input v-model=\"form.item_money\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"显示状态\" required>\r\n      <el-select v-model=\"form.status\" placeholder=\"显示状态\" clearable>\r\n        <el-option\r\n          v-for=\"item in showStatusEnums\"\r\n          :label=\"item.label\"\r\n          :key=\"item.label\"\r\n          :value=\"item.value\"\r\n        />\r\n      </el-select>\r\n    </el-form-item>\r\n    <el-form-item label=\"开奖时间\" required>\r\n      <el-date-picker\r\n        v-model=\"form.next_time\"\r\n        type=\"datetime\"\r\n        value-format=\"YYYY-MM-DD HH:mm\"\r\n        placeholder=\"开奖时间\"\r\n        style=\"width: 100%\"\r\n      />\r\n    </el-form-item>\r\n    <el-form-item label=\"开奖周期（天）\" required>\r\n      <el-input v-model=\"form.cycle\" clearable />\r\n    </el-form-item>\r\n    <el-form-item label=\"福利说明\" required>\r\n      <el-input v-model=\"form.desc\" clearable />\r\n    </el-form-item>\r\n   \r\n  </el-form>\r\n</template>\r\n\r\n<script setup>\r\nimport { rolesEnums, booleanEnums, showStatusEnums } from \"@/config/enums\";\r\nimport { htmlDecodeByRegExp } from \"@/utils/utils\";\r\nimport {\r\n  onBeforeUnmount,\r\n  nextTick,\r\n  ref,\r\n  shallowRef,\r\n  onMounted,\r\n  getCurrentInstance,\r\n} from \"vue\";\r\nimport { getTokenAUTH } from \"@/utils/auth\";\r\n\r\nconst form = ref({\r\n  title: \"\",\r\n  class_id: \"\",\r\n  is_recommend: \"\",\r\n  sort: \"\",\r\n  price: \"\",\r\n  people: \"\",\r\n  // process_time: \"\",\r\n  people_add: \"\",\r\n  // process_update_time: \"\",\r\n  fail_money: \"\",\r\n  limit: \"\",\r\n  level: \"\",\r\n  item_money: \"\",\r\n  status: \"\",\r\n  next_time: \"\",\r\n  cycle: \"\",\r\n  desc: \"\",\r\n  good_id: \"\",\r\n});\r\nconst props = defineProps([\"item\"]);\r\n\r\nconst headers = ref({});\r\nonMounted(() => {\r\n  headers.value[\"Accept-Token\"] = getTokenAUTH();\r\n  nextTick(() => {\r\n    form.value = Object.assign(form.value, props.item);\r\n  });\r\n  getTYpesEnum();\r\n  getGoodsList()\r\n});\r\n\r\nconst { proxy } = getCurrentInstance();\r\n\r\nconst typesEnum = ref([]);\r\n\r\nconst getTYpesEnum = async () => {\r\n  const res = await proxy.$http({\r\n    method: \"get\",\r\n    url: \"/GroupBuy/getGroupBuyClassLists\",\r\n  });\r\n  \r\n  if (res.code == 0) {\r\n    typesEnum.value = res.data.data;\r\n  }\r\n};\r\n\r\nconst goodsList = ref([]);\r\n\r\nconst getGoodsList = async () => {\r\n  const res = await proxy.$http({\r\n    method: \"get\",\r\n    url: \"/Goods/getGoodsLists\",\r\n    params: {\r\n      page: 1,\r\n      limit: 999,\r\n    },\r\n  });\r\n  \r\n  if (res.code == 0) {\r\n    goodsList.value = res.data.data;\r\n  }\r\n};\r\n\r\nconst successUpload = (res) => {\r\n  form.value.img = res.data.url;\r\n};\r\n\r\nconst handleErr = (err) => {\r\n  if (err.status == 320) {\r\n    form.value.img = JSON.parse(err.message).data.url;\r\n  }\r\n};\r\n\r\n// 编辑器实例，必须用 shallowRef\r\nconst editorRef = shallowRef();\r\n\r\n// 内容 HTML\r\nconst valueHtml = ref(\"<p>hello</p>\");\r\nconst mode = ref(\"default\");\r\n\r\nconst toolbarConfig = {};\r\nconst editorConfig = {\r\n  placeholder: \"请输入内容...\",\r\n  MENU_CONF: {\r\n    uploadImage: {\r\n      fieldName: \"file\",\r\n      maxFileSize: 10 * 1024 * 1024, // 10M\r\n      server: proxy.BASE_API_URL + \"index/uploadX\",\r\n      headers: {\r\n        \"Accept-Token\": getTokenAUTH(),\r\n      },\r\n      customInsert(res, insertFn) {\r\n        const url = proxy.IMG_BASE_URL + res.data.url;\r\n        const alt = res.data.alt;\r\n        const href = res.data.href;\r\n        insertFn(url, alt, href);\r\n      },\r\n    },\r\n  },\r\n};\r\n\r\n// 组件销毁时，也及时销毁编辑器\r\nonBeforeUnmount(() => {\r\n  const editor = editorRef.value;\r\n  if (editor == null) return;\r\n  editor.destroy();\r\n});\r\n\r\nconst handleCreated = (editor) => {\r\n  editorRef.value = editor; // 记录 editor 实例，重要！\r\n};\r\n\r\ndefineExpose({ form });\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.demo-form-inline {\r\n  justify-content: flex-start;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.demo-form-inline .el-input {\r\n  --el-input-width: 260px;\r\n}\r\n\r\n.demo-form-inline .el-select {\r\n  --el-select-width: 260px;\r\n}\r\n\r\n/deep/ .el-radio-group {\r\n  width: 260px;\r\n}\r\n\r\n.form-title {\r\n  text-align: left;\r\n  padding-left: 30px;\r\n  margin: 20px auto 10px;\r\n  height: 44px;\r\n  background-color: #f2f2f2;\r\n  border-radius: 5px;\r\n  line-height: 44px;\r\n}\r\n\r\n/deep/ .el-form-item {\r\n  align-items: flex-start;\r\n}\r\n</style>\r\n"], "mappings": "AAkHA,SAASA,UAAU,EAAEC,YAAY,EAAEC,eAAe,QAAQ,gBAAgB;AAC1E,SAASC,kBAAkB,QAAQ,eAAe;AAClD,SACEC,eAAe,EACfC,QAAQ,EACRC,GAAG,EACHC,UAAU,EACVC,SAAS,EACTC,kBAAkB,QACb,KAAK;AACZ,SAASC,YAAY,QAAQ,cAAc;;;;;;;IAE3C,MAAMC,IAAI,GAAGL,GAAG,CAAC;MACfM,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,YAAY,EAAE,EAAE;MAChBC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,EAAE;MACV;MACAC,UAAU,EAAE,EAAE;MACd;MACAC,UAAU,EAAE,EAAE;MACdC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,UAAU,EAAE,EAAE;MACdC,MAAM,EAAE,EAAE;MACVC,SAAS,EAAE,EAAE;MACbC,KAAK,EAAE,EAAE;MACTC,IAAI,EAAE,EAAE;MACRC,OAAO,EAAE;IACX,CAAC,CAAC;IACF,MAAMC,KAAK,GAAGC,OAAqB;IAEnC,MAAMC,OAAO,GAAGxB,GAAG,CAAC,CAAC,CAAC,CAAC;IACvBE,SAAS,CAAC,MAAM;MACdsB,OAAO,CAACC,KAAK,CAAC,cAAc,CAAC,GAAGrB,YAAY,CAAC,CAAC;MAC9CL,QAAQ,CAAC,MAAM;QACbM,IAAI,CAACoB,KAAK,GAAGC,MAAM,CAACC,MAAM,CAACtB,IAAI,CAACoB,KAAK,EAAEH,KAAK,CAACM,IAAI,CAAC;MACpD,CAAC,CAAC;MACFC,YAAY,CAAC,CAAC;MACdC,YAAY,CAAC,CAAC;IAChB,CAAC,CAAC;IAEF,MAAM;MAAEC;IAAM,CAAC,GAAG5B,kBAAkB,CAAC,CAAC;IAEtC,MAAM6B,SAAS,GAAGhC,GAAG,CAAC,EAAE,CAAC;IAEzB,MAAM6B,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,MAAMI,GAAG,GAAG,MAAMF,KAAK,CAACG,KAAK,CAAC;QAC5BC,MAAM,EAAE,KAAK;QACbC,GAAG,EAAE;MACP,CAAC,CAAC;MAEF,IAAIH,GAAG,CAACI,IAAI,IAAI,CAAC,EAAE;QACjBL,SAAS,CAACP,KAAK,GAAGQ,GAAG,CAACK,IAAI,CAACA,IAAI;MACjC;IACF,CAAC;IAED,MAAMC,SAAS,GAAGvC,GAAG,CAAC,EAAE,CAAC;IAEzB,MAAM8B,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,MAAMG,GAAG,GAAG,MAAMF,KAAK,CAACG,KAAK,CAAC;QAC5BC,MAAM,EAAE,KAAK;QACbC,GAAG,EAAE,sBAAsB;QAC3BI,MAAM,EAAE;UACNC,IAAI,EAAE,CAAC;UACP3B,KAAK,EAAE;QACT;MACF,CAAC,CAAC;MAEF,IAAImB,GAAG,CAACI,IAAI,IAAI,CAAC,EAAE;QACjBE,SAAS,CAACd,KAAK,GAAGQ,GAAG,CAACK,IAAI,CAACA,IAAI;MACjC;IACF,CAAC;IAED,MAAMI,aAAa,GAAIT,GAAG,IAAK;MAC7B5B,IAAI,CAACoB,KAAK,CAACkB,GAAG,GAAGV,GAAG,CAACK,IAAI,CAACF,GAAG;IAC/B,CAAC;IAED,MAAMQ,SAAS,GAAIC,GAAG,IAAK;MACzB,IAAIA,GAAG,CAAC5B,MAAM,IAAI,GAAG,EAAE;QACrBZ,IAAI,CAACoB,KAAK,CAACkB,GAAG,GAAGG,IAAI,CAACC,KAAK,CAACF,GAAG,CAACG,OAAO,CAAC,CAACV,IAAI,CAACF,GAAG;MACnD;IACF,CAAC;;IAED;IACA,MAAMa,SAAS,GAAGhD,UAAU,CAAC,CAAC;;IAE9B;IACA,MAAMiD,SAAS,GAAGlD,GAAG,CAAC,cAAc,CAAC;IACrC,MAAMmD,IAAI,GAAGnD,GAAG,CAAC,SAAS,CAAC;IAE3B,MAAMoD,aAAa,GAAG,CAAC,CAAC;IACxB,MAAMC,YAAY,GAAG;MACnBC,WAAW,EAAE,UAAU;MACvBC,SAAS,EAAE;QACTC,WAAW,EAAE;UACXC,SAAS,EAAE,MAAM;UACjBC,WAAW,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;UAAE;UAC/BC,MAAM,EAAE5B,KAAK,CAAC6B,YAAY,GAAG,eAAe;UAC5CpC,OAAO,EAAE;YACP,cAAc,EAAEpB,YAAY,CAAC;UAC/B,CAAC;UACDyD,YAAYA,CAAC5B,GAAG,EAAE6B,QAAQ,EAAE;YAC1B,MAAM1B,GAAG,GAAGL,KAAK,CAACgC,YAAY,GAAG9B,GAAG,CAACK,IAAI,CAACF,GAAG;YAC7C,MAAM4B,GAAG,GAAG/B,GAAG,CAACK,IAAI,CAAC0B,GAAG;YACxB,MAAMC,IAAI,GAAGhC,GAAG,CAACK,IAAI,CAAC2B,IAAI;YAC1BH,QAAQ,CAAC1B,GAAG,EAAE4B,GAAG,EAAEC,IAAI,CAAC;UAC1B;QACF;MACF;IACF,CAAC;;IAED;IACAnE,eAAe,CAAC,MAAM;MACpB,MAAMoE,MAAM,GAAGjB,SAAS,CAACxB,KAAK;MAC9B,IAAIyC,MAAM,IAAI,IAAI,EAAE;MACpBA,MAAM,CAACC,OAAO,CAAC,CAAC;IAClB,CAAC,CAAC;IAEF,MAAMC,aAAa,GAAIF,MAAM,IAAK;MAChCjB,SAAS,CAACxB,KAAK,GAAGyC,MAAM,CAAC,CAAC;IAC5B,CAAC;IAEDG,QAAY,CAAC;MAAEhE;IAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}