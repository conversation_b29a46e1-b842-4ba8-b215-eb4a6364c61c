{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport { getCurrentInstance, ref, unref } from 'vue';\nimport { isNull } from 'lodash-unified';\nimport { getRowIdentity } from '../util.mjs';\nfunction useCurrent(watcherData) {\n  const instance = getCurrentInstance();\n  const _currentRowKey = ref(null);\n  const currentRow = ref(null);\n  const setCurrentRowKey = key => {\n    instance.store.assertRowKey();\n    _currentRowKey.value = key;\n    setCurrentRowByKey(key);\n  };\n  const restoreCurrentRowKey = () => {\n    _currentRowKey.value = null;\n  };\n  const setCurrentRowByKey = key => {\n    var _a;\n    const {\n      data,\n      rowKey\n    } = watcherData;\n    let _currentRow = null;\n    if (rowKey.value) {\n      _currentRow = (_a = (unref(data) || []).find(item => getRowIdentity(item, rowKey.value) === key)) != null ? _a : null;\n    }\n    currentRow.value = _currentRow != null ? _currentRow : null;\n    instance.emit(\"current-change\", currentRow.value, null);\n  };\n  const updateCurrentRow = _currentRow => {\n    const oldCurrentRow = currentRow.value;\n    if (_currentRow && _currentRow !== oldCurrentRow) {\n      currentRow.value = _currentRow;\n      instance.emit(\"current-change\", currentRow.value, oldCurrentRow);\n      return;\n    }\n    if (!_currentRow && oldCurrentRow) {\n      currentRow.value = null;\n      instance.emit(\"current-change\", null, oldCurrentRow);\n    }\n  };\n  const updateCurrentRowData = () => {\n    const rowKey = watcherData.rowKey.value;\n    const data = watcherData.data.value || [];\n    const oldCurrentRow = currentRow.value;\n    if (oldCurrentRow && !data.includes(oldCurrentRow)) {\n      if (rowKey) {\n        const currentRowKey = getRowIdentity(oldCurrentRow, rowKey);\n        setCurrentRowByKey(currentRowKey);\n      } else {\n        currentRow.value = null;\n      }\n      if (isNull(currentRow.value)) {\n        instance.emit(\"current-change\", null, oldCurrentRow);\n      }\n    } else if (_currentRowKey.value) {\n      setCurrentRowByKey(_currentRowKey.value);\n      restoreCurrentRowKey();\n    }\n  };\n  return {\n    setCurrentRowKey,\n    restoreCurrentRowKey,\n    setCurrentRowByKey,\n    updateCurrentRow,\n    updateCurrentRowData,\n    states: {\n      _currentRowKey,\n      currentRow\n    }\n  };\n}\nexport { useCurrent as default };", "map": {"version": 3, "names": ["useCurrent", "watcherData", "instance", "getCurrentInstance", "_currentRowKey", "ref", "currentRow", "setCurrentRowKey", "key", "store", "assertRowKey", "value", "setCurrentRowByKey", "restoreCurrentRowKey", "_a", "data", "<PERSON><PERSON><PERSON>", "_currentRow", "unref", "find", "item", "getRowIdentity", "emit", "updateCurrentRow", "oldCurrentRow", "updateCurrentRowData", "includes", "currentRowKey", "isNull", "states"], "sources": ["../../../../../../../packages/components/table/src/store/current.ts"], "sourcesContent": ["import { getCurrentInstance, ref, unref } from 'vue'\nimport { isNull } from 'lodash-unified'\nimport { getRowIdentity } from '../util'\n\nimport type { Ref } from 'vue'\nimport type { DefaultRow, Table } from '../table/defaults'\nimport type { WatcherPropsData } from '.'\n\nfunction useCurrent<T extends DefaultRow>(watcherData: WatcherPropsData<T>) {\n  const instance = getCurrentInstance() as Table<T>\n  const _currentRowKey: Ref<string | null> = ref(null)\n  const currentRow: Ref<T | null> = ref(null)\n\n  const setCurrentRowKey = (key: string) => {\n    instance.store.assertRowKey()\n    _currentRowKey.value = key\n    setCurrentRowByKey(key)\n  }\n\n  const restoreCurrentRowKey = () => {\n    _currentRowKey.value = null\n  }\n\n  const setCurrentRowByKey = (key: string) => {\n    const { data, rowKey } = watcherData\n    let _currentRow: T | null = null\n    if (rowKey.value) {\n      _currentRow =\n        (unref(data) || []).find(\n          (item) => getRowIdentity(item, rowKey.value) === key\n        ) ?? null\n    }\n    currentRow.value = _currentRow ?? null\n    instance.emit('current-change', currentRow.value, null)\n  }\n\n  const updateCurrentRow = (_currentRow: T) => {\n    const oldCurrentRow = currentRow.value\n    if (_currentRow && _currentRow !== oldCurrentRow) {\n      currentRow.value = _currentRow\n      instance.emit('current-change', currentRow.value, oldCurrentRow)\n      return\n    }\n    if (!_currentRow && oldCurrentRow) {\n      currentRow.value = null\n      instance.emit('current-change', null, oldCurrentRow)\n    }\n  }\n\n  const updateCurrentRowData = () => {\n    const rowKey = watcherData.rowKey.value\n    // data 为 null 时，解构时的默认值会被忽略\n    const data = watcherData.data.value || []\n    const oldCurrentRow = currentRow.value\n    // 当 currentRow 不在 data 中时尝试更新数据\n    if (oldCurrentRow && !data.includes(oldCurrentRow)) {\n      if (rowKey) {\n        const currentRowKey = getRowIdentity(oldCurrentRow, rowKey)\n        setCurrentRowByKey(currentRowKey)\n      } else {\n        currentRow.value = null\n      }\n      if (isNull(currentRow.value)) {\n        instance.emit('current-change', null, oldCurrentRow)\n      }\n    } else if (_currentRowKey.value) {\n      // 把初始时下设置的 rowKey 转化成 rowData\n      setCurrentRowByKey(_currentRowKey.value)\n      restoreCurrentRowKey()\n    }\n  }\n\n  return {\n    setCurrentRowKey,\n    restoreCurrentRowKey,\n    setCurrentRowByKey,\n    updateCurrentRow,\n    updateCurrentRowData,\n    states: {\n      _currentRowKey,\n      currentRow,\n    },\n  }\n}\n\nexport default useCurrent\n"], "mappings": ";;;;;AAGA,SAASA,UAAUA,CAACC,WAAW,EAAE;EAC/B,MAAMC,QAAQ,GAAGC,kBAAkB,EAAE;EACrC,MAAMC,cAAc,GAAGC,GAAG,CAAC,IAAI,CAAC;EAChC,MAAMC,UAAU,GAAGD,GAAG,CAAC,IAAI,CAAC;EAC5B,MAAME,gBAAgB,GAAIC,GAAG,IAAK;IAChCN,QAAQ,CAACO,KAAK,CAACC,YAAY,EAAE;IAC7BN,cAAc,CAACO,KAAK,GAAGH,GAAG;IAC1BI,kBAAkB,CAACJ,GAAG,CAAC;EAC3B,CAAG;EACD,MAAMK,oBAAoB,GAAGA,CAAA,KAAM;IACjCT,cAAc,CAACO,KAAK,GAAG,IAAI;EAC/B,CAAG;EACD,MAAMC,kBAAkB,GAAIJ,GAAG,IAAK;IAClC,IAAIM,EAAE;IACN,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAE,GAAGf,WAAW;IACpC,IAAIgB,WAAW,GAAG,IAAI;IACtB,IAAID,MAAM,CAACL,KAAK,EAAE;MAChBM,WAAW,GAAG,CAACH,EAAE,GAAG,CAACI,KAAK,CAACH,IAAI,CAAC,IAAI,EAAE,EAAEI,IAAI,CAAEC,IAAI,IAAKC,cAAc,CAACD,IAAI,EAAEJ,MAAM,CAACL,KAAK,CAAC,KAAKH,GAAG,CAAC,KAAK,IAAI,GAAGM,EAAE,GAAG,IAAI;IAC7H;IACIR,UAAU,CAACK,KAAK,GAAGM,WAAW,IAAI,IAAI,GAAGA,WAAW,GAAG,IAAI;IAC3Df,QAAQ,CAACoB,IAAI,CAAC,gBAAgB,EAAEhB,UAAU,CAACK,KAAK,EAAE,IAAI,CAAC;EAC3D,CAAG;EACD,MAAMY,gBAAgB,GAAIN,WAAW,IAAK;IACxC,MAAMO,aAAa,GAAGlB,UAAU,CAACK,KAAK;IACtC,IAAIM,WAAW,IAAIA,WAAW,KAAKO,aAAa,EAAE;MAChDlB,UAAU,CAACK,KAAK,GAAGM,WAAW;MAC9Bf,QAAQ,CAACoB,IAAI,CAAC,gBAAgB,EAAEhB,UAAU,CAACK,KAAK,EAAEa,aAAa,CAAC;MAChE;IACN;IACI,IAAI,CAACP,WAAW,IAAIO,aAAa,EAAE;MACjClB,UAAU,CAACK,KAAK,GAAG,IAAI;MACvBT,QAAQ,CAACoB,IAAI,CAAC,gBAAgB,EAAE,IAAI,EAAEE,aAAa,CAAC;IAC1D;EACA,CAAG;EACD,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMT,MAAM,GAAGf,WAAW,CAACe,MAAM,CAACL,KAAK;IACvC,MAAMI,IAAI,GAAGd,WAAW,CAACc,IAAI,CAACJ,KAAK,IAAI,EAAE;IACzC,MAAMa,aAAa,GAAGlB,UAAU,CAACK,KAAK;IACtC,IAAIa,aAAa,IAAI,CAACT,IAAI,CAACW,QAAQ,CAACF,aAAa,CAAC,EAAE;MAClD,IAAIR,MAAM,EAAE;QACV,MAAMW,aAAa,GAAGN,cAAc,CAACG,aAAa,EAAER,MAAM,CAAC;QAC3DJ,kBAAkB,CAACe,aAAa,CAAC;MACzC,CAAO,MAAM;QACLrB,UAAU,CAACK,KAAK,GAAG,IAAI;MAC/B;MACM,IAAIiB,MAAM,CAACtB,UAAU,CAACK,KAAK,CAAC,EAAE;QAC5BT,QAAQ,CAACoB,IAAI,CAAC,gBAAgB,EAAE,IAAI,EAAEE,aAAa,CAAC;MAC5D;IACA,CAAK,MAAM,IAAIpB,cAAc,CAACO,KAAK,EAAE;MAC/BC,kBAAkB,CAACR,cAAc,CAACO,KAAK,CAAC;MACxCE,oBAAoB,EAAE;IAC5B;EACA,CAAG;EACD,OAAO;IACLN,gBAAgB;IAChBM,oBAAoB;IACpBD,kBAAkB;IAClBW,gBAAgB;IAChBE,oBAAoB;IACpBI,MAAM,EAAE;MACNzB,cAAc;MACdE;IACN;EACA,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}