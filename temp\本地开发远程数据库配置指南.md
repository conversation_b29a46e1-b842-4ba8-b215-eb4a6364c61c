# 本地开发远程数据库配置指南

## 🔐 1. SFTP密码配置解决方案

### 已配置内容：
```json
{
    "username": "facai7",
    "password": "your_ftp_password_here",  // ← 请替换为实际密码
    "remotePath": "/"
}
```

### ⚠️ 安全注意事项：

1. **替换密码**：将 `"your_ftp_password_here"` 替换为实际FTP密码
2. **Git安全**：确保 `.vscode/sftp.json` 在 `.gitignore` 中
3. **权限控制**：只在本地开发机器上保存密码

### 🔒 更安全的替代方案（推荐）：

如果服务器支持SFTP（SSH），建议使用密钥认证：
```json
{
    "protocol": "sftp",
    "privateKeyPath": "C:/Users/<USER>/.ssh/id_rsa"
}
```

## 🗄️ 2. 本地连接远程数据库配置

### 已完成的配置：

#### 数据库连接：
```ini
[DATABASE]
TYPE     = mysql
HOSTNAME = **************  # 远程服务器IP
DATABASE = facai7          # 开发环境数据库
USERNAME = facai7
PASSWORD = 5aRTTSwAeyiRWxFt
```

#### Redis缓存：
```ini
[CACHE]
HOST     = **************  # 远程Redis服务器
PORT     = 6379
PASSWORD = zWISJZOLjD
```

#### 消息队列：
```ini
[QUEUE]
HOST     = **************  # 远程Redis服务器
PORT     = 6379
PASSWORD = zWISJZOLjD
```

## 🧪 测试连接

### 1. 测试数据库连接
```bash
cd facai7_api

# 测试数据库连接
php think migrate:status

# 如果连接成功，会显示迁移状态
# 如果失败，会显示连接错误信息
```

### 2. 测试Redis连接
```bash
# 测试缓存连接
php think cache:clear

# 如果成功，会清除缓存
# 如果失败，会显示Redis连接错误
```

### 3. 测试API接口
```bash
# 启动本地Apache服务
# 访问：http://facai7-api.local/

# 或使用PHP内置服务器
php think run -p 8099
# 访问：http://localhost:8099/
```

## 🔧 网络配置要求

### 1. 防火墙设置
确保远程服务器开放以下端口：
- **MySQL**: 3306
- **Redis**: 6379
- **FTP**: 21

### 2. MySQL远程访问权限
在远程服务器MySQL中执行：
```sql
-- 允许facai7用户从任何IP连接
GRANT ALL PRIVILEGES ON facai7.* TO 'facai7'@'%' IDENTIFIED BY '5aRTTSwAeyiRWxFt';
FLUSH PRIVILEGES;

-- 检查用户权限
SELECT user, host FROM mysql.user WHERE user='facai7';
```

### 3. Redis远程访问配置
在远程服务器Redis配置中：
```conf
# redis.conf
bind 0.0.0.0          # 允许外部连接
requirepass zWISJZOLjD # 设置密码
```

## 🚀 开发工作流

### 1. 本地开发流程
```bash
# 1. 启动本地Web服务
cd facai7_api
php think run -p 8099

# 2. 修改代码
# 编辑PHP文件...

# 3. 本地测试
# 访问 http://localhost:8099/api/test

# 4. 同步到远程服务器
# VSCode: Ctrl+Shift+P → SFTP: Upload
```

### 2. 数据同步注意事项
- ✅ **读取数据**：本地直接读取远程数据库
- ⚠️ **写入数据**：谨慎操作，避免影响其他开发者
- 🔄 **缓存处理**：本地修改后清除远程缓存

## 📊 配置验证清单

### ✅ 检查项目：
- [ ] SFTP密码已配置
- [ ] 数据库连接远程服务器
- [ ] Redis连接远程服务器
- [ ] 本地API服务可以启动
- [ ] 数据库连接测试通过
- [ ] Redis连接测试通过
- [ ] 代码同步功能正常

### 🔍 故障排除：

#### 数据库连接失败：
```bash
# 检查网络连通性
telnet ************** 3306

# 检查MySQL用户权限
mysql -h ************** -u facai7 -p
```

#### Redis连接失败：
```bash
# 检查Redis连通性
telnet ************** 6379

# 使用redis-cli测试
redis-cli -h ************** -p 6379 -a zWISJZOLjD ping
```

#### SFTP连接失败：
- 检查FTP用户名密码
- 确认服务器FTP服务运行
- 检查防火墙设置

## 🎯 最佳实践

### 1. 环境隔离
- **本地开发**：使用 `.env.local`
- **远程测试**：使用 `.env.dev`
- **生产环境**：使用 `.env.prod`

### 2. 数据安全
- 定期备份远程数据库
- 避免在开发环境执行危险操作
- 使用事务处理重要数据修改

### 3. 性能优化
- 本地启用调试模式
- 远程关闭不必要的日志
- 合理使用缓存机制

现在您可以：
1. ✅ 无密码登录SFTP（配置密码后）
2. ✅ 本地开发使用远程数据库
3. ✅ 实时测试和调试功能
4. ✅ 高效的代码同步工作流
