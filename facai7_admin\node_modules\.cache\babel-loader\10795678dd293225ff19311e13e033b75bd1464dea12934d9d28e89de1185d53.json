{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Luxembourgish [lb]\n//! author : mweimerskirch : https://github.com/mweimerskirch\n//! author : <PERSON> : https://github.com/kwisatz\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  function processRelativeTime(number, withoutSuffix, key, isFuture) {\n    var format = {\n      m: ['eng Minutt', 'enger <PERSON>utt'],\n      h: ['eng Stonn', 'enger Stonn'],\n      d: ['een Dag', 'engem Dag'],\n      M: ['ee Mount', 'engem Mount'],\n      y: ['ee Joer', 'engem Joer']\n    };\n    return withoutSuffix ? format[key][0] : format[key][1];\n  }\n  function processFutureTime(string) {\n    var number = string.substr(0, string.indexOf(' '));\n    if (eifelerRegelAppliesToNumber(number)) {\n      return 'a ' + string;\n    }\n    return 'an ' + string;\n  }\n  function processPastTime(string) {\n    var number = string.substr(0, string.indexOf(' '));\n    if (eifelerRegelAppliesToNumber(number)) {\n      return 'viru ' + string;\n    }\n    return 'virun ' + string;\n  }\n  /**\n   * Returns true if the word before the given number loses the '-n' ending.\n   * e.g. 'an 10 Deeg' but 'a 5 Deeg'\n   *\n   * @param number {integer}\n   * @returns {boolean}\n   */\n  function eifelerRegelAppliesToNumber(number) {\n    number = parseInt(number, 10);\n    if (isNaN(number)) {\n      return false;\n    }\n    if (number < 0) {\n      // Negative Number --> always true\n      return true;\n    } else if (number < 10) {\n      // Only 1 digit\n      if (4 <= number && number <= 7) {\n        return true;\n      }\n      return false;\n    } else if (number < 100) {\n      // 2 digits\n      var lastDigit = number % 10,\n        firstDigit = number / 10;\n      if (lastDigit === 0) {\n        return eifelerRegelAppliesToNumber(firstDigit);\n      }\n      return eifelerRegelAppliesToNumber(lastDigit);\n    } else if (number < 10000) {\n      // 3 or 4 digits --> recursively check first digit\n      while (number >= 10) {\n        number = number / 10;\n      }\n      return eifelerRegelAppliesToNumber(number);\n    } else {\n      // Anything larger than 4 digits: recursively check first n-3 digits\n      number = number / 1000;\n      return eifelerRegelAppliesToNumber(number);\n    }\n  }\n  var lb = moment.defineLocale('lb', {\n    months: 'Januar_Februar_Mäerz_Abrëll_Mee_Juni_Juli_August_September_Oktober_November_Dezember'.split('_'),\n    monthsShort: 'Jan._Febr._Mrz._Abr._Mee_Jun._Jul._Aug._Sept._Okt._Nov._Dez.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'Sonndeg_Méindeg_Dënschdeg_Mëttwoch_Donneschdeg_Freideg_Samschdeg'.split('_'),\n    weekdaysShort: 'So._Mé._Dë._Më._Do._Fr._Sa.'.split('_'),\n    weekdaysMin: 'So_Mé_Dë_Më_Do_Fr_Sa'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'H:mm [Auer]',\n      LTS: 'H:mm:ss [Auer]',\n      L: 'DD.MM.YYYY',\n      LL: 'D. MMMM YYYY',\n      LLL: 'D. MMMM YYYY H:mm [Auer]',\n      LLLL: 'dddd, D. MMMM YYYY H:mm [Auer]'\n    },\n    calendar: {\n      sameDay: '[Haut um] LT',\n      sameElse: 'L',\n      nextDay: '[Muer um] LT',\n      nextWeek: 'dddd [um] LT',\n      lastDay: '[Gëschter um] LT',\n      lastWeek: function () {\n        // Different date string for 'Dënschdeg' (Tuesday) and 'Donneschdeg' (Thursday) due to phonological rule\n        switch (this.day()) {\n          case 2:\n          case 4:\n            return '[Leschten] dddd [um] LT';\n          default:\n            return '[Leschte] dddd [um] LT';\n        }\n      }\n    },\n    relativeTime: {\n      future: processFutureTime,\n      past: processPastTime,\n      s: 'e puer Sekonnen',\n      ss: '%d Sekonnen',\n      m: processRelativeTime,\n      mm: '%d Minutten',\n      h: processRelativeTime,\n      hh: '%d Stonnen',\n      d: processRelativeTime,\n      dd: '%d Deeg',\n      M: processRelativeTime,\n      MM: '%d Méint',\n      y: processRelativeTime,\n      yy: '%d Joer'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return lb;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "processRelativeTime", "number", "withoutSuffix", "key", "isFuture", "format", "m", "h", "d", "M", "y", "processFutureTime", "string", "substr", "indexOf", "eifelerRegelAppliesToNumber", "processPastTime", "parseInt", "isNaN", "lastDigit", "firstDigit", "lb", "defineLocale", "months", "split", "monthsShort", "monthsParseExact", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "same<PERSON><PERSON><PERSON>", "nextDay", "nextWeek", "lastDay", "lastWeek", "day", "relativeTime", "future", "past", "s", "ss", "mm", "hh", "dd", "MM", "yy", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy"], "sources": ["D:/WorkSpace/facai7/facai7_admin/node_modules/moment/locale/lb.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Luxembourgish [lb]\n//! author : mweimerskirch : https://github.com/mweimerskirch\n//! author : <PERSON> : https://github.com/kwisatz\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    function processRelativeTime(number, withoutSuffix, key, isFuture) {\n        var format = {\n            m: ['eng Minutt', 'enger <PERSON>utt'],\n            h: ['eng Stonn', 'enger Stonn'],\n            d: ['een Dag', 'engem Dag'],\n            M: ['ee Mount', 'engem Mount'],\n            y: ['ee Joer', 'engem Joer'],\n        };\n        return withoutSuffix ? format[key][0] : format[key][1];\n    }\n    function processFutureTime(string) {\n        var number = string.substr(0, string.indexOf(' '));\n        if (eifelerRegelAppliesToNumber(number)) {\n            return 'a ' + string;\n        }\n        return 'an ' + string;\n    }\n    function processPastTime(string) {\n        var number = string.substr(0, string.indexOf(' '));\n        if (eifelerRegelAppliesToNumber(number)) {\n            return 'viru ' + string;\n        }\n        return 'virun ' + string;\n    }\n    /**\n     * Returns true if the word before the given number loses the '-n' ending.\n     * e.g. 'an 10 Deeg' but 'a 5 Deeg'\n     *\n     * @param number {integer}\n     * @returns {boolean}\n     */\n    function eifelerRegelAppliesToNumber(number) {\n        number = parseInt(number, 10);\n        if (isNaN(number)) {\n            return false;\n        }\n        if (number < 0) {\n            // Negative Number --> always true\n            return true;\n        } else if (number < 10) {\n            // Only 1 digit\n            if (4 <= number && number <= 7) {\n                return true;\n            }\n            return false;\n        } else if (number < 100) {\n            // 2 digits\n            var lastDigit = number % 10,\n                firstDigit = number / 10;\n            if (lastDigit === 0) {\n                return eifelerRegelAppliesToNumber(firstDigit);\n            }\n            return eifelerRegelAppliesToNumber(lastDigit);\n        } else if (number < 10000) {\n            // 3 or 4 digits --> recursively check first digit\n            while (number >= 10) {\n                number = number / 10;\n            }\n            return eifelerRegelAppliesToNumber(number);\n        } else {\n            // Anything larger than 4 digits: recursively check first n-3 digits\n            number = number / 1000;\n            return eifelerRegelAppliesToNumber(number);\n        }\n    }\n\n    var lb = moment.defineLocale('lb', {\n        months: 'Januar_Februar_Mäerz_Abrëll_Mee_Juni_Juli_August_September_Oktober_November_Dezember'.split(\n            '_'\n        ),\n        monthsShort:\n            'Jan._Febr._Mrz._Abr._Mee_Jun._Jul._Aug._Sept._Okt._Nov._Dez.'.split(\n                '_'\n            ),\n        monthsParseExact: true,\n        weekdays:\n            'Sonndeg_Méindeg_Dënschdeg_Mëttwoch_Donneschdeg_Freideg_Samschdeg'.split(\n                '_'\n            ),\n        weekdaysShort: 'So._Mé._Dë._Më._Do._Fr._Sa.'.split('_'),\n        weekdaysMin: 'So_Mé_Dë_Më_Do_Fr_Sa'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'H:mm [Auer]',\n            LTS: 'H:mm:ss [Auer]',\n            L: 'DD.MM.YYYY',\n            LL: 'D. MMMM YYYY',\n            LLL: 'D. MMMM YYYY H:mm [Auer]',\n            LLLL: 'dddd, D. MMMM YYYY H:mm [Auer]',\n        },\n        calendar: {\n            sameDay: '[Haut um] LT',\n            sameElse: 'L',\n            nextDay: '[Muer um] LT',\n            nextWeek: 'dddd [um] LT',\n            lastDay: '[Gëschter um] LT',\n            lastWeek: function () {\n                // Different date string for 'Dënschdeg' (Tuesday) and 'Donneschdeg' (Thursday) due to phonological rule\n                switch (this.day()) {\n                    case 2:\n                    case 4:\n                        return '[Leschten] dddd [um] LT';\n                    default:\n                        return '[Leschte] dddd [um] LT';\n                }\n            },\n        },\n        relativeTime: {\n            future: processFutureTime,\n            past: processPastTime,\n            s: 'e puer Sekonnen',\n            ss: '%d Sekonnen',\n            m: processRelativeTime,\n            mm: '%d Minutten',\n            h: processRelativeTime,\n            hh: '%d Stonnen',\n            d: processRelativeTime,\n            dd: '%d Deeg',\n            M: processRelativeTime,\n            MM: '%d Méint',\n            y: processRelativeTime,\n            yy: '%d Joer',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n        ordinal: '%d.',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return lb;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,SAASC,mBAAmBA,CAACC,MAAM,EAAEC,aAAa,EAAEC,GAAG,EAAEC,QAAQ,EAAE;IAC/D,IAAIC,MAAM,GAAG;MACTC,CAAC,EAAE,CAAC,YAAY,EAAE,cAAc,CAAC;MACjCC,CAAC,EAAE,CAAC,WAAW,EAAE,aAAa,CAAC;MAC/BC,CAAC,EAAE,CAAC,SAAS,EAAE,WAAW,CAAC;MAC3BC,CAAC,EAAE,CAAC,UAAU,EAAE,aAAa,CAAC;MAC9BC,CAAC,EAAE,CAAC,SAAS,EAAE,YAAY;IAC/B,CAAC;IACD,OAAOR,aAAa,GAAGG,MAAM,CAACF,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGE,MAAM,CAACF,GAAG,CAAC,CAAC,CAAC,CAAC;EAC1D;EACA,SAASQ,iBAAiBA,CAACC,MAAM,EAAE;IAC/B,IAAIX,MAAM,GAAGW,MAAM,CAACC,MAAM,CAAC,CAAC,EAAED,MAAM,CAACE,OAAO,CAAC,GAAG,CAAC,CAAC;IAClD,IAAIC,2BAA2B,CAACd,MAAM,CAAC,EAAE;MACrC,OAAO,IAAI,GAAGW,MAAM;IACxB;IACA,OAAO,KAAK,GAAGA,MAAM;EACzB;EACA,SAASI,eAAeA,CAACJ,MAAM,EAAE;IAC7B,IAAIX,MAAM,GAAGW,MAAM,CAACC,MAAM,CAAC,CAAC,EAAED,MAAM,CAACE,OAAO,CAAC,GAAG,CAAC,CAAC;IAClD,IAAIC,2BAA2B,CAACd,MAAM,CAAC,EAAE;MACrC,OAAO,OAAO,GAAGW,MAAM;IAC3B;IACA,OAAO,QAAQ,GAAGA,MAAM;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAASG,2BAA2BA,CAACd,MAAM,EAAE;IACzCA,MAAM,GAAGgB,QAAQ,CAAChB,MAAM,EAAE,EAAE,CAAC;IAC7B,IAAIiB,KAAK,CAACjB,MAAM,CAAC,EAAE;MACf,OAAO,KAAK;IAChB;IACA,IAAIA,MAAM,GAAG,CAAC,EAAE;MACZ;MACA,OAAO,IAAI;IACf,CAAC,MAAM,IAAIA,MAAM,GAAG,EAAE,EAAE;MACpB;MACA,IAAI,CAAC,IAAIA,MAAM,IAAIA,MAAM,IAAI,CAAC,EAAE;QAC5B,OAAO,IAAI;MACf;MACA,OAAO,KAAK;IAChB,CAAC,MAAM,IAAIA,MAAM,GAAG,GAAG,EAAE;MACrB;MACA,IAAIkB,SAAS,GAAGlB,MAAM,GAAG,EAAE;QACvBmB,UAAU,GAAGnB,MAAM,GAAG,EAAE;MAC5B,IAAIkB,SAAS,KAAK,CAAC,EAAE;QACjB,OAAOJ,2BAA2B,CAACK,UAAU,CAAC;MAClD;MACA,OAAOL,2BAA2B,CAACI,SAAS,CAAC;IACjD,CAAC,MAAM,IAAIlB,MAAM,GAAG,KAAK,EAAE;MACvB;MACA,OAAOA,MAAM,IAAI,EAAE,EAAE;QACjBA,MAAM,GAAGA,MAAM,GAAG,EAAE;MACxB;MACA,OAAOc,2BAA2B,CAACd,MAAM,CAAC;IAC9C,CAAC,MAAM;MACH;MACAA,MAAM,GAAGA,MAAM,GAAG,IAAI;MACtB,OAAOc,2BAA2B,CAACd,MAAM,CAAC;IAC9C;EACJ;EAEA,IAAIoB,EAAE,GAAGtB,MAAM,CAACuB,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,sFAAsF,CAACC,KAAK,CAChG,GACJ,CAAC;IACDC,WAAW,EACP,8DAA8D,CAACD,KAAK,CAChE,GACJ,CAAC;IACLE,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EACJ,kEAAkE,CAACH,KAAK,CACpE,GACJ,CAAC;IACLI,aAAa,EAAE,6BAA6B,CAACJ,KAAK,CAAC,GAAG,CAAC;IACvDK,WAAW,EAAE,sBAAsB,CAACL,KAAK,CAAC,GAAG,CAAC;IAC9CM,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,gBAAgB;MACrBC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,cAAc;MAClBC,GAAG,EAAE,0BAA0B;MAC/BC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE,GAAG;MACbC,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE,cAAc;MACxBC,OAAO,EAAE,kBAAkB;MAC3BC,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB;QACA,QAAQ,IAAI,CAACC,GAAG,CAAC,CAAC;UACd,KAAK,CAAC;UACN,KAAK,CAAC;YACF,OAAO,yBAAyB;UACpC;YACI,OAAO,wBAAwB;QACvC;MACJ;IACJ,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAEpC,iBAAiB;MACzBqC,IAAI,EAAEhC,eAAe;MACrBiC,CAAC,EAAE,iBAAiB;MACpBC,EAAE,EAAE,aAAa;MACjB5C,CAAC,EAAEN,mBAAmB;MACtBmD,EAAE,EAAE,aAAa;MACjB5C,CAAC,EAAEP,mBAAmB;MACtBoD,EAAE,EAAE,YAAY;MAChB5C,CAAC,EAAER,mBAAmB;MACtBqD,EAAE,EAAE,SAAS;MACb5C,CAAC,EAAET,mBAAmB;MACtBsD,EAAE,EAAE,UAAU;MACd5C,CAAC,EAAEV,mBAAmB;MACtBuD,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,WAAW;IACnCC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOvC,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}