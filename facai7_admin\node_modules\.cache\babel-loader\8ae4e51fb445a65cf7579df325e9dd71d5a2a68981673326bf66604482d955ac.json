{"ast": null, "code": "import { ref, inject, computed, nextTick } from 'vue';\nimport { getValidDateOfYear, getValidDateOfMonth } from '../utils.mjs';\nimport { PICKER_BASE_INJECTION_KEY } from '../../../time-picker/src/constants.mjs';\nimport { useLocale } from '../../../../hooks/use-locale/index.mjs';\nconst usePanelDateRange = (props, emit, leftDate, rightDate) => {\n  const leftCurrentView = ref(\"date\");\n  const leftCurrentViewRef = ref();\n  const rightCurrentView = ref(\"date\");\n  const rightCurrentViewRef = ref();\n  const pickerBase = inject(PICKER_BASE_INJECTION_KEY);\n  const {\n    disabledDate\n  } = pickerBase.props;\n  const {\n    t,\n    lang\n  } = useLocale();\n  const leftYear = computed(() => {\n    return leftDate.value.year();\n  });\n  const leftMonth = computed(() => {\n    return leftDate.value.month();\n  });\n  const rightYear = computed(() => {\n    return rightDate.value.year();\n  });\n  const rightMonth = computed(() => {\n    return rightDate.value.month();\n  });\n  function computedYearLabel(currentView, yearValue) {\n    const yearTranslation = t(\"el.datepicker.year\");\n    if (currentView.value === \"year\") {\n      const startYear = Math.floor(yearValue.value / 10) * 10;\n      return yearTranslation ? `${startYear} ${yearTranslation} - ${startYear + 9} ${yearTranslation}` : `${startYear} - ${startYear + 9}`;\n    }\n    return `${yearValue.value} ${yearTranslation}`;\n  }\n  function focusPicker(currentViewRef) {\n    currentViewRef == null ? void 0 : currentViewRef.focus();\n  }\n  async function showPicker(pickerType, view) {\n    const currentView = pickerType === \"left\" ? leftCurrentView : rightCurrentView;\n    const currentViewRef = pickerType === \"left\" ? leftCurrentViewRef : rightCurrentViewRef;\n    currentView.value = view;\n    await nextTick();\n    focusPicker(currentViewRef.value);\n  }\n  async function handlePick(mode, pickerType, value) {\n    const isLeftPicker = pickerType === \"left\";\n    const startDate = isLeftPicker ? leftDate : rightDate;\n    const endDate = isLeftPicker ? rightDate : leftDate;\n    const currentView = isLeftPicker ? leftCurrentView : rightCurrentView;\n    const currentViewRef = isLeftPicker ? leftCurrentViewRef : rightCurrentViewRef;\n    if (mode === \"year\") {\n      const data = startDate.value.year(value);\n      startDate.value = getValidDateOfYear(data, lang.value, disabledDate);\n    }\n    if (mode === \"month\") {\n      startDate.value = getValidDateOfMonth(startDate.value, startDate.value.year(), value, lang.value, disabledDate);\n    }\n    if (!props.unlinkPanels) {\n      endDate.value = pickerType === \"left\" ? startDate.value.add(1, \"month\") : startDate.value.subtract(1, \"month\");\n    }\n    currentView.value = mode === \"year\" ? \"month\" : \"date\";\n    await nextTick();\n    focusPicker(currentViewRef.value);\n    handlePanelChange(mode);\n  }\n  function handlePanelChange(mode) {\n    emit(\"panel-change\", [leftDate.value.toDate(), rightDate.value.toDate()], mode);\n  }\n  function adjustDateByView(currentView, date, forward) {\n    const action = forward ? \"add\" : \"subtract\";\n    return currentView === \"year\" ? date[action](10, \"year\") : date[action](1, \"year\");\n  }\n  return {\n    leftCurrentView,\n    rightCurrentView,\n    leftCurrentViewRef,\n    rightCurrentViewRef,\n    leftYear,\n    rightYear,\n    leftMonth,\n    rightMonth,\n    leftYearLabel: computed(() => computedYearLabel(leftCurrentView, leftYear)),\n    rightYearLabel: computed(() => computedYearLabel(rightCurrentView, rightYear)),\n    showLeftPicker: view => showPicker(\"left\", view),\n    showRightPicker: view => showPicker(\"right\", view),\n    handleLeftYearPick: year => handlePick(\"year\", \"left\", year),\n    handleRightYearPick: year => handlePick(\"year\", \"right\", year),\n    handleLeftMonthPick: month => handlePick(\"month\", \"left\", month),\n    handleRightMonthPick: month => handlePick(\"month\", \"right\", month),\n    handlePanelChange,\n    adjustDateByView\n  };\n};\nexport { usePanelDateRange };", "map": {"version": 3, "names": ["usePanelDateRange", "props", "emit", "leftDate", "rightDate", "leftCurrentView", "ref", "leftCurrentViewRef", "rightCurrent<PERSON>iew", "rightCurrentViewRef", "pickerBase", "inject", "PICKER_BASE_INJECTION_KEY", "disabledDate", "t", "lang", "useLocale", "leftYear", "computed", "value", "year", "leftMonth", "month", "rightYear", "rightMonth", "computed<PERSON><PERSON><PERSON><PERSON><PERSON>", "current<PERSON>iew", "yearValue", "yearTranslation", "startYear", "Math", "floor", "focusPicker", "currentViewRef", "focus", "showPicker", "pickerType", "view", "nextTick", "handlePick", "mode", "isLeftPicker", "startDate", "endDate", "data", "getValidDateOfYear", "getValidDateOfMonth", "unlinkPanels", "add", "subtract", "handlePanelChange", "toDate", "adjustDateByView", "date", "forward", "action", "leftYearLabel", "right<PERSON><PERSON><PERSON><PERSON><PERSON>", "showLeftPicker", "showRightPicker", "handleLeftYearPick", "handleRightYearPick", "handleLeftMonthPick", "handleRightMonthPick"], "sources": ["../../../../../../../packages/components/date-picker/src/composables/use-panel-date-range.ts"], "sourcesContent": ["import { computed, inject, nextTick, ref } from 'vue'\nimport { useLocale } from '@element-plus/hooks'\nimport { PICKER_BASE_INJECTION_KEY } from '@element-plus/components/time-picker'\nimport { getValidDateOfMonth, getValidDateOfYear } from '../utils'\n\nimport type { PanelDateRangeProps } from '../props/panel-date-range'\nimport type { Dayjs } from 'dayjs'\nimport type { ComputedRef, Ref } from 'vue'\n\ntype CurrentView = 'date' | 'year' | 'month'\ntype CurrentViewRef = { focus: () => void }\n\nexport type Emits = (\n  event: 'pick' | 'set-picker-option' | 'calendar-change' | 'panel-change',\n  ...args: any[]\n) => void\n\nexport const usePanelDateRange = (\n  props: PanelDateRangeProps,\n  emit: Emits,\n  leftDate: Ref<Dayjs>,\n  rightDate: Ref<Dayjs>\n) => {\n  const leftCurrentView = ref<CurrentView>('date')\n  const leftCurrentViewRef = ref<CurrentViewRef>()\n  const rightCurrentView = ref<CurrentView>('date')\n  const rightCurrentViewRef = ref<CurrentViewRef>()\n  const pickerBase = inject(PICKER_BASE_INJECTION_KEY) as any\n  const { disabledDate } = pickerBase.props\n  const { t, lang } = useLocale()\n\n  const leftYear = computed(() => {\n    return leftDate.value.year()\n  })\n  const leftMonth = computed(() => {\n    return leftDate.value.month()\n  })\n\n  const rightYear = computed(() => {\n    return rightDate.value.year()\n  })\n  const rightMonth = computed(() => {\n    return rightDate.value.month()\n  })\n\n  function computedYearLabel(\n    currentView: Ref<CurrentView>,\n    yearValue: ComputedRef<number>\n  ) {\n    const yearTranslation = t('el.datepicker.year')\n    if (currentView.value === 'year') {\n      const startYear = Math.floor(yearValue.value! / 10) * 10\n      return yearTranslation\n        ? `${startYear} ${yearTranslation} - ${\n            startYear + 9\n          } ${yearTranslation}`\n        : `${startYear} - ${startYear + 9}`\n    }\n    return `${yearValue.value} ${yearTranslation}`\n  }\n\n  function focusPicker(currentViewRef?: CurrentViewRef) {\n    currentViewRef?.focus()\n  }\n\n  async function showPicker(\n    pickerType: 'left' | 'right',\n    view: 'month' | 'year'\n  ) {\n    const currentView =\n      pickerType === 'left' ? leftCurrentView : rightCurrentView\n    const currentViewRef =\n      pickerType === 'left' ? leftCurrentViewRef : rightCurrentViewRef\n    currentView.value = view\n    await nextTick()\n    focusPicker(currentViewRef.value)\n  }\n\n  async function handlePick(\n    mode: 'month' | 'year',\n    pickerType: 'left' | 'right',\n    value: number\n  ) {\n    const isLeftPicker = pickerType === 'left'\n    const startDate = isLeftPicker ? leftDate : rightDate\n    const endDate = isLeftPicker ? rightDate : leftDate\n    const currentView = isLeftPicker ? leftCurrentView : rightCurrentView\n    const currentViewRef = isLeftPicker\n      ? leftCurrentViewRef\n      : rightCurrentViewRef\n\n    if (mode === 'year') {\n      const data = startDate.value.year(value)\n      startDate.value = getValidDateOfYear(data, lang.value, disabledDate)\n    }\n\n    if (mode === 'month') {\n      startDate.value = getValidDateOfMonth(\n        startDate.value,\n        startDate.value.year(),\n        value,\n        lang.value,\n        disabledDate\n      )\n    }\n\n    if (!props.unlinkPanels) {\n      endDate.value =\n        pickerType === 'left'\n          ? startDate.value.add(1, 'month')\n          : startDate.value.subtract(1, 'month')\n    }\n\n    currentView.value = mode === 'year' ? 'month' : 'date'\n    await nextTick()\n    focusPicker(currentViewRef.value)\n    handlePanelChange(mode)\n  }\n\n  function handlePanelChange(mode: 'month' | 'year') {\n    emit(\n      'panel-change',\n      [leftDate.value.toDate(), rightDate.value.toDate()],\n      mode\n    )\n  }\n\n  function adjustDateByView(\n    currentView: CurrentView,\n    date: Dayjs,\n    forward: boolean\n  ) {\n    const action = forward ? 'add' : 'subtract'\n    return currentView === 'year'\n      ? date[action](10, 'year')\n      : date[action](1, 'year')\n  }\n\n  return {\n    leftCurrentView,\n    rightCurrentView,\n    leftCurrentViewRef,\n    rightCurrentViewRef,\n    leftYear,\n    rightYear,\n    leftMonth,\n    rightMonth,\n    leftYearLabel: computed(() => computedYearLabel(leftCurrentView, leftYear)),\n    rightYearLabel: computed(() =>\n      computedYearLabel(rightCurrentView, rightYear)\n    ),\n    showLeftPicker: (view: 'month' | 'year') => showPicker('left', view),\n    showRightPicker: (view: 'month' | 'year') => showPicker('right', view),\n    handleLeftYearPick: (year: number) => handlePick('year', 'left', year),\n    handleRightYearPick: (year: number) => handlePick('year', 'right', year),\n    handleLeftMonthPick: (month: number) => handlePick('month', 'left', month),\n    handleRightMonthPick: (month: number) =>\n      handlePick('month', 'right', month),\n    handlePanelChange,\n    adjustDateByView,\n  }\n}\n"], "mappings": ";;;;AAIY,MAACA,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,SAAS,KAAK;EACrE,MAAMC,eAAe,GAAGC,GAAG,CAAC,MAAM,CAAC;EACnC,MAAMC,kBAAkB,GAAGD,GAAG,EAAE;EAChC,MAAME,gBAAgB,GAAGF,GAAG,CAAC,MAAM,CAAC;EACpC,MAAMG,mBAAmB,GAAGH,GAAG,EAAE;EACjC,MAAMI,UAAU,GAAGC,MAAM,CAACC,yBAAyB,CAAC;EACpD,MAAM;IAAEC;EAAY,CAAE,GAAGH,UAAU,CAACT,KAAK;EACzC,MAAM;IAAEa,CAAC;IAAEC;EAAI,CAAE,GAAGC,SAAS,EAAE;EAC/B,MAAMC,QAAQ,GAAGC,QAAQ,CAAC,MAAM;IAC9B,OAAOf,QAAQ,CAACgB,KAAK,CAACC,IAAI,EAAE;EAChC,CAAG,CAAC;EACF,MAAMC,SAAS,GAAGH,QAAQ,CAAC,MAAM;IAC/B,OAAOf,QAAQ,CAACgB,KAAK,CAACG,KAAK,EAAE;EACjC,CAAG,CAAC;EACF,MAAMC,SAAS,GAAGL,QAAQ,CAAC,MAAM;IAC/B,OAAOd,SAAS,CAACe,KAAK,CAACC,IAAI,EAAE;EACjC,CAAG,CAAC;EACF,MAAMI,UAAU,GAAGN,QAAQ,CAAC,MAAM;IAChC,OAAOd,SAAS,CAACe,KAAK,CAACG,KAAK,EAAE;EAClC,CAAG,CAAC;EACF,SAASG,iBAAiBA,CAACC,WAAW,EAAEC,SAAS,EAAE;IACjD,MAAMC,eAAe,GAAGd,CAAC,CAAC,oBAAoB,CAAC;IAC/C,IAAIY,WAAW,CAACP,KAAK,KAAK,MAAM,EAAE;MAChC,MAAMU,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACJ,SAAS,CAACR,KAAK,GAAG,EAAE,CAAC,GAAG,EAAE;MACvD,OAAOS,eAAe,GAAG,GAAGC,SAAS,IAAID,eAAe,MAAMC,SAAS,GAAG,CAAC,IAAID,eAAe,EAAE,GAAG,GAAGC,SAAS,MAAMA,SAAS,GAAG,CAAC,EAAE;IAC1I;IACI,OAAO,GAAGF,SAAS,CAACR,KAAK,IAAIS,eAAe,EAAE;EAClD;EACE,SAASI,WAAWA,CAACC,cAAc,EAAE;IACnCA,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACC,KAAK,EAAE;EAC5D;EACE,eAAeC,UAAUA,CAACC,UAAU,EAAEC,IAAI,EAAE;IAC1C,MAAMX,WAAW,GAAGU,UAAU,KAAK,MAAM,GAAG/B,eAAe,GAAGG,gBAAgB;IAC9E,MAAMyB,cAAc,GAAGG,UAAU,KAAK,MAAM,GAAG7B,kBAAkB,GAAGE,mBAAmB;IACvFiB,WAAW,CAACP,KAAK,GAAGkB,IAAI;IACxB,MAAMC,QAAQ,EAAE;IAChBN,WAAW,CAACC,cAAc,CAACd,KAAK,CAAC;EACrC;EACE,eAAeoB,UAAUA,CAACC,IAAI,EAAEJ,UAAU,EAAEjB,KAAK,EAAE;IACjD,MAAMsB,YAAY,GAAGL,UAAU,KAAK,MAAM;IAC1C,MAAMM,SAAS,GAAGD,YAAY,GAAGtC,QAAQ,GAAGC,SAAS;IACrD,MAAMuC,OAAO,GAAGF,YAAY,GAAGrC,SAAS,GAAGD,QAAQ;IACnD,MAAMuB,WAAW,GAAGe,YAAY,GAAGpC,eAAe,GAAGG,gBAAgB;IACrE,MAAMyB,cAAc,GAAGQ,YAAY,GAAGlC,kBAAkB,GAAGE,mBAAmB;IAC9E,IAAI+B,IAAI,KAAK,MAAM,EAAE;MACnB,MAAMI,IAAI,GAAGF,SAAS,CAACvB,KAAK,CAACC,IAAI,CAACD,KAAK,CAAC;MACxCuB,SAAS,CAACvB,KAAK,GAAG0B,kBAAkB,CAACD,IAAI,EAAE7B,IAAI,CAACI,KAAK,EAAEN,YAAY,CAAC;IAC1E;IACI,IAAI2B,IAAI,KAAK,OAAO,EAAE;MACpBE,SAAS,CAACvB,KAAK,GAAG2B,mBAAmB,CAACJ,SAAS,CAACvB,KAAK,EAAEuB,SAAS,CAACvB,KAAK,CAACC,IAAI,EAAE,EAAED,KAAK,EAAEJ,IAAI,CAACI,KAAK,EAAEN,YAAY,CAAC;IACrH;IACI,IAAI,CAACZ,KAAK,CAAC8C,YAAY,EAAE;MACvBJ,OAAO,CAACxB,KAAK,GAAGiB,UAAU,KAAK,MAAM,GAAGM,SAAS,CAACvB,KAAK,CAAC6B,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,GAAGN,SAAS,CAACvB,KAAK,CAAC8B,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC;IACpH;IACIvB,WAAW,CAACP,KAAK,GAAGqB,IAAI,KAAK,MAAM,GAAG,OAAO,GAAG,MAAM;IACtD,MAAMF,QAAQ,EAAE;IAChBN,WAAW,CAACC,cAAc,CAACd,KAAK,CAAC;IACjC+B,iBAAiB,CAACV,IAAI,CAAC;EAC3B;EACE,SAASU,iBAAiBA,CAACV,IAAI,EAAE;IAC/BtC,IAAI,CAAC,cAAc,EAAE,CAACC,QAAQ,CAACgB,KAAK,CAACgC,MAAM,EAAE,EAAE/C,SAAS,CAACe,KAAK,CAACgC,MAAM,EAAE,CAAC,EAAEX,IAAI,CAAC;EACnF;EACE,SAASY,gBAAgBA,CAAC1B,WAAW,EAAE2B,IAAI,EAAEC,OAAO,EAAE;IACpD,MAAMC,MAAM,GAAGD,OAAO,GAAG,KAAK,GAAG,UAAU;IAC3C,OAAO5B,WAAW,KAAK,MAAM,GAAG2B,IAAI,CAACE,MAAM,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC,GAAGF,IAAI,CAACE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC;EACtF;EACE,OAAO;IACLlD,eAAe;IACfG,gBAAgB;IAChBD,kBAAkB;IAClBE,mBAAmB;IACnBQ,QAAQ;IACRM,SAAS;IACTF,SAAS;IACTG,UAAU;IACVgC,aAAa,EAAEtC,QAAQ,CAAC,MAAMO,iBAAiB,CAACpB,eAAe,EAAEY,QAAQ,CAAC,CAAC;IAC3EwC,cAAc,EAAEvC,QAAQ,CAAC,MAAMO,iBAAiB,CAACjB,gBAAgB,EAAEe,SAAS,CAAC,CAAC;IAC9EmC,cAAc,EAAGrB,IAAI,IAAKF,UAAU,CAAC,MAAM,EAAEE,IAAI,CAAC;IAClDsB,eAAe,EAAGtB,IAAI,IAAKF,UAAU,CAAC,OAAO,EAAEE,IAAI,CAAC;IACpDuB,kBAAkB,EAAGxC,IAAI,IAAKmB,UAAU,CAAC,MAAM,EAAE,MAAM,EAAEnB,IAAI,CAAC;IAC9DyC,mBAAmB,EAAGzC,IAAI,IAAKmB,UAAU,CAAC,MAAM,EAAE,OAAO,EAAEnB,IAAI,CAAC;IAChE0C,mBAAmB,EAAGxC,KAAK,IAAKiB,UAAU,CAAC,OAAO,EAAE,MAAM,EAAEjB,KAAK,CAAC;IAClEyC,oBAAoB,EAAGzC,KAAK,IAAKiB,UAAU,CAAC,OAAO,EAAE,OAAO,EAAEjB,KAAK,CAAC;IACpE4B,iBAAiB;IACjBE;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}